@page "/student"
@using Compass.Common.Controls.Generic
@using Compass.Common.Data
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject UserAccessor UserAccessor

<h1 class="page-title">@currentStudentName</h1>

<ul class="nav-tabs-wrapper">
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == SUMMARY_INDEX ? "active" : "")" @onclick="() => ChangeTab(SUMMARY_INDEX)">Summary</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == EDIT_INDEX ? "active" : "")" @onclick="() => ChangeTab(EDIT_INDEX)">Edit</button>
    </li>
    @if (hasC4LAccess)
    {
        <DropMenu Title="C4L"
                  MenuItemList="@C4LMenuItemList"
                  DropMenuSelectionResult="OnC4LSelectionResult" />
    }
</ul>

@if (currentTabComponent is not null)
{
    <div class="component-content-wrapper student-component-wrapper">
        <CascadingValue Value="this">
            <DynamicComponent Type="currentTabComponent" />
        </CascadingValue>
    </div>
}
