/* Assessment Summary Page Styles */

.assessment-summary-page {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Selection Bar */
.selection-bar {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.selection-bar > * {
    margin-right: 15px;
}

.assessment-type-selector {
    display: flex;
    align-items: center;
}

.assessment-type-selector span {
    margin-right: 10px;
    font-weight: 500;
}

/* Page Header */
.page-header {
    font-size: 24px;
    font-weight: 600;
    margin: 15px 0;
    padding: 0 15px;
}

/* Show/Hide Button */
.show-hide-all {
    align-self: flex-start;
    margin: 0 15px 15px;
}

/* Legend */
.legend {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}

.legend > * {
    margin-right: 15px;
}

.summary-box-small {
    width: 20px;
    height: 20px;
    display: inline-block;
    border: 1px solid #ccc;
}

.summary-box-completed {
    background-color: #4CAF50;
}

.summary-box-started {
    background-color: #FFC107;
}

.summary-box-invalid {
    background-color: #F44336;
}

.summary-low-z-score {
    background-color: #9C27B0;
}

.summary-box-empty {
    background-color: #FFFFFF;
}

/* Table Headers */
.table-columns {
    display: flex;
    padding: 10px 15px;
    background-color: #f0f0f0;
    font-weight: 600;
    border-bottom: 1px solid #ddd;
}

.asmt-sum-name-column {
    width: 200px;
}

.asmt-sum-dob-column {
    width: 120px;
}

.asmt-sum-chkpt-column {
    width: 120px;
}

.asmt-sum-lap-header-container {
    display: flex;
    flex-grow: 1;
}

.asmt-sum-header {
    display: flex;
    flex-direction: column;
    margin-right: 20px;
}

.asmt-sum-header-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.asmt-sum-header-subscale {
    font-size: 0.9em;
    margin-bottom: 3px;
}

/* Scroller */
.scroller {
    flex-grow: 1;
    overflow-y: auto;
    overflow-x: auto;
}

/* Summary Rows */
.summary-row-container {
    display: flex;
    flex-direction: column;
}

.asmt-summary-row {
    display: flex;
    padding: 10px 15px;
    border-bottom: 1px solid #eee;
}

.asmt-summary-row > div {
    display: flex;
    width: 100%;
}

.summary-row-odd {
    background-color: #f9f9f9;
}

.summary-row-even {
    background-color: #ffffff;
}

.expand-button {
    width: 24px;
    height: 24px;
    background-image: url('/images/expand.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border: none;
    background-color: transparent;
    cursor: pointer;
    margin-right: 10px;
}

.asmt-sum-row-name {
    width: 200px;
    font-weight: 500;
}

.asmt-sum-row-dob {
    width: 120px;
}

.asmt-sum-row-type {
    width: 120px;
}

.asmt-sum-row-instrument-container {
    display: flex;
    flex-grow: 1;
}

/* Assessment Boxes */
.asmt-sum-row-instrument {
    display: flex;
    flex-direction: column;
    margin-right: 20px;
}

.date {
    font-size: 0.9em;
    margin-bottom: 5px;
    text-align: center;
}

.domain {
    display: flex;
}

.summary-box {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
    margin-right: 5px;
    cursor: pointer;
    font-weight: 600;
}

/* Delete Checkbox */
.delete {
    display: flex;
    justify-content: center;
    margin-bottom: 5px;
}

.delete-checkbox {
    width: 20px;
    height: 20px;
}

/* Screen Assessment */
.screen-assessment-container {
    display: flex;
    flex-direction: column;
    margin-right: 20px;
}

.screen-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.hide-instrument {
    width: 16px;
    height: 16px;
    background-image: url('/images/hide.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    border: none;
    background-color: transparent;
    cursor: pointer;
    margin-left: 5px;
}

/* Loading Indicator */
.loading-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

/* No Data Message */
.no-data-message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 18px;
    color: #666;
}

/* Expanded Rows */
.expanded-rows {
    width: 100%;
    margin-top: 10px;
    padding-left: 34px;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
    .summary-box {
        width: 35px;
        height: 35px;
        font-size: 0.9em;
    }
}

@media (max-width: 992px) {
    .selection-bar {
        flex-wrap: wrap;
    }
    
    .selection-bar > * {
        margin-bottom: 10px;
    }
    
    .asmt-sum-name-column {
        width: 150px;
    }
    
    .asmt-sum-row-name {
        width: 150px;
    }
}

@media (max-width: 768px) {
    .summary-box {
        width: 30px;
        height: 30px;
        font-size: 0.8em;
    }
    
    .asmt-sum-name-column {
        width: 120px;
    }
    
    .asmt-sum-row-name {
        width: 120px;
    }
    
    .asmt-sum-dob-column,
    .asmt-sum-row-dob {
        width: 100px;
    }
    
    .asmt-sum-chkpt-column,
    .asmt-sum-row-type {
        width: 100px;
    }
}
