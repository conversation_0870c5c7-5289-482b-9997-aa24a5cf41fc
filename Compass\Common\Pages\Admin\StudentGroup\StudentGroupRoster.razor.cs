﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Student;
using Compass.Common.Helpers;
using Compass.Common.Pages.Admin.Student;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Admin.StudentGroup
{
    public partial class StudentGroupRoster : IDisposable
    {
        [Inject]
        public required CommonSessionDataObserver CommonSessionDataObserver { get; set; }

        private bool showRoster = true;
        private Type? currentStudentComponent;

        private List<StudentDisplayDto> studentResults = new();

        private int maxPages;
        private int currentPage;

        private long? currentOrganizationId;
        private long? currentStudentGroupId;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;
        private string studentGroupHierarchyName = string.Empty;

        private long? selectedStudentId;

        private bool noSearchResults = false;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected Dictionary<string, object> GetDynamicParameters()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();

            if (currentStudentComponent != null)
            {
                // Add parameters conditionally
                if (currentStudentComponent == typeof(StudentAddComponent))
                {
                    parameters[nameof(StudentAddComponent.OnReturn)] = EventCallback.Factory.Create(this, HandleReturn);
                }
                else if (currentStudentComponent == typeof(StudentEditComponent))
                {
                    parameters[nameof(StudentEditComponent.selectedStudentId)] = selectedStudentId;
                }
            }

            return parameters;
        }

        private async Task HandleReturn()
        {
            showRoster = true;
            await GetStudentPage();
        }

        protected override async Task OnInitializedAsync()
        {
            showRoster = true;
            searchText = string.Empty;
            currentSearchText = string.Empty;
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                studentGroupHierarchyName = commonSessionData.StudentGroupHierarchy;
                currentStudentGroupId = commonSessionData.CurrentStudentGroupId;

                currentPage = 1;
                maxPages = 0;

                await GetStudentPage();
            }
        }

        private async Task GetStudentPage()
        {
            StudentGroupRosterListAction action = new StudentGroupRosterListAction();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            action.PageQuery = pageQuery;

            if (currentOrganizationId != null && currentStudentGroupId != null)
            {
                action.OrganizationId = currentOrganizationId;
                action.StudentGroupId = currentStudentGroupId;

                KaplanPageable<StudentDisplayDto> currentPage = await StudentGroupService.GetStudentGroupRoster(action);

                studentResults = currentPage.PageContent;
                maxPages = currentPage.MaxPages;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && studentResults.Count == 0;

                StateHasChanged();
            }
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetStudentPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetStudentPage();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetStudentPage();
        }

        protected void OnAddStudentClick()
        {
            showRoster = false;
            currentStudentComponent = typeof(StudentAddComponent);
        }

        protected async Task OnStudentClick(StudentDisplayDto student)
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                commonSessionData.CurrentStudentId = student.Id;

                string studentName = student.FirstName + " " + student.LastName;
                commonSessionData.SelectedEntityName = studentName;

                if (_currentUser != null)
                {
                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();

                    NavigationManager.NavigateTo($"/student");
                }
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);
            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
