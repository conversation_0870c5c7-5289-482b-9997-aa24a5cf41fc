﻿using Compass.Common.Data;
using Compass.Common.DTOs.Entity2;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Runtime.CompilerServices;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class Entity2Repository : IEntity2Repository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public Entity2Repository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        async Task<Entity2> IEntity2Repository.CreateEntity2Async(Entity2 entity2)
        {
            // Get the authentication state
            var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            entity2.ModId = userId;
            entity2.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.Entities2.AddAsync(entity2);
                await _dbContext.SaveChangesAsync();
            }

            using (ApplicationDbContext _dbUserAccessContext = _contextFactory.CreateDbContext())
            {
                UserEntity2Access entity2UserAccess = new UserEntity2Access();
                entity2UserAccess.OrganizationId = entity2.OrganizationId;
                entity2UserAccess.ModId = userId;
                entity2UserAccess.ModTs = DateTime.Now;
                entity2UserAccess.Entity2Id = entity2.Id;
                entity2UserAccess.CanAdd = "Y";
                entity2UserAccess.CanUpdate = "Y";
                entity2UserAccess.CanDelete = "Y";
                entity2UserAccess.CanView = "Y";
                entity2UserAccess.CanAssign = "Y";

                await _dbUserAccessContext.UserEntity2Accesses.AddAsync(entity2UserAccess);
                await _dbUserAccessContext.SaveChangesAsync();
            }

            return entity2;
        }

        public async Task<List<Entity2>> GetEntities2Async(long? organizationId, long? entity1Id)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Entities2.Where(o => o.OrganizationId == organizationId && o.Entity1Id == entity1Id).ToListAsync();
            }
        }

        async Task<Entity2?> IEntity2Repository.GetEntity2Async(long? id)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Entities2.FirstOrDefaultAsync(o => o.Id == id);
            }
        }

        async Task<Entity2> IEntity2Repository.UpdateEntity2Async(long? id, Entity2 entity2)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                var existingEntity2 = await _dbContext.Entities2.FindAsync(id);

                if (existingEntity2 is null)
                {
                    return null;
                }

                // Get the authentication state
                var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                var user = authState.User;
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                existingEntity2.ModId = userId;
                existingEntity2.ModTs = DateTime.Now;

                existingEntity2.Name = entity2.Name;
                existingEntity2.Address1 = entity2.Address1;
                existingEntity2.Address2 = entity2.Address2;
                existingEntity2.City = entity2.City;
                existingEntity2.State = entity2.State;
                existingEntity2.ZipCode = entity2.ZipCode;
                existingEntity2.Country = entity2.Country;

                existingEntity2.ContactEmail = entity2.ContactEmail;
                existingEntity2.ContactFirstName = entity2.ContactFirstName;
                existingEntity2.ContactLastName = entity2.ContactLastName;
                existingEntity2.ContactFax = entity2.ContactFax;
                existingEntity2.ContactPhone = entity2.ContactPhone;
                existingEntity2.Fax = entity2.Fax;

                _dbContext.Entities2.Update(existingEntity2);
                await _dbContext.SaveChangesAsync();

                return existingEntity2;
            }
        }

        public async Task<List<Entity2ListDisplayDto>> GetEntity2List(Entity2ListAction action)
        {
            List<Entity2ListDisplayDto> results = new List<Entity2ListDisplayDto>();
            PageQuery pageQuery = action.pageQuery;
            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", action.userId),
                new SqlParameter("@OrganizationId", action.organizationId),
                new SqlParameter("@SearchCriteria", pageQuery.QueryText),
                new SqlParameter("@PageOffset", pageQuery.GetOffset()),
                new SqlParameter("@PageSize", pageQuery.PageSize)
            };

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                // Use ADO.NET directly for more control
                using var command = _dbContext.Database.GetDbConnection().CreateCommand();
                command.CommandText = "cmn_accessible_entity2_get_list";
                command.CommandType = CommandType.StoredProcedure;

                foreach (var param in parameters)
                {
                    command.Parameters.Add(param);
                }

                // Ensure connection is open
                if (command.Connection.State != ConnectionState.Open)
                {
                    await command.Connection.OpenAsync();
                }

                try
                {
                    using var reader = await command.ExecuteReaderAsync();

                    // Check if we have any rows
                    if (!reader.HasRows)
                    {
                        return results; // Return empty list
                    }

                    while (await reader.ReadAsync())
                    {
                        try
                        {
                            var entity2 = new Entity2ListDisplayDto
                            {
                                // Use GetOrdinal to find column positions and safely get values
                                Id = reader.GetInt64(reader.GetOrdinal("id")),
                                Name = reader.IsDBNull(reader.GetOrdinal("name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("name")),
                                ContactName = reader.IsDBNull(reader.GetOrdinal("ContactName"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactName")),
                                ContactEmail = reader.IsDBNull(reader.GetOrdinal("ContactEmail"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactEmail")),
                                ContactPhone = reader.IsDBNull(reader.GetOrdinal("ContactPhone"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactPhone")),
                                Entity1Name = reader.IsDBNull(reader.GetOrdinal("Entity1Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity1Name")),
                                Entity1Id = reader.GetInt64(reader.GetOrdinal("Entity1Id"))
                            };

                            results.Add(entity2);
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue processing other rows
                            System.Diagnostics.Debug.WriteLine($"Error processing row: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error executing stored procedure: {ex.Message}");
                    throw; // Rethrow the exception after logging
                }
            }

            return results;
        }

        public async Task<int> GetEntity2Count(long? organizationId, long? entity1Id, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

            SqlParameter[] parameters;
            string sqlQuery;
            if (entity1Id != null)
            {
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OrganizationId", organizationId),
                    new SqlParameter("@Entity1Id", entity1Id),
                    new SqlParameter("@SearchCriteria", queryText)
                };

                sqlQuery = "EXEC [cmn_accessible_entity2_get_count] @UserId, @OrganizationId, @Entity1Id, @SearchCriteria";
            }
            else
            {
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OrganizationId", organizationId),
                    new SqlParameter("@SearchCriteria", queryText)
                };

                sqlQuery = "EXEC [cmn_accessible_entity2_get_count] @UserId, @OrganizationId, null, @SearchCriteria";
            }

            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, parameters);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<int> results = await _dbContext.Database
                                .SqlQuery<int>(formattedQuery)
                                .ToListAsync();

                int count = results.FirstOrDefault();

                return count;
            }
        }

        public async Task<bool> DeleteEntity2(long? id)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                Entity2? existingEntity2 = await _dbContext.Entities2.FindAsync(id);

                if (existingEntity2 is null)
                {
                    throw new Exception("Entity2 not found");
                }

                // Get the authentication state
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                if (userId != null)
                {
                    existingEntity2.ModId = userId;
                    existingEntity2.ModTs = DateTime.Now;

                    existingEntity2.IsDeleted = "Y";

                    _dbContext.Entities2.Update(existingEntity2);
                    await _dbContext.SaveChangesAsync();

                    return true;
                }
                else
                {
                    throw new Exception("UserID not found");
                }
            }
        }
    }
}
