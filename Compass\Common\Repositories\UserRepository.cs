﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.User;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Runtime.CompilerServices;

namespace Compass.Common.Repositories
{
    public class UserRepository : IUserRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public UserRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<KaplanPageable<UserListDisplayDto>> GetUserPage(UserListAction action)
        {
            List<UserListDisplayDto> userList = await GetUserList(action);
            PageQuery pageQuery = action.PageQuery;
            int pageSize = pageQuery.PageSize;

            int userCount = await GetUserCount(action.OrganizationId, pageQuery.QueryText);


            int maxPages = (int)Math.Ceiling((double)userCount / pageSize);

            KaplanPageable<UserListDisplayDto> pageable = new KaplanPageable<UserListDisplayDto>();
            pageable.PageContent = userList;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<List<UserListDisplayDto>> GetUserList(UserListAction action)
        {
            string sqlQuery = "SELECT Id, OrganizationId, UserName, Email, FirstName, LastName " +
                " FROM AspNetUsers WHERE OrganizationId = {0} ";
            string searchClause = "AND " +
                "    ( " +
                "        UserName LIKE {1} " +
                "        OR Email LIKE {1} " +
                "        OR FirstName LIKE {1} " +
                "        OR LastName LIKE {1} " +
                "        OR FirstName + ' ' + LastName LIKE {1} " +
                "    ) ";
            string pageInfo = "ORDER BY id OFFSET {2} ROWS FETCH NEXT {3} ROWS ONLY";

            sqlQuery += searchClause;
            sqlQuery += pageInfo;

            PageQuery pageQuery = action.PageQuery;
            string searchText = "%" + pageQuery.QueryText + "%";
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            long? organizationId = action.OrganizationId;

            if (organizationId is null)
            {
                organizationId = -1;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<UserListDisplayDto> resultList = await _dbContext.UserListDisplayDtos
                    .FromSqlRaw(sqlQuery, organizationId, searchText, pageOffset, pageSize)
                    .Select(o => new UserListDisplayDto
                    {
                        Id = o.Id,
                        OrganizationId = o.OrganizationId,
                        UserName = o.UserName,
                        Email = o.Email,
                        FirstName = o.FirstName,
                        LastName = o.LastName
                    })
                    .ToListAsync();

                return resultList;
            }
        }

        public async Task<List<UserListDisplayDto>> GetOrganizationUserList(UserListAction action)
        {
            string sqlQuery = @"SELECT DISTINCT u.Id, u.OrganizationId, u.UserName, u.Email, u.FirstName, u.LastName ";

            string fromClause = GetOrganizationUserFromSql();

            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {1}
                                        OR u.Email LIKE {1}
                                        OR u.FirstName LIKE {1}
                                        OR u.LastName LIKE {1}
                                        OR u.FirstName + ' ' + u.LastName LIKE {1}
                                    ) ";

            string pageInfo = "ORDER BY id OFFSET {2} ROWS FETCH NEXT {3} ROWS ONLY";

            sqlQuery += fromClause;
            sqlQuery += searchClause;
            sqlQuery += pageInfo;

            PageQuery pageQuery = action.PageQuery;
            string searchText = "%" + pageQuery.QueryText + "%";
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            long? organizationId = action.OrganizationId;

            if (organizationId is null)
            {
                organizationId = -1;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<UserListDisplayDto> resultList = await _dbContext.UserListDisplayDtos
                    .FromSqlRaw(sqlQuery, organizationId, searchText, pageOffset, pageSize)
                    .Select(o => new UserListDisplayDto
                    {
                        Id = o.Id,
                        OrganizationId = o.OrganizationId,
                        UserName = o.UserName,
                        Email = o.Email,
                        FirstName = o.FirstName,
                        LastName = o.LastName
                    })
                    .ToListAsync();

                return resultList;
            }

        }

        public async Task<List<UserListDisplayDto>> GetEntity1UserList(UserListAction action)
        {
            string sqlQuery = @"SELECT DISTINCT u.Id, u.OrganizationId, u.UserName, u.Email, u.FirstName, u.LastName ";

            string fromClause = GetEntity1UserFromSql();

            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) ";

            string pageInfo = "ORDER BY id OFFSET {3} ROWS FETCH NEXT {4} ROWS ONLY";

            sqlQuery += fromClause;
            sqlQuery += searchClause;
            sqlQuery += pageInfo;

            PageQuery pageQuery = action.PageQuery;
            string searchText = "%" + pageQuery.QueryText + "%";
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            long? organizationId = action.OrganizationId;
            long? entity1Id = action.EntityId;

            if (organizationId is null)
            {
                organizationId = -1;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<UserListDisplayDto> resultList = await _dbContext.UserListDisplayDtos
                    .FromSqlRaw(sqlQuery, organizationId, entity1Id, searchText, pageOffset, pageSize)
                    .Select(o => new UserListDisplayDto
                    {
                        Id = o.Id,
                        OrganizationId = o.OrganizationId,
                        UserName = o.UserName,
                        Email = o.Email,
                        FirstName = o.FirstName,
                        LastName = o.LastName
                    })
                    .ToListAsync();

                return resultList;
            }
        }

        public async Task<List<UserListDisplayDto>> GetEntity2UserList(UserListAction action)
        {
            string sqlQuery = @"SELECT DISTINCT u.Id, u.OrganizationId, u.UserName, u.Email, u.FirstName, u.LastName ";

            string fromClause = GetEntity2UserFromSql();

            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) ";

            string pageInfo = "ORDER BY id OFFSET {3} ROWS FETCH NEXT {4} ROWS ONLY";

            sqlQuery += fromClause;
            sqlQuery += searchClause;
            sqlQuery += pageInfo;

            PageQuery pageQuery = action.PageQuery;
            string searchText = "%" + pageQuery.QueryText + "%";
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            long? organizationId = action.OrganizationId;
            long? entity2Id = action.EntityId;

            if (organizationId is null)
            {
                organizationId = -1;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<UserListDisplayDto> resultList = await _dbContext.UserListDisplayDtos
                    .FromSqlRaw(sqlQuery, organizationId, entity2Id, searchText, pageOffset, pageSize)
                    .Select(o => new UserListDisplayDto
                    {
                        Id = o.Id,
                        OrganizationId = o.OrganizationId,
                        UserName = o.UserName,
                        Email = o.Email,
                        FirstName = o.FirstName,
                        LastName = o.LastName
                    })
                    .ToListAsync();

                return resultList;
            }
        }

        public async Task<List<UserListDisplayDto>> GetEntity3UserList(UserListAction action)
        {
            string sqlQuery = @"SELECT DISTINCT u.Id, u.OrganizationId, u.UserName, u.Email, u.FirstName, u.LastName ";

            string fromClause = GetEntity3UserFromSql();

            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) ";

            string pageInfo = "ORDER BY id OFFSET {3} ROWS FETCH NEXT {4} ROWS ONLY";

            sqlQuery += fromClause;
            sqlQuery += searchClause;
            sqlQuery += pageInfo;

            PageQuery pageQuery = action.PageQuery;
            string searchText = "%" + pageQuery.QueryText + "%";
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            long? organizationId = action.OrganizationId;
            long? entity3Id = action.EntityId;

            if (organizationId is null)
            {
                organizationId = -1;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<UserListDisplayDto> resultList = await _dbContext.UserListDisplayDtos
                    .FromSqlRaw(sqlQuery, organizationId, entity3Id, searchText, pageOffset, pageSize)
                    .Select(o => new UserListDisplayDto
                    {
                        Id = o.Id,
                        OrganizationId = o.OrganizationId,
                        UserName = o.UserName,
                        Email = o.Email,
                        FirstName = o.FirstName,
                        LastName = o.LastName
                    })
                    .ToListAsync();

                return resultList;
            }
        }

        public async Task<List<UserListDisplayDto>> GetSiteUserList(UserListAction action)
        {
            string sqlQuery = @"SELECT DISTINCT u.Id, u.OrganizationId, u.UserName, u.Email, u.FirstName, u.LastName ";

            string fromClause = GetSiteUserFromSql();

            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) ";

            string pageInfo = "ORDER BY id OFFSET {3} ROWS FETCH NEXT {4} ROWS ONLY";

            sqlQuery += fromClause;
            sqlQuery += searchClause;
            sqlQuery += pageInfo;

            PageQuery pageQuery = action.PageQuery;
            string searchText = "%" + pageQuery.QueryText + "%";
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            long? organizationId = action.OrganizationId;
            long? siteId = action.EntityId;

            if (organizationId is null)
            {
                organizationId = -1;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<UserListDisplayDto> resultList = await _dbContext.UserListDisplayDtos
                    .FromSqlRaw(sqlQuery, organizationId, siteId, searchText, pageOffset, pageSize)
                    .Select(o => new UserListDisplayDto
                    {
                        Id = o.Id,
                        OrganizationId = o.OrganizationId,
                        UserName = o.UserName,
                        Email = o.Email,
                        FirstName = o.FirstName,
                        LastName = o.LastName
                    })
                    .ToListAsync();

                return resultList;
            }
        }

        public async Task<List<UserListDisplayDto>> GetStudentGroupUserList(UserListAction action)
        {
            string sqlQuery = @"SELECT DISTINCT u.Id, u.OrganizationId, u.UserName, u.Email, u.FirstName, u.LastName ";

            string fromClause = GetStudentGroupUserFromSql();

            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) ";

            string pageInfo = "ORDER BY id OFFSET {3} ROWS FETCH NEXT {4} ROWS ONLY";

            sqlQuery += fromClause;
            sqlQuery += searchClause;
            sqlQuery += pageInfo;

            PageQuery pageQuery = action.PageQuery;
            string searchText = "%" + pageQuery.QueryText + "%";
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            long? organizationId = action.OrganizationId;
            long? studentGroupId = action.EntityId;

            if (organizationId is null)
            {
                organizationId = -1;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<UserListDisplayDto> resultList = await _dbContext.UserListDisplayDtos
                    .FromSqlRaw(sqlQuery, organizationId, studentGroupId, searchText, pageOffset, pageSize)
                    .Select(o => new UserListDisplayDto
                    {
                        Id = o.Id,
                        OrganizationId = o.OrganizationId,
                        UserName = o.UserName,
                        Email = o.Email,
                        FirstName = o.FirstName,
                        LastName = o.LastName
                    })
                    .ToListAsync();

                return resultList;
            }
        }

        private string GetOrganizationUserFromSql()
        {
            string fromClause = @"FROM AspNetUsers AS u
                                INNER JOIN cmn_organizations AS o
                                    ON o.id = u.OrganizationId
                                INNER JOIN cmn_user_organization_accesses AS a
                                    ON a.organization_id = o.id
                                INNER JOIN cmn_user_organization_links AS lnk
                                    ON lnk.aspnetuser_id = u.Id
                                        AND lnk.organization_id = o.id
                                        AND lnk.cmn_user_organization_access_id = a.id
                                WHERE u.OrganizationId = {0} ";

            return fromClause;
        }

        private string GetOrgAvailableAssignSql()
        {
            string sql = @"FROM AspNetUsers AS u2
                            WHERE u2.OrganizationId = {0}
                                AND {0} = {1}
                                AND u2.Id NOT IN
                                (
                                    SELECT u.Id "
                                    + GetOrganizationUserFromSql() +

                                    @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) 
                                ) ";

            return sql;
        }

        private string GetEntity1UserFromSql()
        {
            string fromClause = @"FROM AspNetUsers AS u
                                INNER JOIN cmn_hierarchy_entities_1 AS e
                                    ON e.organization_id = u.OrganizationId
                                INNER JOIN cmn_user_entity_1_accesses AS a
                                    ON a.organization_id = e.organization_id
                                        AND a.entity_1_id = e.id
                                INNER JOIN cmn_user_entity_1_links AS lnk
                                    ON lnk.aspnetuser_id = u.Id
                                        AND lnk.organization_id = e.organization_id
                                        AND lnk.cmn_user_entity_1_access_id = a.id
                                WHERE u.OrganizationId = {0} 
                                    AND e.id = {1} ";

            return fromClause;
        }

        private string GetEntity1AvailableAssignSql()
        {
            string sql = @"FROM AspNetUsers AS u2
                            WHERE u2.OrganizationId = {0}
                                AND u2.Id NOT IN
                                (
                                    SELECT u.Id "
                                    + GetEntity1UserFromSql() +
                                @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) 
                                ) ";

            return sql;
        }

        private string GetEntity2UserFromSql()
        {
            string fromClause = @"FROM AspNetUsers AS u 
                                    INNER JOIN cmn_hierarchy_entities_2 AS e
                                        ON e.organization_id = u.OrganizationId
                                    INNER JOIN cmn_user_entity_2_accesses AS a
                                        ON a.organization_id = e.organization_id
                                            AND a.entity_2_id = e.id
                                    INNER JOIN cmn_user_entity_2_links AS lnk
                                        ON lnk.aspnetuser_id = u.Id
                                            AND lnk.organization_id = e.organization_id
                                            AND lnk.cmn_user_entity_2_access_id = a.id
                                    WHERE u.OrganizationId = {0}
                                        AND e.id = {1} ";
            return fromClause;
        }

        private string GetEntity2AvailableAssignSql()
        {
            string sql = @"FROM AspNetUsers AS u2
                            WHERE u2.OrganizationId = {0}
                                AND u2.Id NOT IN
                                (
                                    SELECT u.Id "
                                    + GetEntity2UserFromSql() +
                                @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) 
                                ) ";

            return sql;
        }

        private string GetEntity3UserFromSql()
        {
            string fromClause = @"FROM AspNetUsers AS u 
                                    INNER JOIN cmn_hierarchy_entities_3 AS e
                                        ON e.organization_id = u.OrganizationId
                                    INNER JOIN cmn_user_entity_3_accesses AS a
                                        ON a.organization_id = e.organization_id
                                            AND a.entity_3_id = e.id
                                    INNER JOIN cmn_user_entity_3_links AS lnk
                                        ON lnk.aspnetuser_id = u.Id
                                            AND lnk.organization_id = e.organization_id
                                            AND lnk.cmn_user_entity_3_access_id = a.id
                                    WHERE u.OrganizationId = {0}
                                        AND e.id = {1} ";
            return fromClause;
        }

        private string GetEntity3AvailableAssignSql()
        {
            string sql = @"FROM AspNetUsers AS u2
                            WHERE u2.OrganizationId = {0}
                                AND u2.Id NOT IN
                                (
                                    SELECT u.Id "
                                    + GetEntity3UserFromSql() +
                                @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) 
                                ) ";

            return sql;
        }

        private string GetSiteUserFromSql()
        {
            string fromClause = @"FROM AspNetUsers AS u 
                                    INNER JOIN cmn_sites AS e
                                        ON e.organization_id = u.OrganizationId
                                    INNER JOIN cmn_user_site_accesses AS a
                                        ON a.organization_id = e.organization_id
                                            AND a.site_id = e.id
                                    INNER JOIN cmn_user_site_links AS lnk
                                        ON lnk.aspnetuser_id = u.Id
                                            AND lnk.organization_id = e.organization_id
                                            AND lnk.cmn_user_site_access_id = a.id
                                    WHERE u.OrganizationId = {0}
                                        AND e.id = {1} ";
            return fromClause;
        }

        private string GetSiteAvailableAssignSql()
        {
            string sql = @"FROM AspNetUsers AS u2
                            WHERE u2.OrganizationId = {0}
                                AND u2.Id NOT IN
                                (
                                    SELECT u.Id "
                                    + GetSiteUserFromSql() +
                                @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) 
                                ) ";

            return sql;
        }

        private string GetStudentGroupUserFromSql()
        {
            string fromClause = @"FROM AspNetUsers AS u 
                                    INNER JOIN cmn_student_groups AS e
                                        ON e.organization_id = u.OrganizationId
                                    INNER JOIN cmn_user_student_group_accesses AS a
                                        ON a.organization_id = e.organization_id
                                            AND a.student_group_id = e.id
                                    INNER JOIN cmn_user_student_group_links AS lnk
                                        ON lnk.aspnetuser_id = u.Id
                                            AND lnk.organization_id = e.organization_id
                                            AND lnk.cmn_user_student_group_access_id = a.id
                                    WHERE u.OrganizationId = {0}
                                        AND e.id = {1} ";
            return fromClause;
        }

        private string GetStudentGroupAvailableAssignSql()
        {
            string sql = @"FROM AspNetUsers AS u2
                            WHERE u2.OrganizationId = {0}
                                AND u2.Id NOT IN
                                (
                                    SELECT u.Id "
                                    + GetStudentGroupUserFromSql() +
                                @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) 
                                ) ";

            return sql;
        }

        private string GetAvailableAssignSqlFromClause(int assignLevel)
        {
            string sql = "";

            switch (assignLevel)
            {
                case (int)IUserService.AssignLevels.OrgLevel:
                    sql = GetOrgAvailableAssignSql();
                    break;
                case (int)IUserService.AssignLevels.Entity1Level:
                    sql = GetEntity1AvailableAssignSql();
                    break;
                case (int)IUserService.AssignLevels.Entity2Level:
                    sql = GetEntity2AvailableAssignSql();
                    break;
                case (int)IUserService.AssignLevels.Entity3Level:
                    sql = GetEntity3AvailableAssignSql();
                    break;
                case (int)IUserService.AssignLevels.SiteLevel:
                    sql = GetSiteAvailableAssignSql();
                    break;
                case (int)IUserService.AssignLevels.StudentGroupLevel:
                    sql = GetStudentGroupAvailableAssignSql();
                    break;
            }
            return sql;
        }

        public async Task<List<UserListDisplayDto>> GetUserInviteList(UserAssignListAction action)
        {
            int inviteLevel = action.AssignLevel;
            string sqlQuery = "SELECT DISTINCT u2.Id, u2.OrganizationId, u2.UserName, u2.Email, u2.FirstName, u2.LastName ";
            string fromClause = GetAvailableAssignSqlFromClause(inviteLevel);

            string searchClause = @"AND 
                                    (
                                        u2.UserName LIKE {2}
                                        OR u2.Email LIKE {2}
                                        OR u2.FirstName LIKE {2}
                                        OR u2.LastName LIKE {2}
                                        OR u2.FirstName + ' ' + u2.LastName LIKE {2}
                                    ) ";

            string pageInfo = "ORDER BY id OFFSET {3} ROWS FETCH NEXT {4} ROWS ONLY";

            sqlQuery += fromClause + searchClause + pageInfo;

            PageQuery pageQuery = action.PageQuery;
            string searchText = "%" + pageQuery.QueryText + "%";
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            long? organizationId = action.OrganizationId;
            long? entityId = action.EntityId;

            if (organizationId is null)
            {
                organizationId = -1;
            }
            if (entityId is null)
            {
                entityId = -1;
            }


            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<UserListDisplayDto> resultList = await _dbContext.UserListDisplayDtos
                    .FromSqlRaw(sqlQuery, organizationId, entityId, searchText, pageOffset, pageSize)
                    .Select(o => new UserListDisplayDto
                    {
                        Id = o.Id,
                        OrganizationId = o.OrganizationId,
                        UserName = o.UserName,
                        Email = o.Email,
                        FirstName = o.FirstName,
                        LastName = o.LastName
                    })
                    .ToListAsync();

                return resultList;
            }
        }

        public async Task<int> GetUserCount(long? organizationId, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            string sqlQuery = "SELECT COUNT(Id) AS Value FROM AspNetUsers WHERE OrganizationId = {0} ";
            string searchClause = @"AND 
                                    (
                                        UserName LIKE {1}
                                        OR Email LIKE {1}
                                        OR FirstName LIKE {1}
                                        OR LastName LIKE {1}
                                        OR FirstName + ' ' + LastName LIKE {1}
                                    ) ";
            sqlQuery += searchClause;

            string searchText = "%" + queryText + "%";
            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, searchText);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();

                return count;
            }
        }

        public async Task<int> GetOrganizationUserCount(long? organizationId, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            string sqlQuery = @"SELECT COUNT(u.Id)  AS Value ";
            string fromClause = GetOrganizationUserFromSql();
            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {1}
                                        OR u.Email LIKE {1}
                                        OR u.FirstName LIKE {1}
                                        OR u.LastName LIKE {1}
                                        OR u.FirstName + ' ' + u.LastName LIKE {1}
                                    ) ";
            sqlQuery += fromClause;
            sqlQuery += searchClause;

            string searchText = "%" + queryText + "%";
            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, searchText);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();

                return count;
            }
        }

        public async Task<int> GetEntity1UserCount(long? organizationId, long? entity1Id, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            if (entity1Id is null)
            {
                entity1Id = -1;
            }

            string sqlQuery = @"SELECT COUNT(u.Id)  AS Value ";
            string fromClause = GetEntity1UserFromSql();
            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) ";
            sqlQuery += fromClause;
            sqlQuery += searchClause;

            string searchText = "%" + queryText + "%";
            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, entity1Id, searchText);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();

                return count;
            }
        }

        public async Task<int> GetEntity2UserCount(long? organizationId, long? entity2Id, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            if (entity2Id is null)
            {
                entity2Id = -1;
            }

            string sqlQuery = @"SELECT COUNT(u.Id)  AS Value ";
            string fromClause = GetEntity2UserFromSql();
            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) ";
            sqlQuery += fromClause;
            sqlQuery += searchClause;

            string searchText = "%" + queryText + "%";
            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, entity2Id, searchText);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();

                return count;
            }
        }

        public async Task<int> GetEntity3UserCount(long? organizationId, long? entity3Id, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            if (entity3Id is null)
            {
                entity3Id = -1;
            }

            string sqlQuery = @"SELECT COUNT(u.Id)  AS Value ";
            string fromClause = GetEntity3UserFromSql();
            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) ";
            sqlQuery += fromClause;
            sqlQuery += searchClause;

            string searchText = "%" + queryText + "%";
            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, entity3Id, searchText);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();

                return count;
            }
        }

        public async Task<int> GetSiteUserCount(long? organizationId, long? siteId, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            if (siteId is null)
            {
                siteId = -1;
            }

            string sqlQuery = @"SELECT COUNT(u.Id)  AS Value ";
            string fromClause = GetSiteUserFromSql();
            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) ";
            sqlQuery += fromClause;
            sqlQuery += searchClause;

            string searchText = "%" + queryText + "%";
            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, siteId, searchText);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();

                return count;
            }
        }

        public async Task<int> GetStudentGroupUserCount(long? organizationId, long? studentGroupId, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            if (studentGroupId is null)
            {
                studentGroupId = -1;
            }

            string sqlQuery = @"SELECT COUNT(u.Id)  AS Value ";
            string fromClause = GetStudentGroupUserFromSql();
            string searchClause = @"AND 
                                    (
                                        u.UserName LIKE {2}
                                        OR u.Email LIKE {2}
                                        OR u.FirstName LIKE {2}
                                        OR u.LastName LIKE {2}
                                        OR u.FirstName + ' ' + u.LastName LIKE {2}
                                    ) ";
            sqlQuery += fromClause;
            sqlQuery += searchClause;

            string searchText = "%" + queryText + "%";
            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, studentGroupId, searchText);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();

                return count;
            }
        }

        public async Task<int> GetUserInviteCount(long? organizationId, long? entityId, int inviteLevel, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }
            if (entityId is null)
            {
                entityId = -1;
            }

            string selectClause = "SELECT COUNT(u2.Id) AS Value ";
            string fromClause = GetAvailableAssignSqlFromClause(inviteLevel);

            string sqlQuery = selectClause + fromClause;

            string searchText = "%" + queryText + "%";
            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, entityId, searchText);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();

                return count;
            }
        }

        private string GetUserAssignmentSql()
        {
            string sqlQuery = @"SELECT u.Id, 'Organization' AS AccessLevel, o.name AS EntityName
                            FROM AspNetUsers AS u
                            INNER JOIN cmn_user_organization_links AS lnk
                                ON lnk.aspnetuser_id = u.Id
                                    AND lnk.organization_id = u.OrganizationId
                            INNER JOIN cmn_user_organization_accesses AS a
                                ON a.id = lnk.cmn_user_organization_access_id
                                    AND a.organization_id = lnk.organization_id
                            INNER JOIN cmn_organizations AS o
                                ON o.id = a.organization_id
                            WHERE u.Id = {0}
                            UNION
                            SELECT u.Id, h.hierarchy_entity_1_entity_name AS AccessLevel, e.name AS EntityName
                            FROM AspNetUsers AS u
                            INNER JOIN cmn_user_entity_1_links AS lnk
                                ON lnk.aspnetuser_id = u.Id
                                    AND lnk.organization_id = u.OrganizationId
                            INNER JOIN cmn_user_entity_1_accesses AS a
                                ON a.id = lnk.cmn_user_entity_1_access_id
                                    AND a.organization_id = lnk.organization_id
                            INNER JOIN cmn_hierarchy_entities_1 AS e
                                ON e.id = a.entity_1_id
                                    AND e.organization_id = a.organization_id
                                    AND e.is_deleted = 'N'
                            INNER JOIN cmn_organization_hierarchies AS h
                                ON h.organization_id = e.organization_id
                            WHERE u.Id = {0}
                            UNION
                            SELECT u.Id, h.hierarchy_entity_2_entity_name AS AccessLevel, e.name AS EntityName
                            FROM AspNetUsers AS u
                            INNER JOIN cmn_user_entity_2_links AS lnk
                                ON lnk.aspnetuser_id = u.Id
                                    AND lnk.organization_id = u.OrganizationId
                            INNER JOIN cmn_user_entity_2_accesses AS a
                                ON a.id = lnk.cmn_user_entity_2_access_id
                                    AND a.organization_id = lnk.organization_id
                            INNER JOIN cmn_hierarchy_entities_2 AS e
                                ON e.id = a.entity_2_id
                                    AND e.organization_id = a.organization_id
                                    AND e.is_deleted = 'N'
                            INNER JOIN cmn_organization_hierarchies AS h
                                ON h.organization_id = e.organization_id
                            WHERE u.Id = {0}
                            UNION
                            SELECT u.Id, h.hierarchy_entity_3_entity_name AS AccessLevel, e.name AS EntityName
                            FROM AspNetUsers AS u
                            INNER JOIN cmn_user_entity_3_links AS lnk
                                ON lnk.aspnetuser_id = u.Id
                                    AND lnk.organization_id = u.OrganizationId
                            INNER JOIN cmn_user_entity_3_accesses AS a
                                ON a.id = lnk.cmn_user_entity_3_access_id
                                    AND a.organization_id = lnk.organization_id
                            INNER JOIN cmn_hierarchy_entities_3 AS e
                                ON e.id = a.entity_3_id
                                    AND e.organization_id = a.organization_id
                                    AND e.is_deleted = 'N'
                            INNER JOIN cmn_organization_hierarchies AS h
                                ON h.organization_id = e.organization_id
                            WHERE u.Id = {0}
                            UNION
                            SELECT u.Id, h.hierarchy_site_entity_name AS AccessLevel, s.name AS EntityName
                            FROM AspNetUsers AS u
                            INNER JOIN cmn_user_site_links AS lnk
                                ON lnk.aspnetuser_id = u.Id
                                    AND lnk.organization_id = u.OrganizationId
                            INNER JOIN cmn_user_site_accesses AS a
                                ON a.id = lnk.cmn_user_site_access_id
                                    AND a.organization_id = lnk.organization_id
                            INNER JOIN cmn_sites AS s
                                ON s.id = a.site_id
                                    AND s.organization_id = a.organization_id
                                    AND s.is_deleted = 'N'
                            INNER JOIN cmn_organization_hierarchies AS h
                                ON h.organization_id = s.organization_id
                            WHERE u.Id = {0}
                            UNION
                            SELECT u.Id, h.hierarchy_student_group_entity_name AS AccessLevel, s.name AS EntityName
                            FROM AspNetUsers AS u
                            INNER JOIN cmn_user_student_group_links AS lnk
                                ON lnk.aspnetuser_id = u.Id
                                    AND lnk.organization_id = u.OrganizationId
                            INNER JOIN cmn_user_student_group_accesses AS a
                                ON a.id = lnk.cmn_user_student_group_access_id
                                    AND a.organization_id = lnk.organization_id
                            INNER JOIN cmn_student_groups AS s
                                ON s.id = a.student_group_id
                                    AND s.organization_id = a.organization_id
                                    AND s.is_deleted = 'N'
                            INNER JOIN cmn_organization_hierarchies AS h
                                ON h.organization_id = s.organization_id
                            WHERE u.Id = {0}";

            return sqlQuery;
        }

        public async Task<List<UserAssignmentDto>> GetUserAssignments(string userId)
        {
            string sqlQuery = GetUserAssignmentSql();

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<UserAssignmentDto> resultList = await _dbContext.UserAssignmentDtos
                .FromSqlRaw(sqlQuery, userId)
                    .Select(o => new UserAssignmentDto
                    {
                        Id = o.Id,
                        AccessLevel = o.AccessLevel,
                        EntityName = o.EntityName
                    })
                    .ToListAsync();

                return resultList;
            }
        }
    }
}
