﻿﻿using Compass.DECA.DTOs;
using Compass.DECA.Interfaces.Repositories;
using Compass.DECA.Interfaces.Services;
using Compass.Deca.Models;
using Serilog;

namespace Compass.DECA.Services
{
    public class DecaStudentRatingService : IDecaStudentRatingService
    {
        private readonly IDecaStudentRatingRepository _repository;

        public DecaStudentRatingService(IDecaStudentRatingRepository repository)
        {
            _repository = repository;
        }

        public async Task<List<StudentGroupRatingDto>> GetCurrentStudentRatingsForStudentGroupAsync(long studentGroupId)
        {
            try
            {
                return await _repository.GetCurrentStudentRatingsForStudentGroupAsync(studentGroupId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting student ratings for student group {StudentGroupId}", studentGroupId);
                throw;
            }
        }

        public async Task<List<StudentGroupRatingDto>> GetStudentRatingsForStudentAsync(long studentId)
        {
            try
            {
                return await _repository.GetStudentRatingsForStudentAsync(studentId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting student ratings for student {StudentId}", studentId);
                throw;
            }
        }

        public async Task<DecaStudentRating?> GetByIdAsync(long id)
        {
            try
            {
                return await _repository.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting student rating {Id}", id);
                throw;
            }
        }

        public async Task<DecaStudentRating> CreateAsync(DecaStudentRating rating)
        {
            try
            {
                return await _repository.CreateAsync(rating);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error creating student rating for student {StudentId}", rating.StudentId);
                throw;
            }
        }

        public async Task<DecaStudentRating?> UpdateAsync(long id, DecaStudentRating rating)
        {
            try
            {
                return await _repository.UpdateAsync(id, rating);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error updating student rating {Id}", id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(long id)
        {
            try
            {
                return await _repository.DeleteAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error deleting student rating {Id}", id);
                throw;
            }
        }
    }
}
