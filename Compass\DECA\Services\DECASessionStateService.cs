using Compass.DECA.Interfaces.Services;
using Compass.DECA.Models;

namespace Compass.DECA.Services
{
    /// <summary>
    /// Service that manages session state for DECA components
    /// </summary>
    public class DECASessionStateService : IDECASessionStateService
    {
        /// <summary>
        /// The current rating ID
        /// </summary>
        public long RatingId { get; set; }

        /// <summary>
        /// The current checkpoint
        /// </summary>
        public int Checkpoint { get; set; }

        /// <summary>
        /// Set session context in a single operation (recommended for better performance)
        /// </summary>
        public Task SetSessionContextAsync(DECASessionContext context)
        {
            RatingId = context.RatingId;
            Checkpoint = context.Checkpoint;
            return Task.CompletedTask;
        }

        /// <summary>
        /// Get session context in a single operation (recommended for better performance)
        /// </summary>
        public Task<DECASessionContext> GetSessionContextAsync()
        {
            DECASessionContext context = new DECASessionContext
            {
                RatingId = RatingId,
                Checkpoint = Checkpoint
            };
            return Task.FromResult(context);
        }
    }
}
