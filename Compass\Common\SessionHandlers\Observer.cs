﻿namespace Compass.Common.SessionHandlers
{
    public class Observer
    {
        // List to hold actions
        private readonly List<Action> _listeners = new List<Action>();
        private readonly List<Func<Task>> _listenersAsync = new List<Func<Task>>();

        public void AddStateChangeListeners(Action? listener)
        {
            if (listener == null)
            {
                throw new ArgumentNullException(nameof(listener), "Action cannot be null.");
            }
            if (!_listeners.Contains(listener))
            {
                _listeners.Add(listener);
            }
        }

        public void RemoveStateChangeListeners(Action? listener)
        {
            if (listener == null)
            {
                throw new ArgumentNullException(nameof(listener), "Action cannot be null.");
            }
            _listeners.Remove(listener);
        }

        public void ClearListeners()
        {
            _listeners.Clear();
        }

        public async Task ClearListenersAsync()
        {
            await Task.Run(() => _listeners.Clear());
        }

        public void BroadcastStateChange()
        {
            foreach (var listener in _listeners)
            {
                listener?.Invoke();
            }
        }
        public void AddStateChangeAsyncListeners(Func<Task> listener)
        {
            if (listener == null)
            {
                throw new ArgumentNullException(nameof(listener), "AsyncAction cannot be null.");
            }
            if (!_listenersAsync.Contains(listener))
            {
                _listenersAsync.Add(listener);
            }
        }

        public void RemoveStateChangeAsyncListeners(Func<Task> listener)
        {
            if (_listenersAsync == null)
            {
                throw new ArgumentNullException(nameof(listener), "Action cannot be null.");
            }
            _listenersAsync.Remove(listener);
        }

        public async Task BroadcastStateChangeAsync()
        {
            foreach (var listener in _listenersAsync)
            {
                await listener.Invoke();
            }

            BroadcastStateChange();
        }
    }
}
