﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.User;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Services;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.StudentGroup
{
    public partial class StudentGroupUsers
    {
        private List<UserListDisplayDto> userResults = new();

        private bool IsAssignBoxVisible = false;
        private bool IsInviteBoxVisible = false;
        private bool IsUnAssignDialogVisible = false;
        private bool isLoading = true;
        private bool noSearchResults = false;

        private int maxPages;
        private int currentPage;

        private long? currentOrganizationId;
        private long? currentEntityId;
        private int assignLevel = (int)IUserService.AssignLevels.StudentGroupLevel;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;
        private string studentGroupHierarchyName = string.Empty;
        private string studentGroupName = string.Empty;

        private string unAssignUserId = string.Empty;
        private string unAssignMessage = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            unAssignUserId = string.Empty;
            searchText = string.Empty;
            currentSearchText = string.Empty;
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                currentEntityId = commonSessionData.CurrentStudentGroupId;
                studentGroupHierarchyName = commonSessionData.StudentGroupHierarchy;
                studentGroupName = commonSessionData.SelectedEntityName;

                currentPage = 1;
                maxPages = 0;

                await GetUserPage();
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task GetUserPage()
        {
            UserListAction action = new UserListAction();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            action.PageQuery = pageQuery;
            if (currentOrganizationId != null)
            {
                action.OrganizationId = currentOrganizationId;
                action.EntityId = currentEntityId;

                KaplanPageable<UserListDisplayDto> currentPage = await UserService.GetStudentGroupUserDisplayPages(action);

                userResults = currentPage.PageContent;
                maxPages = userResults.Any() ? currentPage.MaxPages : 1;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && userResults.Count == 0;

                StateHasChanged();
            }

            isLoading = false;
            StateHasChanged();
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetUserPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetUserPage();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetUserPage();
        }

        protected void OnAssignClicked()
        {
            IsAssignBoxVisible = true;
            IsInviteBoxVisible = false;
        }

        protected void OnInviteClicked()
        {
            IsInviteBoxVisible = true;
            IsAssignBoxVisible = false;
        }

        private void HideDialogBoxes()
        {
            IsAssignBoxVisible = false;
            IsInviteBoxVisible = false;
        }

        private async Task AssignUser(string userId)
        {
            if (userId != string.Empty)
            {
                CreateUserLinkAction action = new CreateUserLinkAction
                {
                    UserId = userId,
                    EntityId = currentEntityId,
                    OrganizationId = currentOrganizationId
                };

                await StudentGroupService.AssignStudentGroupUser(action);

                currentPage = 1;
                await GetUserPage();
            }
        }

        protected async Task OnAssignResult(string userId)
        {
            HideDialogBoxes();
            await AssignUser(userId);
        }

        protected async Task OnInviteUserResult(string userId)
        {
            HideDialogBoxes();
            await AssignUser(userId);
        }

        protected void UnAssignUser(string userId, string? userName)
        {
            unAssignMessage = userName;
            unAssignUserId = userId;
            IsUnAssignDialogVisible = true;
        }

        protected async Task OnUnAssignDialogResult(bool result)
        {
            IsUnAssignDialogVisible = false;

            if (result)
            {
                if (unAssignUserId != string.Empty)
                {
                    CreateUserLinkAction action = new CreateUserLinkAction
                    {
                        UserId = unAssignUserId,
                        EntityId = currentEntityId,
                        OrganizationId = currentOrganizationId
                    };

                    await StudentGroupService.UnAssignStudentGroupUser(action);

                    unAssignMessage = string.Empty;
                    unAssignUserId = string.Empty;
                    currentPage = 1;
                    await GetUserPage();
                }
            }
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
