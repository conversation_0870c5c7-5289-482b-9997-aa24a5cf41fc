﻿@using System.Globalization
@using Compass.Common.Services
@inject CultureService CultureService

<!DOCTYPE html>
<html lang="@CultureInfo.CurrentCulture.ToString()">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <base href="/" />
        <link rel="stylesheet" href="bootstrap/bootstrap.min.css" />
        <link rel="stylesheet" href="app.css" />
        <link rel="stylesheet" href="Compass.styles.css" />
        <link rel="icon" type="image/png" href="c4l-favicon.png" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
        <HeadOutlet />
        <PageTitle>Compass | C4L</PageTitle>
    </head>

    <body>
        <Routes />
        <script src="_framework/blazor.web.js"></script>
    </body>
</html>

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if ( firstRender )
        {
            await CultureService.InitializeCultureAsync();
        }
    }
}
