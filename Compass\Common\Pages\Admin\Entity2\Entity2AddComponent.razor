﻿@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject IEntity2Service Entity2Service;
@inject UserAccessor UserAccessor
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

@if (entity2 is not null)
{
    <EditForm Model="entity2" FormName="formEntity2" OnValidSubmit="SubmitAsync" class="c4l-form entity2-add-form">
        <h3 class="c4l-form-heading">@Localizer["lbl_Add"] @entity2Name</h3>

        <DataAnnotationsValidator></DataAnnotationsValidator>
        <ValidationSummary></ValidationSummary>

        <div>
            <label class="col-form-label" for="entity2-name">
                @Localizer["lbl_Name"]
                <InputText 
                    @bind-Value="entity2.Name" 
                    class="form-control mt-2"
                    id="entity2-name" 
                    aria-required="true"
                />
            </label>
            <ValidationMessage For="() => entity2.Name" class="mt-2 text-danger" />
        </div>

        <div>
            <label class="col-form-label" for="entity2-address1">
                @Localizer["lbl_Address1"]
                <InputText 
                    @bind-Value="entity2.Address1" 
                    class="form-control mt-2"
                    id="entity2-address1" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity2-address2">
                @Localizer["lbl_Address2"]
                <InputText 
                    @bind-Value="entity2.Address2" 
                    class="form-control mt-2"
                    id="entity2-address2" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity2-city">
                @Localizer["lbl_City"]
                <InputText 
                    @bind-Value="entity2.City" 
                    class="form-control mt-2"
                    id="entity2-city" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity2-state">
                @Localizer["lbl_State"]
                <InputSelect 
                    @bind-Value="entity2.State" 
                    id="entity2-state" 
                >
                    <option disabled value="">-- @Localizer["lbl_State"] --</option>
                    @foreach (var stateCode in CompassResource.UsStates)
                    {
                        <option value="@stateCode">@stateCode</option>
                    }
                </InputSelect>
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity2-zipcode">
                @Localizer["lbl_ZipCode"]
                <InputText 
                    @bind-Value="entity2.ZipCode" 
                    class="form-control mt-2"
                    id="entity2-zipcode" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity2-contact-firstname">
                @Localizer["lbl_ContactFirstName"]
                <InputText 
                    @bind-Value="entity2.ContactFirstName" 
                    class="form-control mt-2"
                    id="entity2-contact-firstname" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity2-contact-lastname">
                @Localizer["lbl_ContactLastName"]
                <InputText 
                    @bind-Value="entity2.ContactLastName" 
                    class="form-control mt-2"
                    id="entity2-contact-lastname" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity2-contact-email">
                @Localizer["lbl_ContactEmail"]
                <InputText 
                    @bind-Value="entity2.ContactEmail" 
                    class="form-control mt-2"
                    id="entity2-contact-email" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity2-contact-phone">
                @Localizer["lbl_ContactPhone"]
                <InputText 
                    @bind-Value="entity2.ContactPhone" 
                    class="form-control mt-2"
                    id="entity2-contact-phone" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity2-contact-fax">
                @Localizer["lbl_ContactFax"]
                <InputText 
                    @bind-Value="entity2.ContactFax" 
                    class="form-control mt-2"
                    id="entity2-contact-fax" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity2-fax">
                @Localizer["lbl_Fax"]
                <InputText 
                    @bind-Value="entity2.Fax" 
                    class="form-control mt-2"
                    id="entity2-fax" 
                />
            </label>
        </div>

        <div class="form-submit-buttons-wrapper">
            <button class="c4l-button c4l-secondary-button c4l-form-button" type="submit">@Localizer["lbl_Save"]</button>
        </div>

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success mt-4" role="alert">
                <p class="text-center font-weight-600 mb-0">@successMessage</p>
            </div>
        }
    </EditForm>
}
