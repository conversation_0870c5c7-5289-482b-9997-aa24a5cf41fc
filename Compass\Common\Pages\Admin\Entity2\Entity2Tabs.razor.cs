﻿using Compass.Common.Data;
using Compass.Common.Pages.Admin.Entity3;
using Compass.Common.Pages.Admin.Site;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity2
{
    public partial class Entity2Tabs : IDisposable
    {
        private static readonly int SUMMARY_INDEX = 1;
        private static readonly int MANAGE_INDEX = 2;
        private static readonly int ADD_CHILD_INDEX = 3;
        private static readonly int EDIT_INDEX = 4;
        private static readonly int SUPPORT_INDEX = 5;
        private static readonly int REPORT_INDEX = 6;
        private static readonly int USER_INDEX = 7;

        private int currentTab = SUMMARY_INDEX;
        private Type? currentTabComponent;
        private Type? childHierarchyComponentType;

        private string childHierarchyEntity = string.Empty;
        private string currentEntity2Name = string.Empty;

        private readonly Dictionary<int, Type> tabComponents = new()
    {
        { SUMMARY_INDEX, typeof(Entity2SummaryComponent) },
        { MANAGE_INDEX, typeof(Entity2ManageComponent) },
        { EDIT_INDEX, typeof(Entity2EditComponent) },
        { SUPPORT_INDEX, typeof(Entity2SummaryComponent) },
        { REPORT_INDEX, typeof(Entity2ReportsComponent) },
        { USER_INDEX, typeof(Entity2Users) }
    };

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            currentTab = SUMMARY_INDEX;
            // Initialize with the first tab's component
            currentTabComponent = tabComponents[currentTab];

            CommonSessionDataObserver.AddStateChangeAsyncListeners(UpdateEntity2Name);

            await SetChildOrganizationHierarchy();
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task SetChildOrganizationHierarchy()
        {
            childHierarchyEntity = "";

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                if (commonSessionData.Entity3Hierarchy != string.Empty)
                {
                    childHierarchyEntity = commonSessionData.Entity3Hierarchy;
                    childHierarchyComponentType = typeof(Entity3AddComponent);
                }
                else if (commonSessionData.SiteHierarchy != string.Empty)
                {
                    childHierarchyEntity = commonSessionData.SiteHierarchy;
                    childHierarchyComponentType = typeof(SiteAddComponent);
                }

                this.currentEntity2Name = commonSessionData.SelectedEntityName;
            }
        }

        private async Task UpdateEntity2Name()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.currentEntity2Name = commonSessionData.SelectedEntityName;
                StateHasChanged();
            }
        }

        protected void ChangeTab(int tabIndex)
        {
            currentTab = tabIndex;

            if (currentTab == ADD_CHILD_INDEX)
            {
                currentTabComponent = childHierarchyComponentType;
            }
            else
            {
                currentTabComponent = tabComponents[currentTab];
            }
        }

        public void Dispose()
        {
            CommonSessionDataObserver.RemoveStateChangeAsyncListeners(UpdateEntity2Name);
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
