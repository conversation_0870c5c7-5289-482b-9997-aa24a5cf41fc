﻿@page "/account/set-password"

@using System.ComponentModel.DataAnnotations
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity

@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager

<PageTitle>Set Password | C4L</PageTitle>

<StatusMessage Message="@message" AlertType="@alertType" />

<div class="info-message-wrapper mb-4">
    <p class="my-0">You do not have a local username/password for this site. <br /> Add a local account so you can log in without an external login.</p>
</div>

<EditForm Model="Input" FormName="set-password" OnValidSubmit="OnValidSubmitAsync" method="post" class="c4l-form">
    <h2 class="c4l-form-heading text-center">Set your password</h2>
    <DataAnnotationsValidator />
    <ValidationSummary class="text-danger" role="alert" />

    <div class="form-floating mb-3">
        <InputText type="password" @bind-Value="Input.NewPassword" class="form-control" autocomplete="new-password" placeholder="Please enter your new password." />
        <label for="new-password" class="form-label">New password</label>
        <ValidationMessage For="() => Input.NewPassword" class="text-danger" />
    </div>

    <div class="form-floating mb-3">
        <InputText type="password" @bind-Value="Input.ConfirmPassword" class="form-control" autocomplete="new-password" placeholder="Please confirm your new password." />
        <label for="confirm-password" class="form-label">Confirm password</label>
        <ValidationMessage For="() => Input.ConfirmPassword" class="text-danger" />
    </div>

    <button type="submit" class="c4l-button c4l-form-button c4l-primary-button">Set password</button>
</EditForm>

@code {
    private string? message;
    private string? alertType = "danger";
    private ApplicationUser user = default!;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);

        var hasPassword = await UserManager.HasPasswordAsync(user);
        if (hasPassword)
        {
            RedirectManager.RedirectTo("account/change-password");
        }
    }

    private async Task OnValidSubmitAsync()
    {
        var addPasswordResult = await UserManager.AddPasswordAsync(user, Input.NewPassword!);
        if (!addPasswordResult.Succeeded)
        {
            message = $"Error: {string.Join(",", addPasswordResult.Errors.Select(error => error.Description))}";
            return;
        }

        await SignInManager.RefreshSignInAsync(user);
        RedirectManager.RedirectToCurrentPageWithStatus("Your password has been set.", HttpContext);
    }

    private sealed class InputModel
    {
        [Required]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "New password")]
        public string? NewPassword { get; set; }

        [DataType(DataType.Password)]
        [Display(Name = "Confirm new password")]
        [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match.")]
        public string? ConfirmPassword { get; set; }
    }
}
