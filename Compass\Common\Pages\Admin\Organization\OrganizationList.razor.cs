﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Organization;
using Compass.Common.Helpers;
using Compass.Common.Models;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Organization
{
    public partial class OrganizationList
    {
        private List<OrganizationListDisplayDto> organizationResults = new();

        private int maxPages;
        private int currentPage;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        bool isCurrentUserSuperAdmin = false;
        private bool noSearchResults = false;
        private bool isLoading = true;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            searchText = string.Empty;
            currentSearchText = string.Empty;

            await GetCommonSessionData();
            isCurrentUserSuperAdmin = await UserAccessor.IsUserInRoleAsync(_currentUserId, UserAccessor.USER_ROLE_SUPER_ADMIN);
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            currentPage = 1;
            maxPages = 0;
            await GetOrganizationPage();
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);
            InvokeAsync(StateHasChanged);
        }

        private async Task GetOrganizationPage()
        {
            if (isCurrentUserSuperAdmin)
            {
                isLoading = true;
                PageQuery pageQuery = new PageQuery();
                pageQuery.PageNumber = this.currentPage;
                pageQuery.QueryText = currentSearchText;

                KaplanPageable<OrganizationListDisplayDto> currentPage = await OrganizationService.GetOrganizationPages(pageQuery);

                organizationResults = currentPage.PageContent;
                maxPages = currentPage.MaxPages;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && organizationResults.Count == 0;
                isLoading = false;

                StateHasChanged();
            }
        }

        protected async Task OnOrgSelected(long? orgId)
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                if (_currentUser != null)
                {
                    // reset all entity IDs (but not organization ID)
                    commonSessionData.ResetCurrentIdValues();
                    
                    // Temporarily store the organization ID we want to set
                    long? targetOrgId = orgId;
                    
                    // Use UnSelectOrganization to clear all hierarchy strings and organization data
                    commonSessionData.UnSelectOrganization();
                    
                    // Set back the organization ID we want
                    commonSessionData.CurrentOrganizationId = targetOrgId;
                    
                    // Get visible entities for the new organization
                    List<VisibleEntity> visibleEntities = await OrganizationHierarchyRepository.GetVisibleEntities(_currentUserId, orgId);

                    // Only set hierarchy values if entities exist
                    for (int i = 0; i < visibleEntities.Count(); i++)
                    {
                        VisibleEntity ve = visibleEntities[i];
                        if (ve.EntityLevel == 0)
                        {
                            commonSessionData.CurrentOrganizationName = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 1)
                        {
                            commonSessionData.Entity1Hierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 2)
                        {
                            commonSessionData.Entity2Hierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 3)
                        {
                            commonSessionData.Entity3Hierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 4)
                        {
                            commonSessionData.SiteHierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 5)
                        {
                            commonSessionData.StudentGroupHierarchy = ve.EntityHierarchy ?? "";
                        }
                    }

                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();

                    // Force a full page reload to clear any component state
                    NavigationManager.NavigateTo($"/organization", forceLoad: true);
                }
            }
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetOrganizationPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetOrganizationPage();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetOrganizationPage();
        }

        protected void OnCreate()
        {
            if (isCurrentUserSuperAdmin)
            {
                NavigationManager.NavigateTo("/organization/addedit/0");
            }
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
