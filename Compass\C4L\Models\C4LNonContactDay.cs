using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.C4L.Models
{
    [Table("c4l_non_contact_days")]
    public class C4LNonContactDay
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("classroom_id")]
        public long C4L_ClassroomId { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("start_date")]
        public DateTime StartDate { get; set; }

        [Column("end_date")]
        public DateTime EndDate { get; set; }

        [Column("description")]
        public string? Description { get; set; }

        [Column("mod_id")]
        public string ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }
    }
}