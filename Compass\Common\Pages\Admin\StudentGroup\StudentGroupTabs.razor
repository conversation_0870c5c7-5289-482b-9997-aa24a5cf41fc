﻿@page "/studentgroup"
@using Compass.C4L.Interfaces.Services
@using Compass.C4L.Pages
@using Compass.Common.Controls.Generic
@using Compass.Common.Data
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject UserAccessor UserAccessor

<h1 class="page-title">@currentStudentGroupName</h1>

<ul class="nav-tabs-wrapper">
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == SUMMARY_INDEX ? "active" : "")" @onclick="() => ChangeTab(SUMMARY_INDEX)">Summary</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == MANAGE_INDEX ? "active" : "")" @onclick="() => ChangeTab(MANAGE_INDEX)">Manage</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == EDIT_INDEX ? "active" : "")" @onclick="() => ChangeTab(EDIT_INDEX)">Edit</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == REPORT_INDEX ? "active" : "")" @onclick="() => ChangeTab(REPORT_INDEX)">Reports</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == USER_INDEX ? "active" : "")" @onclick="() => ChangeTab(USER_INDEX)">Users</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == ROSTER_INDEX ? "active" : "")" @onclick="() => ChangeTab(ROSTER_INDEX)">Roster</button>
    </li>
</ul>

@if (currentTabComponent is not null)
{
    <div class="component-content-wrapper student-group-component-wrapper">
        <CascadingValue Value="this">
            <DynamicComponent Type="currentTabComponent" />
        </CascadingValue>
    </div>
}

@* Remove this section as it's causing the duplicate rendering *@
@* @if (currentTabComponent == typeof(C4L_NonContactDayAddEditComponent))
{
    <C4L_NonContactDayAddEditComponent NonContactDayId="@NonContactDayIdToEdit" />
} *@
