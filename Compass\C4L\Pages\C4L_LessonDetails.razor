@page "/c4l-lesson-details"

@using Compass.C4L.Interfaces.Services
@using Compass.C4L.Models
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.Extensions.Logging

@inject UserAccessor UserAccessor
@inject UserSessionService UserSessionService
@inject IStudentGroupRepository StudentGroupRepository
@inject IC4LClassroomService C4LClassroomService
@inject IC4LSessionStateService SessionStateService
@inject IC4LLessonPreparationCompletedService LessonPreparationCompletedService
@inject IC4LLessonPreparationService LessonPreparationService
@inject IC4LCmsApiService C4LCmsApiService
@inject ILogger<C4L_LessonDetails> Logger

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="lesson-details-container">
    <div class="header-container">
        <header>
            <h1>@LessonTitle</h1>
            <div class="lesson-subtitle">@LessonType</div>
            <div class="lesson-unit-info">Unit @Unit - Week @Week - Day @Day</div>
        </header>
        <button class="c4l-button c4l-primary-button" type="button" @onclick="ReturnToLessons">
            Back to Lessons
        </button>
    </div>

    <div class="lesson-details-content">
        @if (ActiveTab == "Overview")
        {
            <div class="lesson-section">
                <h2>At a Glance</h2>
                <div>
                    @((MarkupString)lessonContentDto.AtAGlance)
                </div>
            </div>

            <div class="section-divider"></div>

            <div class="lesson-section">
                <h2>Learning Objectives</h2>
                @if (lessonContentDto.Objectives.NonAssessed.Count > 0)
                {
                    <h3>Non-Assessed Objectives</h3>
                    <ul>
                        @foreach (string objective in lessonContentDto.Objectives.NonAssessed)
                        {
                            <li>@((MarkupString)objective)</li>
                        }
                    </ul>
                }
                else
                {
                    <p>No non-assessed objectives for this lesson.</p>
                }
            </div>

            <div class="section-divider"></div>

            <div class="lesson-section">
                <h2>Assessed Objectives</h2>
                @if (lessonContentDto.Objectives.Assessed.Count > 0)
                {
                    <ul>
                        @foreach (string objective in lessonContentDto.Objectives.Assessed)
                        {
                            <li>@((MarkupString)objective)</li>
                        }
                    </ul>
                }
                else
                {
                    <p>No assessed objectives for this lesson.</p>
                }
            </div>

            <div class="section-divider"></div>

            <div class="lesson-section">
                <h2>Preparation</h2>
                @foreach (string preparation in lessonContentDto.PreparationTasks)
                {
                    @((MarkupString)preparation)
                }

                @if (IsPreparationCompleted)
                {
                    <div class="preparation-completed">
                        <span class="completed-icon">✓</span> Preparation completed
                    </div>
                    <button class="c4l-button c4l-primary-button mark-complete-button" @onclick="async () => await UpdateCompletedStatus(false)">
                        MARK AS INCOMPLETE
                    </button>
                }
                else
                {
                    <div class="preparation-completed">
                        <span class="completed-icon">✓</span> Preparation not completed
                    </div>
                    <button class="c4l-button c4l-primary-button mark-complete-button" @onclick="async () => await UpdateCompletedStatus(true)">
                        MARK AS COMPLETE
                    </button>
                }
            </div>

            <div class="section-divider"></div>

            <div class="lesson-section">
                <h2>Materials</h2>
                @((MarkupString)lessonContentDto.Materials)
            </div>

            <div class="lesson-section">
                <h2>Instructions</h2>
                @foreach (string instruction in lessonContentDto.Instructions)
                {
                    @((MarkupString)instruction)
                }
            </div>
        }
        else if (ActiveTab == "Teacher Reflection")
        {
            <div class="lesson-section">
                <h2>Teacher Reflection</h2>
                <p>Teacher reflection content will be added in the future.</p>
            </div>
        }
    </div>
</div>

<style>
    .lesson-details-container {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;
        border-bottom: 1px solid #e5e7eb;
        padding-bottom: 15px;
    }

    header {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    h1 {
        margin: 0;
        font-size: 28px;
        color: var(--c4l-primary-purple);
        font-weight: bold;
    }

    .lesson-subtitle {
        font-size: 16px;
    }

    .lesson-unit-info {
        font-size: 16px;
    }

    .lesson-tabs {
        display: flex;
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 20px;
    }

    .tab-item {
        padding: 10px 20px;
        cursor: pointer;
        position: relative;
    }

    .tab-item.active {
        font-weight: bold;
        color: var(--c4l-primary-purple);
    }

    .tab-item.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--c4l-primary-purple);
    }

    .arrow {
        font-size: 12px;
        color: var(--c4l-primary-purple);
    }

    .lesson-details-content {
        margin-top: 20px;
    }

    .lesson-section {
        margin-bottom: 20px;
    }

    h2 {
        color: var(--c4l-primary-purple);
        font-size: 22px;
        margin-bottom: 15px;
        font-weight: bold;
    }

    p {
        line-height: 1.6;
        margin-bottom: 15px;
    }

    ul {
        padding-left: 20px;
        margin-bottom: 15px;
    }

    li {
        margin-bottom: 10px;
        line-height: 1.6;
    }

    em {
        font-style: italic;
    }

    .section-divider {
        border-bottom: 1px dotted #ccc;
        margin: 30px 0;
        height: 1px;
    }

    .mark-complete-button {
        margin-top: 15px;
    }

    .preparation-completed {
        margin-top: 15px;
        color: var(--c4l-secondary-teal);
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .completed-icon {
        font-size: 18px;
        font-weight: bold;
    }
</style>
