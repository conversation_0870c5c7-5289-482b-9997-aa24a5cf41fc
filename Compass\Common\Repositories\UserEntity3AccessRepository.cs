﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class UserEntity3AccessRepository : IUserEntity3AccessRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public UserEntity3AccessRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<UserEntity3Access?> GetUserEntity3AccessAsync(long? organizationId, long? entity3Id)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (entity3Id is null)
            {
                throw new ArgumentNullException(nameof(entity3Id));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.UserEntity3Accesses.FirstOrDefaultAsync(o => o.OrganizationId == organizationId && o.Entity3Id == entity3Id);
            }
        }

        public async Task<UserEntity3Access?> AddUserEntity3AccessAsync(UserEntity3Access? userEntity3Access)
        {
            if (userEntity3Access is null)
            {
                throw new ArgumentNullException(nameof(userEntity3Access));
            }

            // Get the authentication state 
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            userEntity3Access.ModId = userId;
            userEntity3Access.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.UserEntity3Accesses.AddAsync(userEntity3Access);
                await _dbContext.SaveChangesAsync();
            }

            return userEntity3Access;
        }
    }
}
