using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_items_lookup")]
    public class LAPItemsLookup
    {
        [Key]
        [Column("StaticID")]
        public long StaticId { get; set; }

        [Required]
        [Column("Language")]
        public int Language { get; set; }

        [Required]
        [Column("Sequence")]
        public short Sequence { get; set; }

        [Required]
        [Column("SubscaleStaticID")]
        public long SubscaleStaticId { get; set; }

        [Required]
        [Column("ItemNo")]
        public short ItemNo { get; set; }

        [Column("Description")]
        [StringLength(1000)]
        public string? Description { get; set; }

        [Required]
        [Column("Instrument")]
        public int Instrument { get; set; }

        [Column("Months")]
        public short? Months { get; set; }

        [Column("Skill")]
        [StringLength(1000)]
        public string? Skill { get; set; }

        [Column("Materials")]
        [StringLength(1000)]
        public string? Materials { get; set; }

        [Column("Comment")]
        [StringLength(1000)]
        public string? Comment { get; set; }

        [Column("MinSelectionRequired")]
        public int? MinSelectionRequired { get; set; }

        [Column("AnnualGoal")]
        [StringLength(1000)]
        public string? AnnualGoal { get; set; }

        [Column("Required")]
        public short? Required { get; set; }

        [Column("IEPRefNo")]
        [StringLength(4)]
        public string? IEPRefNo { get; set; }

        [Column("AutoScoreItemNo")]
        public long? AutoScoreItemNo { get; set; }

        [Column("AutoScoreItemNoFwd")]
        public long? AutoScoreItemNoFwd { get; set; }

        [Column("CollectionMethods")]
        [StringLength(20)]
        public string? CollectionMethods { get; set; }

        [Required]
        [Column("SubscaleID")]
        [StringLength(5)]
        public string SubscaleId { get; set; } = string.Empty;

        [Column("ItemProcedure")]
        [StringLength(1000)]
        public string? ItemProcedure { get; set; }
    }
}