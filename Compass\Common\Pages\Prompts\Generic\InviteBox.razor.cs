﻿using Compass.Common.Data;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.WebUtilities;
using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Text.Encodings.Web;

namespace Compass.Common.Pages.Prompts.Generic
{
    public partial class InviteBox
    {
        [Parameter]
        public EventCallback<string> InviteUserResult { get; set; }

        [Parameter]
        public bool IsVisible { get; set; }

        [Parameter]
        public long? OrganizationId { get; set; }

        [Parameter]
        public string CustomModalClass { get; set; } = string.Empty;

        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        [SupplyParameterFromQuery]
        private string? ReturnUrl { get; set; }

        private string? successMessage = string.Empty;
        private IEnumerable<IdentityError>? identityErrors;
        private string? Message => identityErrors is null ? null : $"Error: {string.Join(", ", identityErrors.Select(error => error.Description))}";

        protected override void OnParametersSet()
        {
            if (IsVisible)
            {
                Input = new(); // Clear input when the dialog is shown
            }
        }

        private ApplicationUser CreateUser()
        {
            try
            {
                return Activator.CreateInstance<ApplicationUser>();
            }
            catch
            {
                throw new InvalidOperationException($"Can't create an instance of '{nameof(ApplicationUser)}'. " +
                    $"Ensure that '{nameof(ApplicationUser)}' is not an abstract class and has a parameterless constructor.");
            }
        }

        private IUserEmailStore<ApplicationUser> GetEmailStore()
        {
            if (!UserManager.SupportsUserEmail)
            {
                throw new NotSupportedException("The default UI requires a user store with email support.");
            }
            return (IUserEmailStore<ApplicationUser>)UserStore;
        }

        protected async Task InviteUserAsync(EditContext editContext)
        {
            ApplicationUser? exitingUser = await UserManager.FindByEmailAsync(Input.Email);

            if (exitingUser == null)
            {
                ApplicationUser user = CreateUser();

                if (Input.Email != null && Input.Email != string.Empty)
                {
                    IUserEmailStore<ApplicationUser> emailStore = GetEmailStore();
                    await emailStore.SetEmailAsync(user, Input.Email, CancellationToken.None);
                }

                if (this.OrganizationId is null)
                {
                    throw new Exception("Organization Id is not set");
                }
                user.OrganizationId = this.OrganizationId;

                user.UserName = Input.Email;
                user.FirstName = Input.FirstName;
                user.LastName = Input.LastName;

                IdentityResult result = await UserManager.CreateAsync(user);

                if (!result.Succeeded)
                {
                    identityErrors = result.Errors;
                    return;
                }

                string userId = await UserManager.GetUserIdAsync(user);
                string code = await UserManager.GenerateEmailConfirmationTokenAsync(user);
                code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
                string callbackUrl = NavigationManager.GetUriWithQueryParameters(
                    NavigationManager.ToAbsoluteUri("Account/AcceptInvite").AbsoluteUri,
                    new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code, ["returnUrl"] = ReturnUrl });

                if (Input.Email != null)
                {
                    await EmailSender.SendConfirmationLinkAsync(user, Input.Email, HtmlEncoder.Default.Encode(callbackUrl));
                }
                else
                {
                    Logger.LogInformation("User invite failed to send.");
                }

                await InviteUserResult.InvokeAsync(userId);
            }
            else
            {
                await InviteUserResult.InvokeAsync(exitingUser.Id);
            }
        }

        protected async Task OnCancelClick()
        {
            await InviteUserResult.InvokeAsync(string.Empty);
        }

        public sealed class InputModel
        {
            [Required]
            [Display(Name = "FirstName")]
            public string FirstName { get; set; } = "";

            [Required]
            [Display(Name = "LastName")]
            public string LastName { get; set; } = "";

            [Required]
            [Display(Name = "Email")]
            [CustomValidation(typeof(InputModel), nameof(ValidateEmail))]
            public string Email { get; set; } = "";

            public static ValidationResult? ValidateEmail(string? email, ValidationContext context)
            {
                if (string.IsNullOrEmpty(email))
                {
                    return ValidationResult.Success; // Optional, so no error if empty.
                }

                var emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
                if (System.Text.RegularExpressions.Regex.IsMatch(email, emailPattern))
                {
                    return ValidationResult.Success;
                }

                return new ValidationResult("The email address is not valid.");
            }
        }
    }
}
