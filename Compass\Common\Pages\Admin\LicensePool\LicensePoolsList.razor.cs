﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.LicensePool;
using Compass.Common.Helpers;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.LicensePool
{
    public partial class LicensePoolsList
    {
        private List<LicensePoolListDisplayDto> licensePoolResults = new();

        private int maxPages;
        private int currentPage;

        private long? currentOrganizationId;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        private bool noSearchResults = false;
        private bool isLoading = true;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private bool isCurrentUserSuperAdmin = false;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            searchText = string.Empty;
            currentSearchText = string.Empty;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            isCurrentUserSuperAdmin = await UserAccessor.IsUserInRoleAsync(_currentUserId, UserAccessor.USER_ROLE_SUPER_ADMIN);
            if (commonSessionData != null)
            {
                currentOrganizationId = commonSessionData.CurrentOrganizationId;

                currentPage = 1;
                maxPages = 0;
                await GetLicensePoolPage();
            }
        }

        private async Task GetLicensePoolPage()
        {
            if (isCurrentUserSuperAdmin)
            {
                isLoading = true;
                PageQuery pageQuery = new PageQuery();
                pageQuery.PageNumber = this.currentPage;
                pageQuery.QueryText = currentSearchText;

                if (currentOrganizationId != null)
                {
                    KaplanPageable<LicensePoolListDisplayDto> currentPage = await LicensePoolService.GetLicensePoolDisplayPages(pageQuery, currentOrganizationId);

                    licensePoolResults = currentPage.PageContent;
                    maxPages = currentPage.MaxPages;
                    noSearchResults = !string.IsNullOrEmpty(currentSearchText) && licensePoolResults.Count == 0;
                    isLoading = false;

                    StateHasChanged();
                }
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetLicensePoolPage();
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetLicensePoolPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetLicensePoolPage();
            }
        }

        protected void OnLicensePoolDetailsClick(long licensePoolId)
        {
            NavigationManager.NavigateTo($"/license-edit/{licensePoolId}");
        }

        protected void OnCreateLicensePool()
        {
            if (isCurrentUserSuperAdmin)
            {
                NavigationManager.NavigateTo("/license-add/");
            }
        }
    }
}
