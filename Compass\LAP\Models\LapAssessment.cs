using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_assessments")]
    public class LapAssessment
    {
        [Key]
        [Column("InstID")]
        public long Id { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Column("create_ts")]
        public DateTime? CreatTs { get; set; }

        [Required]
        [Column("CustomerInstID")]
        public long OrganizationId { get; set; }

        [Required]
        [Column("ChildInstID")]
        public long StudentId { get; set; }

        [Required]
        [Column("DateOfAssessment")]
        public DateTime DateOfAssessment { get; set; }

        [Required]
        [Column("SchoolYearInstID")]
        public long SchoolYearId { get; set; }

        [Column("SchoolYear")]
        public int? SchoolYear { get; set; }

        [Column("ObserverInstID")]
        public string? ObserverId { get; set; }

        [Column("ObserverType")]
        public int? ObserverType { get; set; }

        [Column("ObserverName")]
        [StringLength(200)]
        public string? ObserverName { get; set; }

        [Column("TeacherInstID")]
        public long? TeacherInstId { get; set; }

        [Column("Teacher")]
        [StringLength(200)]
        public string? Teacher { get; set; }

        [Required]
        [Column("Language")]
        public int Language { get; set; }

        [Column("ChronologicalAge")]
        public int? ChronologicalAge { get; set; }

        [Column("Type")]
        public int? Instrument { get; set; }

        //LAP Screen only
        [Column("Result")]
        [StringLength(15)]
        public string? Result { get; set; }

        //LAP Screen only
        [Column("TotalScore")]
        public int? TotalScore { get; set; }

        [Column("CheckPt")]
        public int? CheckPt { get; set; }
    }
}