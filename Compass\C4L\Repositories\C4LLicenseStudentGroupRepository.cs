﻿using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.C4L.Repositories
{
    public class C4LLicenseStudentGroupRepository : IC4LLicenseStudentGroupRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public C4LLicenseStudentGroupRepository(
            IDbContextFactory<ApplicationDbContext> contextFactory,
            AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<long?> CreateLicense(long organizationId, long licensePoolId, long c4l_classroomId)
        {
            C4LLicenseStudentGroup license = new C4LLicenseStudentGroup();
            license.OrganizationId = organizationId;
            license.LicensePoolId = licensePoolId;
            license.C4L_ClassroomId = c4l_classroomId;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                license.ModId = userId;
                license.ModTs = DateTime.Now;

                await _dbContext.C4LLicenseStudentGroups.AddAsync(license);
                await _dbContext.SaveChangesAsync();
                return license.Id;
            }
        }

        public async Task<long?> CreateLicenseWithContext(long organizationId, long licensePoolId, long c4l_classroomId, ApplicationDbContext dbContext)
        {
            C4LLicenseStudentGroup license = new C4LLicenseStudentGroup();
            license.OrganizationId = organizationId;
            license.LicensePoolId = licensePoolId;
            license.C4L_ClassroomId = c4l_classroomId;

            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

            license.ModId = userId;
            license.ModTs = DateTime.Now;

            await dbContext.C4LLicenseStudentGroups.AddAsync(license);
            await dbContext.SaveChangesAsync();
            return license.Id;
        }
    }
}
