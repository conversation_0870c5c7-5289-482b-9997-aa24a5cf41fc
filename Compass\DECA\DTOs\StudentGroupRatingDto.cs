﻿﻿using System;

namespace Compass.DECA.DTOs
{
    public class StudentGroupRatingDto
    {
        // Student Information
        public long StudentId { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public DateTime? BirthDate { get; set; }

        // Rating Information
        public long? RatingId { get; set; }
        public DateTime? RatingDate { get; set; }
        public int? RatingYear { get; set; }
        public string? RatingPeriod { get; set; }
        public string? RecordForm { get; set; }
        public string? RatingLevel { get; set; }
        public string? RaterType { get; set; }
        public int? AgeAtRatingMonths { get; set; }

        // IN (Inattention) Scale
        public int? InRaw { get; set; }
        public int? InTScore { get; set; }
        public int? InPercentile { get; set; }
        public string? InDescription { get; set; }

        // SC (Self-Control) Scale
        public int? ScRaw { get; set; }
        public int? ScTScore { get; set; }
        public int? ScPercentile { get; set; }
        public string? ScDescription { get; set; }

        // AT (Attention) Scale
        public int? ArRaw { get; set; }
        public int? ArTScore { get; set; }
        public int? ArPercentile { get; set; }
        public string? AtDescription { get; set; }

        // TPF (Task Planning/Focus) Scale
        public int? TpfRaw { get; set; }
        public int? TpfTScore { get; set; }
        public int? TpfPercentile { get; set; }
        public string? TpfDescription { get; set; }

        // BC (Behavioral Control) Scale
        public int? BcRaw { get; set; }
        public int? BcTScore { get; set; }
        public int? BcPercentile { get; set; }
        public string? BcDescription { get; set; }

        // PR (Peer Relations) Scale
        public int? PrRaw { get; set; }
        public int? PrTScore { get; set; }
        public int? PrPercentile { get; set; }
        public string? PrDescription { get; set; }

        // OT (Organization/Time Management) Scale
        public int? OtRaw { get; set; }
        public int? OtTScore { get; set; }
        public int? OtPercentile { get; set; }
        public string? OtDescription { get; set; }

        // GB (Goal-directed Behavior) Scale
        public int? GbRaw { get; set; }
        public int? GbTScore { get; set; }
        public int? GbPercentile { get; set; }
        public string? GbDescription { get; set; }

        // SO (Social) Scale
        public int? SoRaw { get; set; }
        public int? SoTScore { get; set; }
        public int? SoPercentile { get; set; }
        public string? SoDescription { get; set; }

        // DM (Decision Making) Scale
        public int? DmRaw { get; set; }
        public int? DmTScore { get; set; }
        public int? DmPercentile { get; set; }
        public string? DmDescription { get; set; }

        // RS (Response to Stress) Scale
        public int? RsRaw { get; set; }
        public int? RsTScore { get; set; }
        public int? RsPercentile { get; set; }
        public string? RsDescription { get; set; }

        // SA (Social Awareness) Scale
        public int? SaRaw { get; set; }
        public int? SaTScore { get; set; }
        public int? SaPercentile { get; set; }
        public string? SaDescription { get; set; }

        // SM (Self-Management) Scale
        public int? SmRaw { get; set; }
        public int? SmTScore { get; set; }
        public int? SmPercentile { get; set; }
        public string? SmDescription { get; set; }

        // SEC (Social-Emotional Composite) Scale
        public int? SecRaw { get; set; }
        public int? SecTScore { get; set; }
        public int? SecPercentile { get; set; }
        public string? SecDescription { get; set; }
    }
}
