namespace Compass.Common.Resources
{
    public class CompassResource
    {
        public static string LinkStatus_Active => "Active";
        public static string LinkStatus_Closed => "Closed";
        public const string C4L = "C4L";
        public const string DECA = "DECA";
        public const string LAP = "LAP";

        // Array of all available products using the constants
        public static readonly string[] KaplanProducts = new[]
        {
            C4L,
            DECA,
            LAP
        };

        public static List<string> UsStates => new()
        {
            "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware", "Florida",
            "Georgia", "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana",
            "Maine", "Maryland", "Massachusetts", "Michigan", "Minnesota", "Mississippi", "Missouri",
            "Montana", "Nebraska", "Nevada", "New Hampshire", "New Jersey", "New Mexico", "New York",
            "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon", "Pennsylvania", "Rhode Island",
            "South Carolina", "South Dakota", "Tennessee", "Texas", "Utah", "Vermont", "Virginia",
            "Washington", "West Virginia", "Wisconsin", "Wyoming"
        };
    }
}