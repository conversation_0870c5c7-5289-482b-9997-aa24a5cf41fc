﻿using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Student;
using Compass.Common.DTOs.StudentGroup;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Compass.Common.Models;
using Compass.Common.Resources;

namespace Compass.Common.Services
{
    public class StudentGroupService : IStudentGroupService
    {
        private readonly IStudentGroupRepository _studentGroupRepository;
        private readonly IUserStudentGroupAccessRepository _userStudentGroupAccessRepository;
        private readonly IUserStudentGroupLinkRepository _userStudentGroupLinkRepository;
        private readonly IUserRepository _userRepository;
        private readonly IStudentGroupRosterRepository _studentGroupRosterRepository;

        public StudentGroupService(IStudentGroupRepository studentGroupRepository,
                                    IUserStudentGroupAccessRepository userStudentGroupAccessRepository,
                                    IUserStudentGroupLinkRepository userStudentGroupLinkRepository,
                                    IUserRepository userRepository,
                                    IStudentGroupRosterRepository studentGroupRosterRepository)
        {
            _studentGroupRepository = studentGroupRepository;
            _userStudentGroupAccessRepository = userStudentGroupAccessRepository;
            _userStudentGroupLinkRepository = userStudentGroupLinkRepository;
            _userRepository = userRepository;
            _studentGroupRosterRepository = studentGroupRosterRepository;
        }

        public async Task<StudentGroup> CreateStudentGroupAsync(StudentGroup studentGroup)
        {
            StudentGroup createdStudentGroup = await _studentGroupRepository.CreateStudentGroupAsync(studentGroup);

            UserStudentGroupAccess studentGroupUserAccess = new UserStudentGroupAccess();
            studentGroupUserAccess.OrganizationId = studentGroup.OrganizationId;
            studentGroupUserAccess.StudentGroupId = studentGroup.Id;
            studentGroupUserAccess.CanAdd = "Y";
            studentGroupUserAccess.CanUpdate = "Y";
            studentGroupUserAccess.CanDelete = "Y";
            studentGroupUserAccess.CanView = "Y";
            studentGroupUserAccess.CanAssign = "Y";

            await _userStudentGroupAccessRepository.AddUserStudentGroupAccessAsync(studentGroupUserAccess);

            return createdStudentGroup;
        }

        public async Task<StudentGroup?> UpdateStudentGroupAsync(long? id, StudentGroup? studentGroup)
        {
            if (id == null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (studentGroup == null)
            {
                throw new ArgumentNullException(nameof(studentGroup));
            }

            StudentGroup? updatedStudentGroup = await _studentGroupRepository.UpdateStudentGroupAsync(id, studentGroup);
            return updatedStudentGroup;
        }

        public async Task<StudentGroup?> GetStudentGroupAsync(long? id)
        {
            if (id == null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            StudentGroup? studentGroup = await _studentGroupRepository.GetStudentGroupAsync(id);
            return studentGroup;
        }

        public async Task<KaplanPageable<StudentGroupListDisplayDto>> GetStudentGroupPage(StudentGroupListAction action)
        {
            List<StudentGroupListDisplayDto> studentGroupList = await _studentGroupRepository.GetStudentGroupList(action);

            long? organizationId = action.OrganizationId;
            long? siteId = action.SiteId;

            PageQuery pageQuery = action.PageQuery;
            int pageSize = pageQuery.PageSize;

            int studentGroupCount = await _studentGroupRepository.GetStudentGroupCount(organizationId, siteId, pageQuery.QueryText);

            int maxPages = (int)Math.Ceiling((double)studentGroupCount / pageSize);

            KaplanPageable<StudentGroupListDisplayDto> pageable = new KaplanPageable<StudentGroupListDisplayDto>();
            pageable.PageContent = studentGroupList;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        private async Task<bool> ValidateHasUsers(long? organizationId, long? studentGroupId)
        {
            int userCount = await _userRepository.GetStudentGroupUserCount(organizationId, studentGroupId, string.Empty);

            if (userCount > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task<DeleteReturnDto> DeleteStudentGroup(long? organizationId, long? studentGroupId)
        {
            DeleteReturnDto ret = new();
            ret.Success = true;

            // Validate Students
            int studentRosterCount = await _studentGroupRosterRepository.GetRosterCount(organizationId, studentGroupId);
            if (studentRosterCount > 0)
            {
                ret.Success = false;
                ret.Message = nameof(CommonResource.err_StudentAssigned);
            }

            // validate users
            bool hasUsers = await ValidateHasUsers(organizationId, studentGroupId);
            if (hasUsers)
            {
                ret.Success = false;
                ret.Message = nameof(CommonResource.err_UsersAssigned);
            }

            //If valid then delete
            if (ret.Success)
            {
                bool success = await _studentGroupRepository.DeleteStudentGroup(studentGroupId);
                ret.Success = success;
            }

            return ret;
        }

        public async Task<UserStudentGroupLink> AssignStudentGroupUser(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            long? studentGroupId = action.EntityId;

            UserStudentGroupAccess? userStudentGroupAccess = await _userStudentGroupAccessRepository.GetUserStudentGroupAccessAsync(organizationId, studentGroupId);

            if (userStudentGroupAccess is null)
            {
                throw new Exception("No Student Group Access Found");
            }

            UserStudentGroupLink createdLink = new UserStudentGroupLink();
            createdLink.OrganizationId = organizationId;
            createdLink.StudentGroupUserAccessId = userStudentGroupAccess.Id;
            createdLink.UserId = action.UserId;
            createdLink.LinkStatus = CompassResource.LinkStatus_Active;
            createdLink.UserRole = "TEST"; // TODO need to figure out user roles

            UserStudentGroupLink newLink = await _userStudentGroupLinkRepository.AddUserStudentGroupLinkAsync(createdLink);

            return newLink;
        }

        public async Task<bool> UnAssignStudentGroupUser(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            long? studentGroupId = action.EntityId;
            UserStudentGroupAccess? userStudentGroupAccess = await _userStudentGroupAccessRepository.GetUserStudentGroupAccessAsync(organizationId, studentGroupId);

            if (userStudentGroupAccess is null)
            {
                throw new Exception("No Student Group Access Found");
            }

            string? userId = action.UserId;
            long? accessId = userStudentGroupAccess.Id;

            UserStudentGroupLink? removeLink = await _userStudentGroupLinkRepository.GetUserStudentGroupLinkAsync(organizationId, userId, accessId);

            if (removeLink is null)
            {
                throw new Exception("No Link Found");
            }

            long? linkId = removeLink.Id;
            bool result = await _userStudentGroupLinkRepository.RemoveUserStudentGroupLinkAsync(linkId);

            return result;
        }

        public async Task<KaplanPageable<StudentDisplayDto>> GetStudentGroupRoster(StudentGroupRosterListAction action)
        {
            List<StudentDisplayDto> studentDisplayList = await _studentGroupRosterRepository.GetRosterList(action);

            long? organizationId = action.OrganizationId;
            long? studentGroupId = action.StudentGroupId;
            int rosterCount = await _studentGroupRosterRepository.GetRosterCount(organizationId, studentGroupId);

            PageQuery pageQuery = action.PageQuery;
            int pageSize = pageQuery.PageSize;

            int maxPages = (int)Math.Ceiling((double)rosterCount / pageSize);

            KaplanPageable<StudentDisplayDto> pageable = new KaplanPageable<StudentDisplayDto>();
            pageable.PageContent = studentDisplayList;
            pageable.MaxPages = maxPages;

            return pageable;
        }
    }
}
