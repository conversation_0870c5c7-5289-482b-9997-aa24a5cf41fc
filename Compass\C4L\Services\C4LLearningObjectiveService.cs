using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;

namespace Compass.C4L.Services
{
    public class C4LLearningObjectiveService : IC4LLearningObjectiveService
    {
        private readonly IC4LLearningObjectiveRepository _repository;

        public C4LLearningObjectiveService(IC4LLearningObjectiveRepository repository)
        {
            _repository = repository;
        }

        public async Task<IEnumerable<C4LLearningObjective>> GetLearningObjectivesAsync(string language)
        {
            return await _repository.GetLearningObjectivesAsync(language);
        }
    }
}