﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.User;
using Compass.Common.Helpers;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.General
{
    public partial class GeneralUsersList
    {
        private List<UserListDisplayDto> userResults = new();

        private int maxPages;
        private int currentPage;

        private long? currentOrganizationId;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        private bool IsCreateUserBoxVisible = false;
        private bool noSearchResults = false;
        private bool isLoading = true;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            searchText = string.Empty;
            currentSearchText = string.Empty;
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                currentOrganizationId = commonSessionData.CurrentOrganizationId;

                currentPage = 1;
                maxPages = 0;
                await GetUserPage();
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task GetUserPage()
        {
            isLoading = true;
            UserListAction action = new UserListAction();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;
            action.PageQuery = pageQuery;

            if (currentOrganizationId != null)
            {
                action.OrganizationId = currentOrganizationId;

                KaplanPageable<UserListDisplayDto> currentPage = await UserService.GetUserDisplayPages(action);

                userResults = currentPage.PageContent;
                maxPages = currentPage.MaxPages;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && userResults.Count == 0;
                isLoading = false;

                StateHasChanged();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetUserPage();
        }

        protected void OnCreateUser()
        {
            IsCreateUserBoxVisible = true;
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetUserPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetUserPage();
            }
        }

        protected async Task OnCreateUserResult(bool result)
        {
            IsCreateUserBoxVisible = false;
            await GetUserPage();
        }

        protected void OnUserDetailsClick(string userId)
        {
            string url = "/user-details/" + userId;
            NavigationManager.NavigateTo(url);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
