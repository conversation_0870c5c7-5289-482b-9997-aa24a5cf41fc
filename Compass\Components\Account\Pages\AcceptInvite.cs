﻿using Compass.Common.Data;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.JSInterop;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Compass.Components.Account.Pages
{
    public partial class AcceptInvite
    {
        private string? statusMessage;

        // No HttpContext dependency

        [SupplyParameterFromQuery]
        private string? UserId { get; set; }

        [SupplyParameterFromQuery]
        private string? Code { get; set; }

        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        private IEnumerable<IdentityError>? identityErrors;

        protected override async Task OnInitializedAsync()
        {
            try
            {
                // This component uses a custom layout that doesn't share the main app's session
                // No need to log out as we're in a separate context

                if (UserId is null || Code is null)
                {
                    RedirectManager.RedirectTo("");
                    return;
                }

                ApplicationUser? user = await UserManager.FindByIdAsync(UserId);

                if (user is null)
                {
                    statusMessage = $"Error loading user with ID {UserId}";
                    return;
                }
                else
                {
                    string? userName = user.UserName;
                    string? firstName = user.FirstName;
                    string? lastName = user.LastName;

                    Input.UserName ??= userName;
                    Input.FirstName ??= firstName;
                    Input.LastName ??= lastName;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error in AcceptInvite.OnInitializedAsync");
                statusMessage = "An error occurred while processing your invitation. Please try again.";
            }
        }

        protected async Task ConfirmUserAsync(EditContext editContext)
        {
            try
            {
                if (UserId is null || Code is null)
                {
                    RedirectManager.RedirectTo("");
                    return;
                }

                ApplicationUser? user = await UserManager.FindByIdAsync(UserId);

                if (user == null)
                {
                    statusMessage = $"Error loading user with ID {UserId}";
                    return;
                }

                string? code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(Code));
                IdentityResult result = await UserManager.ConfirmEmailAsync(user, code);

                if (!result.Succeeded)
                {
                    statusMessage = "Error confirming your email.";
                    Logger.LogError("Failed to confirm email for user {UserId}", UserId);
                    return;
                }

                // Update user information
                await UserManager.SetUserNameAsync(user, Input.UserName);

                // Set first and last name
                user.FirstName = Input.FirstName;
                user.LastName = Input.LastName;

                // Add password
                IdentityResult passwordResult = await UserManager.AddPasswordAsync(user, Input.Password);
                if (!passwordResult.Succeeded)
                {
                    statusMessage = "Error setting password.";
                    Logger.LogError("Failed to set password for user {UserId}", UserId);
                    return;
                }

                // Update user
                IdentityResult updateResult = await UserManager.UpdateAsync(user);
                if (!updateResult.Succeeded)
                {
                    statusMessage = "Error updating user information.";
                    Logger.LogError("Failed to update user {UserId}", UserId);
                    return;
                }

                statusMessage = "Thank you for confirming your email. You will be redirected to the login page.";
                Logger.LogInformation("User {UserId} successfully confirmed email and set up account", UserId);

                // Show the success message for a moment before redirecting
                await Task.Delay(1500);

                // Perform a complete logout
                await PerformLogoutAndRedirect();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error in AcceptInvite.ConfirmUserAsync");
                statusMessage = "An error occurred while processing your invitation. Please try again.";
            }
        }


        private async Task PerformLogoutAndRedirect()
        {
            try
            {
                // First, sign out using SignInManager
                await SignInManager.SignOutAsync();
                Logger.LogInformation("User logged out after accepting invite");

                // Submit the logout form using JavaScript
                await JSRuntime.InvokeVoidAsync("eval", "document.getElementById('logoutForm').submit();");
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error during logout process: {Message}", ex.Message);

                // If the above fails, try a direct redirect
                try
                {
                    // Use our custom JavaScript function as a fallback
                    await JSRuntime.InvokeVoidAsync("clearAllCookiesAndRedirect", "/login");
                }
                catch
                {
                    // Last resort: direct navigation
                    NavigationManager.NavigateTo("/login", true);
                }
            }
        }

        public sealed class InputModel
        {
            [Required]
            [Display(Name = "UserName")]
            public string? UserName { get; set; }

            [Required]
            [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
            [DataType(DataType.Password)]
            [Display(Name = "Password")]
            public string Password { get; set; } = "";

            [DataType(DataType.Password)]
            [Display(Name = "Confirm password")]
            [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
            public string ConfirmPassword { get; set; } = "";

            [Display(Name = "FirstName")]
            public string? FirstName { get; set; }

            [Display(Name = "LastName")]
            public string? LastName { get; set; }
        }
    }
}
