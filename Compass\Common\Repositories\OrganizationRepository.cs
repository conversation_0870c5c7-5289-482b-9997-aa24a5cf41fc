﻿using Compass.Common.Data;
using Compass.Common.DTOs.Organization;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Runtime.CompilerServices;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class OrganizationRepository : IOrganizationRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public OrganizationRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<Organization?> GetOrganizationAsync(long? organizationId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Organizations.FirstOrDefaultAsync(o => o.Id == organizationId);
            }
        }

        public async Task<Organization?> UpdateOrganizationAsync(long? id, Organization organization)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (organization is null)
            {
                throw new ArgumentNullException(nameof(organization));
            }

            if (organization.Id != id)
            {
                throw new ArgumentException("Organization id does not match the id in the organization object.");
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                var organizationToUpdate = await _dbContext.Organizations.FindAsync(id);

                if (organizationToUpdate is null)
                {
                    return null;
                }
                else
                {
                    // Get the authentication state
                    var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                    var user = authState.User;
                    var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                    organizationToUpdate.Name = organization.Name;
                    organizationToUpdate.ContactEmail = organization.ContactEmail;
                    organizationToUpdate.ModTs = DateTime.Now;
                    organizationToUpdate.ModId = userId;
                    organizationToUpdate.Address1 = organization.Address1;
                    organizationToUpdate.Address2 = organization.Address2;
                    organizationToUpdate.City = organization.City;
                    organizationToUpdate.Country = organization.Country;
                    organizationToUpdate.State = organization.State;
                    organizationToUpdate.ZipCode = organization.ZipCode;
                    organizationToUpdate.ContactFirstName = organization.ContactFirstName;
                    organizationToUpdate.ContactLastName = organization.ContactLastName;
                    organizationToUpdate.ContactPhone = organization.ContactPhone;
                    organizationToUpdate.ContactFax = organization.ContactFax;
                    organizationToUpdate.fax = organization.fax;
                    organizationToUpdate.MfaRequired = organization.MfaRequired;

                    _dbContext.Organizations.Update(organizationToUpdate);
                    await _dbContext.SaveChangesAsync();

                    return organizationToUpdate;
                }
            }
        }

        public async Task<List<OrganizationListDisplayDto>> GetOrganizations(PageQuery pageQuery)
        {
            string sqlQuery = "SELECT id, name, contact_email FROM cmn_organizations ";
            string searchClause = "WHERE " +
                "    ( " +
                "        name LIKE {0} " +
                "        OR contact_email LIKE {0} " +
                "        OR contact_first_name LIKE {0} " +
                "        OR contact_last_name LIKE {0} " +
                "        OR [state] LIKE {0} " +
                "        OR address_1 LIKE {0} " +
                "        OR address_2 LIKE {0} " +
                "        OR city LIKE {0} " +
                "        OR zip_code LIKE {0} " +
                "        OR contact_first_name + ' ' + contact_last_name LIKE {0} " +
                "    ) ";
            string pageInfo = "ORDER BY id OFFSET {1} ROWS FETCH NEXT {2} ROWS ONLY";

            sqlQuery += searchClause;
            sqlQuery += pageInfo;

            string searchText = "%" + pageQuery.QueryText + "%";
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<OrganizationListDisplayDto> resultList = await _dbContext.Organizations
                .FromSqlRaw(sqlQuery, searchText, pageOffset, pageSize)
                .Select(o => new OrganizationListDisplayDto
                {
                    Id = o.Id,
                    OrgName = o.Name,
                    OrgContactEmail = o.ContactEmail
                })
                .ToListAsync();

                return resultList;
            }
        }

        public async Task<int> GetOrganizationCount(string queryText)
        {
            string sqlQuery = @"SELECT COUNT(id) AS Value 
                                FROM cmn_organizations 
                                WHERE 
                                ( 
                                    name LIKE {0} 
                                    OR contact_email LIKE {0} 
                                    OR contact_first_name LIKE {0} 
                                    OR contact_last_name LIKE {0}  
                                    OR [state] LIKE {0} 
                                    OR address_1 LIKE {0} 
                                    OR address_2 LIKE {0} 
                                    OR city LIKE {0} 
                                    OR zip_code LIKE {0} 
                                    OR contact_first_name + ' ' + contact_last_name LIKE {0} 
                                )";

            string searchText = "%" + queryText + "%";
            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, searchText);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();
                return count;
            }
        }

        public async Task<Organization> AddOrganizationAsync(Organization organization)
        {
            if (organization is null)
            {
                throw new ArgumentNullException(nameof(organization));
            }

            // Get the authentication state
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            organization.ModId = userId;
            organization.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.Organizations.AddAsync(organization);
                await _dbContext.SaveChangesAsync();
            }

            return organization;
        }
    }
}
