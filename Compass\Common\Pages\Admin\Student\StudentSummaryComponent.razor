﻿@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Models
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject UserAccessor UserAccessor
@inject IStudentService StudentService
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<div class="container mt-4">
    <LoaderComponent IsLoading="isLoading">
        <div class="card">
            <div class="card-header">
                <h3>Student Information</h3>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Full Name:</div>
                    <div class="col-md-8">@fullName</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">School ID:</div>
                    <div class="col-md-8">@schoolId</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Gender:</div>
                    <div class="col-md-8">@(gender == "M" ? "Male" : gender == "F" ? "Female" : gender)</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Birth Date:</div>
                    <div class="col-md-8">@(birthDate?.ToString("d"))</div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4 fw-bold">Enroll Date:</div>
                    <div class="col-md-8">@(enrollDate?.ToString("d"))</div>
                </div>
            </div>
        </div>

        <div>
            @if (hasLAPLicense)
            {
                <button @onclick="OnLAPOptionClick">LAP</button>
            }
            @if (hasDECALicense)
            {
                <button @onclick="OnDECAOptionClick">DECA</button>
            }
            @if (hasC4LLicense)
            {
                <button @onclick="OnC4LOptionClick">C4L</button>
            }
        </div>
    </LoaderComponent>
</div>
