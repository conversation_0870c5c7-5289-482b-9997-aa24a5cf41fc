﻿@using Compass.C4L.DTOs
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))
<div class="component-content-wrapper">

    <h2 class="page-title">Game Center</h2>

    @if (!string.IsNullOrEmpty(studentName))
    {
        <p class="text-center font-weight-500">Welcome, @studentName!</p>
    }
 
    <div class="c4l-table-scroll-wrapper">
        @if (!c4lClassroomSelectionList.Any())
        {
            <div class="info-message-wrapper">
                <p>There are no C4l classrooms for this student.</p>
            </div>
        }
        else
        {
            @if (c4lClassroomSelectionList.Count > 1)
            {
                <div class="status-message-wrapper">
                    <h3 class="text-center" data-color="c4l-primary-purple">Please select a classroom to play a game:</h3>

                    <div class="c4l-form classroom-selection-form">
                        @foreach (C4LClassroomSelectionDisplayDto dto in c4lClassroomSelectionList)
                        {
                            <div class="form-checkbox-label classroom-option">
                                <input type="radio" class="form-check-input" id="<EMAIL>" name="classroomSelection" @onchange="() => SelectC4LClassroom(dto)" />
                                <label for="<EMAIL>" class="classroom-option-label">
                                    <span class="font-weight-600" data-color="c4l-primary-purple">@dto.StudentGroupName</span>
                                    <span class="font-weight-400" data-color="c41-dark-gray">@dto.StartDate</span>
                                </label>
                            </div>
                        }
                    </div>
                </div>
            }

            @if (!string.IsNullOrWhiteSpace(GameUrl))
            {
                <div class="buttons-wrapper centered-buttons">
                    <a href="@GameUrl" target="_blank" class="c4l-button c4l-primary-button" aria-label="Launch Game in new window">
                        Launch Game
                    </a>
                </div>
            }
        }
    </div>
</div>