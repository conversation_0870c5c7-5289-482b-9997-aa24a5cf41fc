﻿@page "/account/login-with-recovery-code"

@using System.ComponentModel.DataAnnotations
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity

@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> User<PERSON>anager
@inject IdentityRedirectManager RedirectManager
@inject ILogger<LoginWithRecoveryCode> Logger

<PageTitle>Recovery Code Verification | C4L</PageTitle>

<h1 class="page-title mb-3">Recovery code verification</h1>

<StatusMessage Message="@message" AlertType="@alertType" />
<p class="text-center">You have requested to log in with a recovery code. This login will not be remembered until you provide an authenticator app code at log in or disable 2FA and log in again.</p>

<EditForm Model="Input" FormName="login-with-recovery-code" OnValidSubmit="OnValidSubmitAsync" method="post" class="c4l-form mt-5">
    <DataAnnotationsValidator />
    <ValidationSummary class="text-danger" role="alert" />

    <h3 class="c4l-form-heading">Enter recovery code</h3>
    <div class="form-floating mb-3">
        <InputText @bind-Value="Input.RecoveryCode" class="form-control" autocomplete="off" placeholder="RecoveryCode" />
        <label for="recovery-code" class="form-label">Recovery Code</label>
        <ValidationMessage For="() => Input.RecoveryCode" class="text-danger" />
    </div>

    <button type="submit" class="c4l-button c4l-form-button c4l-primary-button">Submit code</button>
</EditForm>

@code {
    private string? message;
    private string? alertType = "danger";
    private ApplicationUser user = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        // Ensure the user has gone through the username & password screen first
        user = await SignInManager.GetTwoFactorAuthenticationUserAsync() ??
            throw new InvalidOperationException("Unable to load two-factor authentication user.");
    }

    private async Task OnValidSubmitAsync()
    {
        var recoveryCode = Input.RecoveryCode.Replace(" ", string.Empty);

        var result = await SignInManager.TwoFactorRecoveryCodeSignInAsync(recoveryCode);

        var userId = await UserManager.GetUserIdAsync(user);

        if (result.Succeeded)
        {
            Logger.LogInformation("User with ID '{UserId}' logged in with a recovery code.", userId);
            alertType = "success";
            RedirectManager.RedirectTo(ReturnUrl);
        }
        else if (result.IsLockedOut)
        {
            Logger.LogWarning("User account locked out.");
            RedirectManager.RedirectTo("account/lockout");
        }
        else
        {
            Logger.LogWarning("Invalid recovery code entered for user with ID '{UserId}' ", userId);
            message = "Error: Invalid recovery code entered.";
        }
    }

    private sealed class InputModel
    {
        [Required]
        [DataType(DataType.Text)]
        [Display(Name = "Recovery Code")]
        public string RecoveryCode { get; set; } = "";
    }
}
