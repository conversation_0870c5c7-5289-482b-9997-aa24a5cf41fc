﻿﻿using Compass.DECA.Interfaces.Repositories;
using Compass.DECA.Interfaces.Services;
using Compass.Deca.Models;
using Serilog;

namespace Compass.DECA.Services
{
    public class DecaQuestionService : IDecaQuestionService
    {
        private readonly IDecaQuestionRepository _repository;

        public DecaQuestionService(IDecaQuestionRepository repository)
        {
            _repository = repository;
        }

        public async Task<DecaQuestion?> GetByIdAsync(long id)
        {
            try
            {
                return await _repository.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting question {Id}", id);
                throw;
            }
        }

        public async Task<List<DecaQuestion>> GetByRecordFormAsync(string recordForm)
        {
            try
            {
                return await _repository.GetByRecordFormAsync(recordForm);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting questions for record form {RecordForm}", recordForm);
                throw;
            }
        }

        public async Task<List<DecaQuestion>> GetAllAsync()
        {
            try
            {
                return await _repository.GetAllAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting all questions");
                throw;
            }
        }

        public async Task<DecaQuestion?> GetByRecordFormAndQuestionNumberAsync(string recordForm, int questionNumber)
        {
            try
            {
                return await _repository.GetByRecordFormAndQuestionNumberAsync(recordForm, questionNumber);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting question for record form {RecordForm} and question number {QuestionNumber}", recordForm, questionNumber);
                throw;
            }
        }

        public async Task<List<string>> GetDistinctRecordFormsAsync()
        {
            try
            {
                return await _repository.GetDistinctRecordFormsAsync();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting distinct record forms");
                throw;
            }
        }

        public async Task<List<DecaQuestion>> GetByProtectiveFactorScaleAsync(string protectiveFactorScale)
        {
            try
            {
                return await _repository.GetByProtectiveFactorScaleAsync(protectiveFactorScale);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting questions for protective factor scale {ProtectiveFactorScale}", protectiveFactorScale);
                throw;
            }
        }
    }
}
