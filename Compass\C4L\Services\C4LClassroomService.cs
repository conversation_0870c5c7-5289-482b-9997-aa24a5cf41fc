using Compass.C4L.DTOs;
using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Compass.Common.Models;
using Compass.Common.Resources;
using Serilog;

namespace Compass.C4L.Services
{
    public class C4LClassroomService : IC4LClassroomService
    {
        private readonly IC4LClassroomRepository _c4LClassroomRepository;
        private readonly ISchoolYearRepository _schoolYearRepository;
        private readonly IStudentGroupRepository _studentGroupRepository;
        private readonly ILicensePoolService _licensePoolService;
        private readonly IC4LLicenseStudentGroupRepository _c4LLicenseStudentGroupRepository;
        private readonly IC4LNonContactDayRepository _nonContactDayRepository;

        public C4LClassroomService(IC4LClassroomRepository c4LClassroomRepositoryrepository,
                                    ISchoolYearRepository schoolYearRepository,
                                    IStudentGroupRepository studentGroupRepository,
                                    ILicensePoolService licensePoolService,
                                    IC4LLicenseStudentGroupRepository c4LLicenseStudentGroupRepository,
                                    IC4LNonContactDayRepository nonContactDayRepository)
        {
            _c4LClassroomRepository = c4LClassroomRepositoryrepository;
            _schoolYearRepository = schoolYearRepository;
            _studentGroupRepository = studentGroupRepository;
            _licensePoolService = licensePoolService;
            _c4LLicenseStudentGroupRepository = c4LLicenseStudentGroupRepository;
            _nonContactDayRepository = nonContactDayRepository;
        }

        public async Task<C4LClassroom?> GetC4LClassroomByIdAsync(long id)
        {
            try
            {
                return await _c4LClassroomRepository.GetC4LClassroomByIdAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LClassroomService.GetC4LClassroomByIdAsync for ID {Id}", id);
                throw;
            }
        }

        public async Task<C4LClassroom?> GetByStudentGroupIdAsync(long studentGroupId)
        {
            try
            {
                return await _c4LClassroomRepository.GetByStudentGroupIdAsync(studentGroupId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LClassroomService.GetC4LClassroomByClassroomIdAsync for StudentGroupId {StudentGroupId}", studentGroupId);
                throw;
            }
        }

        public async Task<IEnumerable<C4LClassroom>> GetC4LClassroomByOrganizationIdAsync(long organizationId)
        {
            try
            {
                return await _c4LClassroomRepository.GetC4LClassroomByOrganizationIdAsync(organizationId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LClassroomService.GetC4LClassroomByOrganizationIdAsync for OrganizationId {OrganizationId}", organizationId);
                throw;
            }
        }

        public async Task<C4LClassroom?> UpdateC4LClassroomAsync(long? c4l_classroomId, C4LClassroom? classroom)
        {
            if (c4l_classroomId == null)
            {
                throw new ArgumentNullException(nameof(c4l_classroomId));
            }

            if (classroom == null)
            {
                throw new ArgumentNullException(nameof(classroom));
            }

            try
            {
                return await _c4LClassroomRepository.UpdateC4LClassroomAsync(c4l_classroomId, classroom);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LClassroomService.UpdateC4LClassroomAsync for Classroom {Id}", classroom.Id);
                throw;
            }
        }

        public async Task<C4LClassroomCreateResultDto> CreateC4LClassroomAsync(C4LClassroom? classroom)
        {
            if (classroom == null)
            {
                throw new ArgumentNullException(nameof(classroom));
            }

            C4LClassroomCreateResultDto ret = new C4LClassroomCreateResultDto();
            try
            {
                long? organizationId = classroom.OrganizationId;
                long? studentGroupId = classroom.StudentGroupId;
                C4LClassroomAccessDto c4lAccess = await GetCurrentByStudentGroupIdAsync(organizationId, studentGroupId);
                C4LClassroom? existingClassroom = c4lAccess.C4LClassroom;
                if (existingClassroom != null)
                {
                    //This should never happen but this is a security check to prevent data getting messed up
                    ret.C4LClassroom = null;
                    ret.ResultError = "C4L Classroom already exists for this school year in this Student Group";

                    return ret;
                }

                //Get Current school year to set in
                StudentGroup? studentGroup = await _studentGroupRepository.GetStudentGroupAsync(studentGroupId);
                if (studentGroup == null)
                {
                    throw new Exception("Student Group does not exist");
                }
                long? siteId = studentGroup.SiteId;

                SchoolYear? currentSchoolYear = await _schoolYearRepository.GetCurrentSchoolYear(organizationId, siteId);
                if (currentSchoolYear == null)
                {
                    throw new Exception("No current School Year");
                }
                long schoolYearId = currentSchoolYear.Id;
                classroom.SchoolYearId = schoolYearId;

                long? licensePoolId = await _licensePoolService.GetAvailableLicensePoolId(organizationId, CompassResource.C4L);
                if (licensePoolId == null)
                {
                    ret.C4LClassroom = null;
                    ret.ResultError = "Not enough licenses";

                    return ret;
                }

                C4LClassroom? createdC4lClassroom = await _c4LClassroomRepository.CreateC4LClassroomAsync(classroom);
                long? c4l_classroomId = createdC4lClassroom.Id;
                ret.C4LClassroom = createdC4lClassroom;

                await _licensePoolService.IncrementLicense(organizationId, c4l_classroomId, licensePoolId, _c4LLicenseStudentGroupRepository);

                return ret;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LClassroomService.CreateC4LClassroomAsync for OrganizationId {OrganizationId}", classroom.OrganizationId);
                throw;
            }
        }

        public async Task<C4LClassroomAccessDto> GetCurrentByStudentGroupIdAsync(long? organizationId, long? studentGroupId)
        {
            try
            {
                C4LClassroomAccessDto ret = new C4LClassroomAccessDto();

                bool hasC4LAccess = await _licensePoolService.CheckActiveLicense(organizationId, CompassResource.C4L);
                ret.HasC4LAccess = hasC4LAccess;
                ret.C4LClassroom = null;

                if (hasC4LAccess && studentGroupId != null)
                {
                    C4LClassroom? c4lClassroom = await _c4LClassroomRepository.GetCurrentByStudentGroupIdAsync(organizationId, studentGroupId);
                    ret.C4LClassroom = c4lClassroom;
                }

                return ret;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LClassroomService.GetCurrentByStudentGroupIdAsync for studentGroupId {studentGroupId}", studentGroupId);
                throw;
            }
        }

        public async Task<bool> GetStudentC4LAccessAsync(long? organizationId, long? studentId)
        {
            try
            {
                bool studentHasAccess = false;

                bool hasC4LAccess = await _licensePoolService.CheckActiveLicense(organizationId, CompassResource.C4L);

                if (hasC4LAccess && studentId != null)
                {
                    int accessCount = await _c4LClassroomRepository.GetCurrentClassroomsByAssignedStudentIdCountAsync(organizationId, studentId);

                    if (accessCount > 0)
                    {
                        studentHasAccess = true;
                    }
                }

                return studentHasAccess;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LClassroomService.GetStudentC4LAccessAsync for studentId {studentId}", studentId);
                throw;
            }
        }

        public async Task<List<C4LClassroomSelectionDisplayDto>> GetStudentC4LClassroomsAsync(long? organizationId, long? studentId)
        {
            try
            {
                List<C4LClassroomSelectionDisplayDto> c4lClassroomList = new List<C4LClassroomSelectionDisplayDto>();

                bool hasC4LAccess = await _licensePoolService.CheckActiveLicense(organizationId, CompassResource.C4L);

                if (hasC4LAccess && studentId != null)
                {
                    c4lClassroomList = await _c4LClassroomRepository.GetCurrentClassroomsByAssignedStudentIdAsync(organizationId, studentId);
                }

                return c4lClassroomList;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LClassroomService.GetStudentC4LAccessAsync for studentId {studentId}", studentId);
                throw;
            }
        }

        public async Task<List<C4LNonContactDay>> GetNonContactDaysByClassroomIdAsync(long c4l_classroomId)
        {
            try
            {
                return (await _nonContactDayRepository.GetByClassroomIdAsync(c4l_classroomId)).ToList();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LClassroomService.GetNonContactDaysByClassroomIdAsync for classroomId {classroomId}", c4l_classroomId);
                throw;
            }
        }
    }
}
