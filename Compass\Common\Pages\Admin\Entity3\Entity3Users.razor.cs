﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.User;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Services;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity3
{
    public partial class Entity3Users : IDisposable
    {
        private List<UserListDisplayDto> userResults = new();

        private bool IsAssignBoxVisible = false;
        private bool IsInviteBoxVisible = false;
        private bool IsUnAssignDialogVisible = false;
        private bool isLoading = true;
        private bool noSearchResults = false;

        private int maxPages;
        private int currentPage;

        private long? currentOrganizationId;
        private long? currentEntityId;
        private int assignLevel = (int)IUserService.AssignLevels.Entity3Level;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;
        private string entity3HierarchyName = string.Empty;
        private string entity3Name = string.Empty;

        private string unAssignUserId = string.Empty;
        private string unAssignMessage = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            unAssignUserId = string.Empty;
            searchText = string.Empty;
            currentSearchText = string.Empty;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                currentEntityId = commonSessionData.CurrentEntity3Id;
                entity3HierarchyName = commonSessionData.Entity3Hierarchy;
                entity3Name = commonSessionData.SelectedEntityName;

                currentPage = 1;
                maxPages = 0;

                await GetUserPage();
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task GetUserPage()
        {
            UserListAction action = new UserListAction();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            action.PageQuery = pageQuery;
            if (currentOrganizationId != null)
            {
                action.OrganizationId = currentOrganizationId;
                action.EntityId = currentEntityId;

                KaplanPageable<UserListDisplayDto> currentPage = await UserService.GetEntity3UserDisplayPages(action);

                userResults = currentPage.PageContent;
                maxPages = userResults.Any() ? currentPage.MaxPages : 1;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && userResults.Count == 0;

                StateHasChanged();
            }

            isLoading = false;
            StateHasChanged();
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetUserPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetUserPage();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetUserPage();
        }

        protected void OnAssignClicked()
        {
            IsAssignBoxVisible = true;
            IsInviteBoxVisible = false;
        }

        protected void OnInviteClicked()
        {
            IsInviteBoxVisible = true;
            IsAssignBoxVisible = false;
        }

        private void HideDialogBoxes()
        {
            IsAssignBoxVisible = false;
            IsInviteBoxVisible = false;
        }

        private async Task AssignUser(string userId)
        {
            if (userId != string.Empty)
            {
                CreateUserLinkAction action = new CreateUserLinkAction
                {
                    UserId = userId,
                    EntityId = currentEntityId,
                    OrganizationId = currentOrganizationId
                };

                await Entity3Service.AssignEntity3User(action);

                currentPage = 1;
                await GetUserPage();
            }
        }

        protected async Task OnAssignResult(string userId)
        {
            HideDialogBoxes();
            await AssignUser(userId);
        }

        protected async Task OnInviteUserResult(string userId)
        {
            HideDialogBoxes();
            await AssignUser(userId);
        }

        protected void UnAssignUser(string userId, string? userName)
        {
            unAssignMessage = userName;
            unAssignUserId = userId;
            IsUnAssignDialogVisible = true;
        }

        protected async Task OnUnAssignDialogResult(bool result)
        {
            IsUnAssignDialogVisible = false;

            if (result)
            {
                if (unAssignUserId != string.Empty)
                {
                    CreateUserLinkAction action = new CreateUserLinkAction
                    {
                        UserId = unAssignUserId,
                        EntityId = currentEntityId,
                        OrganizationId = currentOrganizationId
                    };

                    await Entity3Service.UnAssignEntity3User(action);

                    unAssignMessage = string.Empty;
                    unAssignUserId = string.Empty;
                    currentPage = 1;
                    await GetUserPage();
                }
            }
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
