using Compass.C4L.DTOs;
using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;

namespace Compass.C4L.Services
{
    public class C4LLessonPreparationService : IC4LLessonPreparationService
    {
        private readonly IC4LLessonPreparationRepository _repository;
        private readonly IC4LNonContactDayRepository _nonContactDayRepository;
        private readonly IC4LClassroomService _classroomService;

        public C4LLessonPreparationService(
            IC4LLessonPreparationRepository repository,
            IC4LNonContactDayRepository nonContactDayRepository,
            IC4LClassroomService classroomService)
        {
            _repository = repository;
            _nonContactDayRepository = nonContactDayRepository;
            _classroomService = classroomService;
        }

        public async Task<List<C4LLessonPreparation>> GetPreparationsByLessonAsync(string language, int unit, int week, int day)
        {
            return await _repository.GetPreparationsByLessonAsync(language, unit, week, day);
        }

        public async Task<C4LLessonPreparation?> GetPreparationAsync(string language, int unit, int week, int day, int lessonTypeSequence, int titleSequence)
        {
            string lang = language;
            switch (language)
            {
                case "en":
                    lang = "English";
                    break;
                case "es":
                    lang = "Spanish";
                    break;
                default:
                    lang = language;
                    break;
            }

            return await _repository.GetPreparationAsync(lang, unit, week, day, lessonTypeSequence, titleSequence);
        }

        public async Task<List<LessonPreparationWithTitleDto>> RetrievePreparationsWithTitle(string language, int unit, int week, long? c4l_classroomId = null)
        {
            List<LessonPreparationWithTitleDto> allPreparations = new List<LessonPreparationWithTitleDto>();

            // Simply retrieve preparations for the specified unit and week
            for (int day = 1; day <= 5; day++)
            {
                // Get preparations for this day
                List<LessonPreparationWithTitleDto> dayPreparations = await _repository.RetrievePreparationsWithTitle(
                    language, unit, week, day, c4l_classroomId);

                allPreparations.AddRange(dayPreparations);
            }

            return allPreparations.OrderBy(p => p.Unit)
                                 .ThenBy(p => p.Week)
                                 .ThenBy(p => p.Day)
                                 .ThenBy(p => p.LessonTypeSequence)
                                 .ThenBy(p => p.TitleSequence)
                                 .ToList();
        }
    }
}
