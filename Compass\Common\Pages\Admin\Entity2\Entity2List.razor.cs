﻿using Compass.Common.Data;
using Compass.Common.DTOs.Entity2;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity2
{
    public partial class Entity2List : IDisposable
    {
        private List<Entity2ListDisplayDto> entity2Results = new();
        private string entity2Hierarchy = string.Empty;
        private string entity1Hierarchy = string.Empty;

        private int maxPages;
        private int currentPage;

        private long? currentOrganizationId;
        private long? currentEntity1Id;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        private bool noSearchResults = false;
        private bool isLoading = true;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
                if (commonSessionData != null)
                {
                    currentOrganizationId = commonSessionData.CurrentOrganizationId;
                }
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            isLoading = true;
            searchText = string.Empty;
            currentSearchText = string.Empty;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                entity2Hierarchy = commonSessionData.Entity2Hierarchy;
                entity1Hierarchy = commonSessionData.Entity1Hierarchy;

                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                currentEntity1Id = commonSessionData.CurrentEntity1Id;

                currentPage = 1;
                maxPages = 0;
                await GetEntity2Page();
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task GetEntity2Page()
        {
            Entity2ListAction action = new();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            action.pageQuery = pageQuery;
            if (currentOrganizationId != null)
            {
                action.organizationId = currentOrganizationId;
                action.userId = _currentUserId;
                action.entity1Id = currentEntity1Id;

                KaplanPageable<Entity2ListDisplayDto> currentPage = await Entity2Service.GetEntity2Page(action);

                entity2Results = currentPage.PageContent;
                maxPages = currentPage.MaxPages;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && entity2Results.Count == 0;
                isLoading = false;

                StateHasChanged();
            }
        }

        protected async Task OnEntity2Selected(Entity2ListDisplayDto entity)
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                commonSessionData.CurrentEntity2Id = entity.Id;
                commonSessionData.CurrentEntity1Id = entity.Entity1Id;

                if (entity.Name != null)
                {
                    commonSessionData.SelectedEntityName = entity.Name;
                }
                else
                {
                    commonSessionData.SelectedEntityName = string.Empty;
                }

                if (_currentUser != null)
                {
                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();

                    NavigationManager.NavigateTo($"/entity2");
                }
            }
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetEntity2Page();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetEntity2Page();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetEntity2Page();
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
