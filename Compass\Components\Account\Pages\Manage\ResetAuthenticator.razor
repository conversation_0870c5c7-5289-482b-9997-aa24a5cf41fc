﻿@page "/account/reset-authenticator-key"
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity

@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager
@inject ILogger<ResetAuthenticator> Logger

<PageTitle>Reset Authenticator Key | C4L</PageTitle>

<StatusMessage Message="@message" AlertType="@alertType" />

<form @formname="reset-authenticator" @onsubmit="OnSubmitAsync" method="post" class="c4l-form">
    <AntiforgeryToken />

    <h3 class="c4l-form-heading">Reset authenticator key</h3>

    <div class="alert alert-warning" role="alert">
        <p><strong>If you reset your authenticator key your authenticator app will not work until you reconfigure it.</strong></p>

        <p>This process disables 2FA until you verify your authenticator app. If you do not complete your authenticator app configuration you may lose access to your account.</p>
    </div>

    <button type="submit" class="c4l-button c4l-form-button c4l-danger-button">Reset authenticator key</button>
</form>

@code {
    private string? message;
    private string? alertType = "success";

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    private async Task OnSubmitAsync()
    {
        var user = await UserAccessor.GetRequiredUserAsync(HttpContext);
        await UserManager.SetTwoFactorEnabledAsync(user, false);
        await UserManager.ResetAuthenticatorKeyAsync(user);
        var userId = await UserManager.GetUserIdAsync(user);

        message = $"User with ID '{userId}' has reset their authentication app key.";

        await SignInManager.RefreshSignInAsync(user);

        RedirectManager.RedirectToWithStatus(
            "Account/Manage/EnableAuthenticator",
            "Your authenticator app key has been reset, you will need to configure your authenticator app using the new key.",
            HttpContext);
    }
}
