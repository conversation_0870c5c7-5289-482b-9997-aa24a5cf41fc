﻿@using Compass.Common.DTOs.StudentGroup
@using Compass.Common.Interfaces.Services
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserSessionService UserSessionService
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject UserAccessor UserAccessor
@inject IStudentService StudentService

<div class="dialog-overlay" style="@(IsVisible ? "display: flex;" : "display: none;")">
    <div class="modal-scroll-wrapper">
        <div class="dialog-box assign-modal-wrapper">
            <div class="search-input-wrapper">
                <div class="form-input-wrapper">
                    <input type="text" class="form-control" placeholder="Search Name or Email" aria-label="Search" @bind="searchText" />
                </div>

                <div class="search-buttons-wrapper">
                    <button class="c4l-button c4l-primary-button" type="button" @onclick="OnSearch">Search</button>
                </div>
            </div>

            <div class="c4l-table-scroll-wrapper">
                <div class="c4l-table-wrapper">
                    <div class="c4l-table-headings-wrapper">
                        <h6 class="c4l-table-heading">@studentGroupHierarchy</h6>
                        <h6 class="c4l-table-heading">@entity1Hierarchy</h6>
                        <h6 class="c4l-table-heading">@entity2Hierarchy</h6>
                        <h6 class="c4l-table-heading">@entity3Hierarchy</h6>
                        <h6 class="c4l-table-heading">@siteHierarchy</h6>
                    </div>

                    @foreach (StudentGroupListDisplayDto studentGroup in unAssignedStudentGroupResults)
                    {
                        <button name="entity 3 select button" type="button" title="Select @studentGroup.Name" @onclick="() => OnStudentGroupSelected(studentGroup)">
                            <div class="c4l-table-result-wrapper">
                                <p class="c4l-table-result-item">@studentGroup.Name</p>
                                @if (@studentGroup.Entity1Name != null && studentGroup.Entity1Name != string.Empty)
                                {
                                    <p class="c4l-table-result-item">@studentGroup.Entity1Name</p>
                                }
                                @if (@studentGroup.Entity2Name != null && @studentGroup.Entity2Name != string.Empty)
                                {
                                    <p class="c4l-table-result-item">@studentGroup.Entity2Name</p>
                                }
                                @if (@studentGroup.Entity3Name != null && @studentGroup.Entity3Name != string.Empty)
                                {
                                    <p class="c4l-table-result-item">@studentGroup.Entity3Name</p>
                                }
                                @if (@studentGroup.SiteName != null && @studentGroup.SiteName != string.Empty)
                                {
                                    <p class="c4l-table-result-item">@studentGroup.SiteName</p>
                                }
                            </div>
                        </button>
                    }
                </div>
            </div>

            <div class="c4l-pagination-wrapper">
                <div class="c4l-pagination-buttons-wrapper">
                    <div class="buttons-wrapper">
                        <button class="c4l-button c4l-ghost-primary c4l-pagination-button"
                                @onclick="() => OnPreviousClicked()"
                                disabled="@(currentPage <= 1)"
                                type="button">
                            Previous
                        </button>

                        <button class="c4l-button c4l-ghost-primary c4l-pagination-button"
                                @onclick="() => OnNextClicked()"
                                disabled="@(currentPage >= maxPages)"
                                type="button">
                            Next
                        </button>

                        <button type="button" class="c4l-button c4l-tertiary-button c4l-pagination-button" @onclick="() => OnCancelClick()">Cancel</button>
                    </div>
                </div>

                <div class="page-count-wrapper font-weight-500">
                    <span class="current-page-number">@currentPage</span> of @maxPages
                </div>
            </div>
        </div>
    </div>
</div>
