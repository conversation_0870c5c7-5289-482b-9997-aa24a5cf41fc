﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Common.Models
{
    [Table("cmn_students")]
    public class Student
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime? ModTs { get; set; }

        [Column("first_name")]
        public string FirstName { get; set; } = string.Empty;

        [Column("middle_name")]
        public string MiddleName { get; set; } = string.Empty;

        [Column("last_name")]
        public string LastName { get; set; } = string.Empty;

        [Column("school_id")]
        public string SchoolId { get; set; } = string.Empty;

        [Column("gender")]
        public string Gender { get; set; } = string.Empty;

        [Column("birth_date")]
        public DateTime? BirthDate { get; set; }

        [Column("enroll_date")]
        public DateTime? EnrollDate { get; set; }

        [Column("hispanic_latino")]
        public string HispanicLatino { get; set; } = string.Empty;

        [Column("primary_language_id")]
        public long? PrimaryLanguageId { get; set; }

        [Column("school_lunch")]
        public string SchoolLunch { get; set; } = string.Empty;

        [Column("dual_language")]
        public string DualLanguage { get; set; } = string.Empty;

        [Column("iepifsp")]
        public string IepIfsp { get; set; } = string.Empty;

        [Column("disability")]
        public string Disability { get; set; } = string.Empty;
    }
}
