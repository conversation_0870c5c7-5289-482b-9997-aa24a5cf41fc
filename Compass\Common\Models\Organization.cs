﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Common.Models
{
    [Table("cmn_organizations")]
    public class Organization
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }
        [Column("mod_id")]
        public string ModId { get; set; }
        [Required]
        [Column("name")]
        public string Name { get; set; } = string.Empty;
        [Column("mod_ts")]
        public DateTime ModTs { get; set; }
        [Column("contact_email")]
        public string? ContactEmail { get; set; }
        [Column("address_1")]
        public string? Address1 { get; set; }
        [Column("address_2")]
        public string? Address2 { get; set; }
        [Column("city")]
        public string? City { get; set; }
        [Column("zip_code")]
        public string? ZipCode { get; set; }
        [Column("country")]
        public string? Country { get; set; }
        [Column("state")]
        public string? State { get; set; }
        [Column("contact_first_name")]
        public string? ContactFirstName { get; set; }
        [Column("contact_last_name")]
        public string? ContactLastName { get; set; }
        [Column("contact_phone")]
        public string? ContactPhone { get; set; }
        [Column("contact_fax")]
        public string? ContactFax { get; set; }
        [Column("fax")]
        public string? fax { get; set; }
        [Column("mfa_required")]
        public string? MfaRequired { get; set; }
    }
}
