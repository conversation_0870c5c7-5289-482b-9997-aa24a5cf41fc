using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.C4L.Repositories
{
    public class C4LLessonPreparationCompletedRepository : IC4LLessonPreparationCompletedRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public C4LLessonPreparationCompletedRepository(
            IDbContextFactory<ApplicationDbContext> contextFactory,
            AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<C4LLessonPreparationCompleted?> GetByPreparationIdAndSchoolYearAsync(long preparationId, int schoolYear)
        {
            using (ApplicationDbContext dbContext = _contextFactory.CreateDbContext())
            {
                return await dbContext.C4LLessonPreparationCompleteds
                    .FirstOrDefaultAsync(c => c.LessonPreparationId == preparationId && c.Schoolyear == schoolYear);
            }
        }

        public async Task<C4LLessonPreparationCompleted> CreateAsync(C4LLessonPreparationCompleted completedPreparation)
        {
            // Get the current user ID for the mod_id field
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string userId = user.FindFirstValue(ClaimTypes.NameIdentifier) ?? string.Empty;

            // Set the ModId and ModTs
            completedPreparation.ModId = userId;
            completedPreparation.ModTs = DateTime.Now;

            // Ensure organization ID is set
            if (completedPreparation.OrganizationId <= 0)
            {
                throw new ArgumentException("Organization ID must be set", nameof(completedPreparation));
            }

            using (ApplicationDbContext dbContext = _contextFactory.CreateDbContext())
            {
                await dbContext.C4LLessonPreparationCompleteds.AddAsync(completedPreparation);
                await dbContext.SaveChangesAsync();
                return completedPreparation;
            }
        }

        public async Task<C4LLessonPreparationCompleted> UpdateAsync(C4LLessonPreparationCompleted completedPreparation)
        {
            // Ensure organization ID is set
            if (completedPreparation.OrganizationId <= 0)
            {
                throw new ArgumentException("Organization ID must be set", nameof(completedPreparation));
            }

            // Get the current user ID for the mod_id field
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string userId = user.FindFirstValue(ClaimTypes.NameIdentifier) ?? string.Empty;

            // Update ModId and ModTs
            completedPreparation.ModId = userId;
            completedPreparation.ModTs = DateTime.Now;

            using (ApplicationDbContext dbContext = _contextFactory.CreateDbContext())
            {
                dbContext.C4LLessonPreparationCompleteds.Update(completedPreparation);
                await dbContext.SaveChangesAsync();
                return completedPreparation;
            }
        }
    }
}
