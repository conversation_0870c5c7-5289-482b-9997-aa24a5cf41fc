.deca-rating-summary-title {
    text-align: center;
    margin-block: 2rem 4rem;
    color: var(--c4l-primary-purple);
}

.loading-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 20rem;
}

.ratings-container {
    width: 100%;
    background-color: var(--white);
    border: 1px solid var(--c4l-primary-purple);
    border-radius: 0.25rem;
    overflow: auto;
    box-shadow: 0 0.25rem 0.375rem rgba(0, 0, 0, 0.1);
}

.ratings-header {
    display: flex;
    background-color: var(--c4l-primary-purple);
    color: var(--white);
    font-weight: 600;
    border-bottom: 1px solid var(--c4l-primary-purple);
}

.ratings-header .header-item {
    flex: 1;
    padding: 1rem 0.75rem;
    text-align: center;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 8rem;
}

.ratings-header .header-item:last-child {
    border-right: none;
}

.ratings-content {
    display: flex;
    flex-direction: column;
}

.rating-row {
    display: flex;
    border-bottom: 1px solid var(--neutral-200);
    transition: background-color 0.2s ease;
}

.rating-row:hover {
    background-color: var(--neutral-100);
}

.rating-row:last-child {
    border-bottom: none;
}

.rating-item {
    flex: 1;
    padding: 0.75rem;
    text-align: center;
    border-right: 1px solid var(--neutral-200);
    min-width: 8rem;
    word-wrap: break-word;
}

.rating-item:last-child {
    border-right: none;
}

.no-data-message {
    max-width: 45rem;
    margin: 2rem auto;
    text-align: center;
    border-radius: 0.25rem;
    padding: 1.5rem;
}

.no-data-message p {
    margin: 0;
    margin-left: 0.5rem;
    display: inline;
}

.no-data-message i {
    vertical-align: middle;
}

@media (max-width: 75rem) {
    .ratings-container {
        font-size: 0.875rem;
    }
    
    .ratings-header .header-item,
    .rating-item {
        min-width: 6rem;
        padding: 0.5rem;
    }
}

@media (max-width: 48rem) {
    .ratings-container {
        font-size: 0.75rem;
    }
    
    .ratings-header .header-item,
    .rating-item {
        min-width: 4rem;
        padding: 0.375rem 0.25rem;
    }
    
    .deca-rating-summary-title {
        font-size: 1.5rem;
        margin-block: 1rem 2rem;
    }
}
