﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Common.Models
{
    [Table("cmn_student_race_links")]
    public class StudentRaceLink
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Column("student_id")]
        public long StudentId { get; set; }

        [Column("student_race_id")]
        public long StudentRaceId { get; set; }
    }
}
