using Compass.C4L.DTOs;
using Compass.C4L.Models;

namespace Compass.C4L.Interfaces.Repositories
{
    public interface IC4LLessonPreparationRepository
    {
        Task<List<C4LLessonPreparation>> GetPreparationsByLessonAsync(string language, int unit, int week, int day);
        Task<C4LLessonPreparation?> GetPreparationAsync(string language, int unit, int week, int day, int lessonTypeSequence, int titleSequence);
        Task<List<LessonPreparationWithTitleDto>> RetrievePreparationsWithTitle(string language, int unit, int week, int day, long? classroomId = null);
    }
}
