﻿@page "/account/external-login"

@using System.Security.Claims
@using Compass.Common.Data
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Models
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@inject NavigationManager Navigation
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager
@inject ILogger<ExternalLogin> Logger
@inject IOrganizationHierarchyRepository OrganizationHierarchyRepository
@inject UserSessionService UserSessionService
@inject CommonSessionDataObserver CommonSessionDataObserver;


<PageTitle>External Login | C4L</PageTitle>

<h1 class="page-title mb-4">External login</h1>

@if (error != null)
{
    <div class="alert alert-danger text-center">
        @error
    </div>
}

@code {
    public const string LoginCallbackAction = "LoginCallback";
    private string? error;
    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }
    [SupplyParameterFromQuery]
    private string? Action { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (Action == "LoginCallback")
        {
            Logger.LogInformation("Starting external login callback process");
            var result = await HandleExternalLoginCallback();

            if (result.Succeeded)
            {
                Logger.LogInformation("Login succeeded - redirecting");
                Navigation.NavigateTo(ReturnUrl ?? "/");
                return;
            }
            else
            {
                Logger.LogWarning("Login failed - IsLockedOut: {IsLockedOut}, IsNotAllowed: {IsNotAllowed}, RequiresTwoFactor: {RequiresTwoFactor}",
                    result.IsLockedOut, result.IsNotAllowed, result.RequiresTwoFactor);

                if (result.IsLockedOut)
                    error = "Account is locked out.";
                else if (result.IsNotAllowed)
                    error = "Login not allowed.";
                else if (result.RequiresTwoFactor)
                    error = "Requires two-factor authentication.";
                else
                    error = "Error signing in with external provider.";
            }
        }
    }

    private async Task SetCommonSessionData(string? username)
    {
        if (username != null)
        {
            var _currentUser = await UserManager.FindByNameAsync(username);

            if (_currentUser != null)
            {
                CommonSessionData commonSessionData = new CommonSessionData();
                commonSessionData.CurrentOrganizationId = _currentUser.OrganizationId;
                if (_currentUser.OrganizationId != null && _currentUser.OrganizationId > 0)
                {
                    string? userId = _currentUser.Id;
                    List<VisibleEntity> visibleEntities = await OrganizationHierarchyRepository.GetVisibleEntities(userId, _currentUser.OrganizationId);

                    for (int i = 0; i < visibleEntities.Count(); i++)
                    {
                        VisibleEntity ve = visibleEntities[i];
                        if (ve.EntityLevel == 0)
                        {
                            commonSessionData.CurrentOrganizationName = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 1)
                        {
                            commonSessionData.Entity1Hierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 2)
                        {
                            commonSessionData.Entity2Hierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 3)
                        {
                            commonSessionData.Entity3Hierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 4)
                        {
                            commonSessionData.SiteHierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 5)
                        {
                            commonSessionData.StudentGroupHierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 6)
                        {
                            commonSessionData.StudentHierarchy = ve.EntityHierarchy ?? "";
                        }
                    }
                }

                await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                await CommonSessionDataObserver.BroadcastStateChangeAsync();
            }
        }
    }

    private async Task<SignInResult> HandleExternalLoginCallback()
    {
        var info = await SignInManager.GetExternalLoginInfoAsync();
        if (info == null)
        {
            Logger.LogWarning("No external login info found");
            error = "Error loading external login information.";
            return SignInResult.Failed;
        }

        Logger.LogInformation("External login info retrieved - Provider: {Provider}", info.LoginProvider);

        // Try to sign in first
        var result = await SignInManager.ExternalLoginSignInAsync(
            info.LoginProvider,
            info.ProviderKey,
            isPersistent: false,
            bypassTwoFactor: true);

        if (result.Succeeded)
        {
            Logger.LogInformation("External login succeeded");
            string? email = info.Principal.FindFirstValue(ClaimTypes.Email);
            await SetCommonSessionData(email);
        }
        else
        {
            // Get the email from the external provider
            var email = info.Principal.FindFirstValue(ClaimTypes.Email);
            if (email != null)
            {
                // Check if user with this email already exists
                var existingUser = await UserManager.FindByEmailAsync(email);

                if (existingUser != null)
                {
                    // User exists but Microsoft login isn't linked - let's link it
                    var addLoginResult = await UserManager.AddLoginAsync(existingUser, info);
                    if (addLoginResult.Succeeded)
                    {
                        Logger.LogInformation("Added Microsoft login to existing user");
                        // Now try to sign in again
                        result = await SignInManager.ExternalLoginSignInAsync(
                            info.LoginProvider,
                            info.ProviderKey,
                            isPersistent: false,
                            bypassTwoFactor: true);

                        await SetCommonSessionData(email);
                    }
                    else
                    {
                        Logger.LogWarning("Failed to add Microsoft login to existing user");
                        error = "Failed to link Microsoft account.";
                        return SignInResult.Failed;
                    }
                }
                else
                {
                    // Only create new user if they don't already exist
                    var user = new ApplicationUser
                        {
                            UserName = email,
                            Email = email,
                            EmailConfirmed = true
                        };

                    var createResult = await UserManager.CreateAsync(user);
                    if (createResult.Succeeded)
                    {
                        var addLoginResult = await UserManager.AddLoginAsync(user, info);
                        if (addLoginResult.Succeeded)
                        {
                            result = await SignInManager.ExternalLoginSignInAsync(
                                info.LoginProvider,
                                info.ProviderKey,
                                isPersistent: false,
                                bypassTwoFactor: true);

                            await SetCommonSessionData(email);
                        }
                    }
                }
            }
        }

        return result;
    }
}
