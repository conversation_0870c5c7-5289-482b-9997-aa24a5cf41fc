﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Common.Models
{
    [Table("cmn_user_student_group_accesses")]
    public class UserStudentGroupAccess
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }
        [Column("organization_id")]
        public long? OrganizationId { get; set; }
        [Column("student_group_id")]
        public long StudentGroupId { get; set; }
        [Column("mod_id")]
        public string ModId { get; set; }
        [Column("mod_ts")]
        public DateTime ModTs { get; set; }
        [Column("can_add")]
        public string CanAdd { get; set; }
        [Column("can_delete")]
        public string CanDelete { get; set; }
        [Column("can_update")]
        public string CanUpdate { get; set; }
        [Column("can_view")]
        public string CanView { get; set; }
        [Column("can_assign")]
        public string CanAssign { get; set; } 
    }
}
