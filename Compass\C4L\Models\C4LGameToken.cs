﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.C4L.Models
{
    [Table("c4l_game_tokens")]
    public class C4LGameToken
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("student_id")]
        public long? StudentId { get; set; }

        [Column("c4l_classroom_id")]
        public long? C4LClassroomId { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("mod_id")]
        public string ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Column("token")]
        public string Token { get; set; }

        [Column("unit")]
        public int Unit { get; set; }

        [Column("week")]
        public int Week { get; set; }

        [Column("student_name")]
        public string StudentName { get; set; }

        [Column("expire_ts")]
        public DateTime ExpireTs { get; set; }

        [Column("used")]
        public string Used { get; set; }
    }
}
