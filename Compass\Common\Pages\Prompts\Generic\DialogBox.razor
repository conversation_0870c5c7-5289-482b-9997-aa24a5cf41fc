﻿@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="dialog-overlay" style="@(IsVisible ? "display: flex;" : "display: none;")">
    <div class="dialog-box">
        <div class="dialog-box-message-wrapper">
            <h3 class="dialog-box-heading">@Title</h3>
            <p>@Message</p>
        </div>

        <div class="dialog-actions d-flex">
            <button class="c4l-button c4l-danger-button" @onclick="OnYes">Yes</button>
            <button class="c4l-button c4l-primary-button" @onclick="OnNo">No</button>
        </div>
    </div>
</div>
