﻿@using Compass.Common.Resources
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject IStringLocalizer<CommonResource> Localizer

<div class="messagebox-overlay" style="@(IsVisible ? "display: flex;" : "display: none;")">
    <div class="messagebox">
        <h3 class="mb-2">@Title</h3>

        @if (IsLocalized)
        {
            <p class="mb-0">@Localizer[@Message].Value</p>
        }
        else
        {
            <p class="mb-0">@Message</p>
        }
        <div class="messagebox-actions">
            <button class="c4l-button c4l-primary-button" @onclick="Close">Close</button>
        </div>
    </div>
</div>
