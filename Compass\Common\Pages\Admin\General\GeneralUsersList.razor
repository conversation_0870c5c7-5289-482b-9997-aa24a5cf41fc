﻿@page "/userlist"
@using Compass.Common.DTOs.User
@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Compass.Common.Pages.Prompts.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject NavigationManager NavigationManager
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject UserAccessor UserAccessor
@inject IUserService UserService
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<PageTitle>Users List | C4L</PageTitle>

<h1 class="page-title horizontal-line">General Users List</h1>

<div class="c4l-search-table-wrapper">
    <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

    <LoaderComponent IsLoading="isLoading">
        <div class="c4l-table-scroll-wrapper">
            <div class="c4l-table-wrapper has-links users-wrapper">
                <div class="c4l-table-headings-wrapper users-heading-wrapper">
                    <h6 class="c4l-table-heading">@Localizer["lbl_Username"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_Email"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_FirstName"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_LastName"]</h6>
                </div>

                @foreach (UserListDisplayDto user in userResults)
                {
                    <button type="button" title="Select @user.UserName" @onclick="() => OnUserDetailsClick(user.Id)">
                        <div class="c4l-table-result-wrapper user-result-username">
                            <p class="c4l-table-result-item">@user.UserName</p>
                            <p class="c4l-table-result-item">@user.Email</p>
                            <p class="c4l-table-result-item">@user.FirstName</p>
                            <p class="c4l-table-result-item">@user.LastName</p>
                        </div>
                    </button>
                }
            </div>
        </div>

        <div class="c4l-pagination-wrapper">
            <div class="c4l-pagination-buttons-wrapper">
                <div class="buttons-wrapper">
                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnPreviousClicked()"
                        disabled="@(currentPage <= 1)"
                    >
                        @Localizer["lbl_Previous"]
                    </button>

                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnNextClicked()"
                        disabled="@(currentPage >= maxPages)"
                    >
                        @Localizer["lbl_Next"]
                    </button>
                </div>

                <div class="add-user-button-wrapper">
                    <button class="c4l-button c4l-secondary-button add-user-button" type="button" @onclick="OnCreateUser">@Localizer["lbl_adduser"]</button>
                </div>
            </div>

            <div class="page-count-wrapper font-weight-500">
                <span class="current-page-number">@currentPage</span> @Localizer["lbl_of"] @maxPages
            </div>
        </div>
    </LoaderComponent>
</div>

<CreateUserBox 
    IsVisible="@IsCreateUserBoxVisible" 
    CreateUserResult="OnCreateUserResult" 
    OrganizationId="currentOrganizationId" 
/>
