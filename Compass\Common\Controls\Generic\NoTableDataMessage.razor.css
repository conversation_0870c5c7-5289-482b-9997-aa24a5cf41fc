.no-data-message-wrapper {
  --_info-text-color: hsl(236.4, 68.6%, 23.7%);
  --_info-bg-color: hsl(190 89.7% 87.5%);
  --_info-success-text-color: hsl(152 69% 19%);
  --_info-success-bg-color: hsl(151, 100%, 95%);
  --_info-warning-text-color: hsl(45 94% 21%);
  --_info-warning-bg-color: hsl(46, 100%, 97%);
  --_info-danger-text-color: hsl(354 79.4% 21%);
  --_info-danger-bg-color: hsl(354, 100%, 97%);
  background-color: var(--_info-bg-color);
  color: var(--_info-text-color);
  border-radius: 0.25rem;
  border-inline-start: 0.375rem solid var(--_info-text-color);
  padding: 1rem;

  & a {
    font-weight: 500;
    color: currentColor;
  }

  &.success-message {
    background-color: var(--_info-success-bg-color);
    border-color: var(--_info-success-text-color);
    color: var(--_info-success-text-color);

    & a {
      color: currentColor;
    }
  }

  &.warning-message {
    background-color: var(--_info-warning-bg-color);
    border-color: var(--_info-warning-text-color);
    color: var(--_info-warning-text-color);

    & a {
      color: currentColor;
    }
  }

  &.danger-message {
    background-color: var(--_info-danger-bg-color);
    border-color: var(--_info-danger-text-color);
    color: var(--_info-danger-text-color);

    & a {
      color: currentColor;
    }
  }
}
