using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Microsoft.AspNetCore.Components;

namespace Compass.C4L.Pages
{
    public partial class C4L_NonContactDay
    {
        [Parameter]
        public long? c4l_classroomId { get; set; }

        [Inject]
        public required IC4LNonContactDayService NonContactDayService { get; set; }

        private List<C4LNonContactDay> nonContactDays = new();
        private string errorMessage = string.Empty;

        protected override async Task OnInitializedAsync()
        {
            await LoadNonContactDays();
        }

        private async Task LoadNonContactDays()
        {
            try
            {
                if (c4l_classroomId.HasValue)
                {
                    nonContactDays = (await NonContactDayService.GetNonContactDaysAsync(c4l_classroomId.Value)).ToList();
                }
            }
            catch (Exception ex)
            {
                errorMessage = "Failed to load non-contact days";
                Console.WriteLine($"Error loading non-contact days: {ex.Message}");
            }
        }
    }
}
