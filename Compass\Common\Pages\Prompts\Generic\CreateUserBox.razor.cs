﻿using Compass.Common.Data;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace Compass.Common.Pages.Prompts.Generic
{
    public partial class CreateUserBox
    {
        [Parameter]
        public EventCallback<bool> CreateUserResult { get; set; }

        [Parameter]
        public bool IsVisible { get; set; }

        [Parameter]
        public long? OrganizationId { get; set; }

        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();
        private string? successMessage = string.Empty;

        private IEnumerable<IdentityError>? identityErrors;
        private string? Message => identityErrors is null ? null : $"Error: {string.Join(", ", identityErrors.Select(error => error.Description))}";

        protected override void OnParametersSet()
        {
            if (IsVisible)
            {
                Input = new(); // Clear input when the dialog is shown
            }
        }

        protected async Task CreateUserAsync(EditContext editContext)
        {
            ApplicationUser user = CreateUser();

            if (Input.Email != null && Input.Email != string.Empty)
            {
                IUserEmailStore<ApplicationUser> emailStore = GetEmailStore();
                await emailStore.SetEmailAsync(user, Input.Email, CancellationToken.None);
            }

            if (this.OrganizationId is null)
            {
                throw new Exception("Organization Id is not set");
            }
            user.OrganizationId = this.OrganizationId;

            user.UserName = Input.UserName;
            user.FirstName = Input.FirstName;
            user.LastName = Input.LastName;

            IdentityResult result = await UserManager.CreateAsync(user, Input.Password);

            if (!result.Succeeded)
            {
                identityErrors = result.Errors;
                return;
            }

            await CreateUserResult.InvokeAsync(true);
        }

        protected async Task OnCancelClick()
        {
            IsVisible = false;
            await CreateUserResult.InvokeAsync(false);
        }

        private ApplicationUser CreateUser()
        {
            try
            {
                return Activator.CreateInstance<ApplicationUser>();
            }
            catch
            {
                throw new InvalidOperationException($"Can't create an instance of '{nameof(ApplicationUser)}'. " +
                    $"Ensure that '{nameof(ApplicationUser)}' is not an abstract class and has a parameterless constructor.");
            }
        }

        private IUserEmailStore<ApplicationUser> GetEmailStore()
        {
            if (!UserManager.SupportsUserEmail)
            {
                throw new NotSupportedException("The default UI requires a user store with email support.");
            }
            return (IUserEmailStore<ApplicationUser>)UserStore;
        }

        public sealed class InputModel
        {
            [Required]
            [Display(Name = "UserName")]
            public string UserName { get; set; } = "";

            [Required]
            [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
            [DataType(DataType.Password)]
            [Display(Name = "Password")]
            public string Password { get; set; } = "";

            [DataType(DataType.Password)]
            [Display(Name = "Confirm password")]
            [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
            public string ConfirmPassword { get; set; } = "";

            [Display(Name = "FirstName")]
            public string FirstName { get; set; } = "";

            [Display(Name = "LastName")]
            public string LastName { get; set; } = "";

            [Display(Name = "Email")]
            [CustomValidation(typeof(InputModel), nameof(ValidateEmail))]
            public string Email { get; set; } = "";

            public static ValidationResult? ValidateEmail(string? email, ValidationContext context)
            {
                if (string.IsNullOrEmpty(email))
                {
                    return ValidationResult.Success; // Optional, so no error if empty.
                }

                var emailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
                if (System.Text.RegularExpressions.Regex.IsMatch(email, emailPattern))
                {
                    return ValidationResult.Success;
                }

                return new ValidationResult("The email address is not valid.");
            }
        }
    }
}
