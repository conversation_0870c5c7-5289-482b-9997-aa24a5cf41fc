using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;

namespace Compass.C4L.Services
{
    public class C4LLearningCenterService : IC4LLearningCenterService
    {
        private readonly IC4LLearningCenterRepository _learningCenterRepository;

        public C4LLearningCenterService(IC4LLearningCenterRepository learningCenterRepository)
        {
            _learningCenterRepository = learningCenterRepository;
        }

        public async Task<List<C4LLearningCenter>> GetLearningCentersByUnitAsync(string language, string unit)
        {
            return await _learningCenterRepository.GetLearningCentersByUnitAsync(language, unit);
        }

        public async Task<List<C4LLearningCenter>> GetAllLearningCentersAsync(string language)
        {
            return await _learningCenterRepository.GetAllLearningCentersAsync(language);
        }
    }
}