﻿using Compass.Common.Data;
using Compass.Common.Models;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace Compass.Components.Account.Pages
{
    public partial class UserLogin : ComponentBase, IDisposable
    {
        private string? errorMessage;
        private string? alertType = "danger";

        [CascadingParameter]
        private HttpContext HttpContext { get; set; } = default!;

        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        [SupplyParameterFromQuery]
        private string? ReturnUrl { get; set; }

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);
        }

        private async Task SetCommonSessionData()
        {
            _currentUser = await UserManager.FindByNameAsync(Input.Username);

            if (_currentUser != null)
            {
                CommonSessionData commonSessionData = new CommonSessionData();
                commonSessionData.CurrentOrganizationId = _currentUser.OrganizationId;
                if (_currentUser.OrganizationId != null && _currentUser.OrganizationId > 0)
                {
                    string? userId = _currentUser.Id;
                    List<VisibleEntity> visibleEntities = await OrganizationHierarchyRepository.GetVisibleEntities(userId, _currentUser.OrganizationId);

                    for (int i = 0; i < visibleEntities.Count(); i++)
                    {
                        VisibleEntity ve = visibleEntities[i];
                        if (ve.EntityLevel == 0)
                        {
                            commonSessionData.CurrentOrganizationName = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 1)
                        {
                            commonSessionData.Entity1Hierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 2)
                        {
                            commonSessionData.Entity2Hierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 3)
                        {
                            commonSessionData.Entity3Hierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 4)
                        {
                            commonSessionData.SiteHierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 5)
                        {
                            commonSessionData.StudentGroupHierarchy = ve.EntityHierarchy ?? "";
                        }
                        else if (ve.EntityLevel <= 6)
                        {
                            commonSessionData.StudentHierarchy = ve.EntityHierarchy ?? "";
                        }
                    }
                }

                await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                await CommonSessionDataObserver.BroadcastStateChangeAsync();
            }

        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }

        public async Task LoginUser()
        {
            // This doesn't count login failures towards account lockout
            // To enable password failures to trigger account lockout, set lockoutOnFailure: true
            SignInResult result = await SignInManager.PasswordSignInAsync(Input.Username, Input.Password, Input.RememberMe, lockoutOnFailure: false);
            if (result.Succeeded)
            {
                Logger.LogInformation("User logged in.");

                await SetCommonSessionData();

                RedirectManager.RedirectTo(ReturnUrl);

                await CommonSessionDataObserver.BroadcastStateChangeAsync();
            }
            else if (result.RequiresTwoFactor)
            {
                RedirectManager.RedirectTo("account/login-with-2fa",
                    new() { ["returnUrl"] = ReturnUrl, ["rememberMe"] = Input.RememberMe });
            }
            else if (result.IsLockedOut)
            {
                Logger.LogWarning("User account locked out.");
                RedirectManager.RedirectTo("account/lockout");
            }
            else
            {
                errorMessage = "Error: Invalid login attempt.";
            }
        }

        private sealed class InputModel
        {
            [Required]
            public string Username { get; set; } = "";

            [Required]
            [DataType(DataType.Password)]
            public string Password { get; set; } = "";

            [Display(Name = "Remember me?")]
            public bool RememberMe { get; set; }
        }
    }
}
