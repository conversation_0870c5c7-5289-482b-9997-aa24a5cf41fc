using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Data;
using Compass.Common.Pages.Admin.StudentGroup;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.C4L.Pages
{
    public partial class C4L_LearningCenterComponent
    {
        [Inject]
        public required IC4LLearningCenterService LearningCenterService { get; set; }

        [CascadingParameter]
        public StudentGroupTabs? ParentTabs { get; set; }

        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private string SelectedLanguage { get; set; } = "English";
        private string SelectedUnit { get; set; } = "1";
        private List<C4LLearningCenter> LearningCenters { get; set; } = new List<C4LLearningCenter>();

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            (ApplicationUser? user, string? userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;

            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            await GetCommonSessionData();
            await LoadLearningCenters();
        }

        private async Task LoadLearningCenters()
        {
            try
            {
                LearningCenters = await LearningCenterService.GetLearningCentersByUnitAsync(SelectedLanguage, SelectedUnit);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading learning centers: {ex.Message}");
            }
        }
    }
}