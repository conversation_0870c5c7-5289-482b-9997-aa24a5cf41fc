﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Common.Models
{
    [Table("cmn_student_groups")]
    public class StudentGroup
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("mod_id")]
        public string ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Column("site_id")]
        public long? SiteId { get; set; }

        [Required]
        [Column("name")]
        public string? Name { get; set; } = string.Empty;

        [Column("group_type")]
        public string? GroupType { get; set; }

        [Column("is_deleted")]
        public string IsDeleted { get; set; } = "N";
    }
}
