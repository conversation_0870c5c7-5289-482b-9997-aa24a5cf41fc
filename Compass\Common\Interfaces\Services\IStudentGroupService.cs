﻿using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Student;
using Compass.Common.DTOs.StudentGroup;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Services
{
    public interface IStudentGroupService
    {
        public Task<StudentGroup> CreateStudentGroupAsync(StudentGroup studentGroup);
        public Task<StudentGroup?> UpdateStudentGroupAsync(long? id, StudentGroup? studentGroup);
        public Task<StudentGroup?> GetStudentGroupAsync(long? id);
        public Task<KaplanPageable<StudentGroupListDisplayDto>> GetStudentGroupPage(StudentGroupListAction action);
        public Task<DeleteReturnDto> DeleteStudentGroup(long? organizationId, long? studentGroupId);
        public Task<UserStudentGroupLink> AssignStudentGroupUser(CreateUserLinkAction action);
        public Task<bool> UnAssignStudentGroupUser(CreateUserLinkAction action);
        public Task<KaplanPageable<StudentDisplayDto>> GetStudentGroupRoster(StudentGroupRosterListAction action);
    }
}
