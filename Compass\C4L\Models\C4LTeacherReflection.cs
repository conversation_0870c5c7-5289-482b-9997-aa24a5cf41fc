using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.C4L.Models
{
    [Table("c4l_teacher_reflections")]
    public class C4LTeacherReflection
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime? ModTs { get; set; }

        [Column("organization_id")]
        public long OrganizationId { get; set; }

        [Column("classroom_id")]
        public long ClassroomId { get; set; }

        [Column("lesson_id")]
        public int LessonId { get; set; }

        [Column("notes")]
        public string? Notes { get; set; }
    }
}
