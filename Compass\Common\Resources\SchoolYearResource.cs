﻿using System.ComponentModel.DataAnnotations;

namespace Compass.Common.Resources
{
    public class SchoolYearResource
    {
        public static int GetDefaultYear()
        {
            int currentYear = DateTime.Now.Year;
            return currentYear;
        }

        public static string GetDefaultDescription()
        {
            int currentYear = DateTime.Now.Year;
            int nextYear = currentYear + 1;

            string description = currentYear + " - " + nextYear;
            return description;
        }

        public static ValidationResult? ValidateSchoolYear(int? schoolYear, ValidationContext context)
        {
            if (schoolYear == null)
            {
                return new ValidationResult("School Year is Required.");
            }

            int currentYear = DateTime.Now.Year;
            if (schoolYear > currentYear + 2)
            {
                return new ValidationResult("School Year can not be 2 years in advance.");
            }

            if (schoolYear < currentYear - 5)
            {
                return new ValidationResult("School Year can not be 5 years in the past.");
            }

            return ValidationResult.Success;
        }

        public static ValidationResult? ValidateDescription(string? description, ValidationContext context)
        {
            if (string.IsNullOrEmpty(description))
            {
                return new ValidationResult("Description is Required.");
            }

            if (description.Length > 100)
            {
                return new ValidationResult("Description can not be longer than 100 characters.");
            }

            return ValidationResult.Success;
        }
    }
}
