using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.LicensePool;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Compass.Common.Models;

namespace Compass.Common.Services
{
    public class LicensePoolService : ILicensePoolService
    {
        private readonly ILicensePoolRepository _licensePoolRepository;

        public LicensePoolService(ILicensePoolRepository licensePoolRepository)
        {
            _licensePoolRepository = licensePoolRepository;
        }

        public async Task<LicensePool?> UpdateLicensePoolAsync(long? id, LicensePool licensePool)
        {
            LicensePool? updatedLicensePool = await _licensePoolRepository.UpdateLicensePoolAsync(id, licensePool);

            return updatedLicensePool;
        }

        public async Task<LicensePool?> GetLicensePoolAsync(long? licensePoolId)
        {
            LicensePool? licensePool = await _licensePoolRepository.GetLicensePoolAsync(licensePoolId);

            return licensePool;
        }

        public async Task<LicensePool> AddLicensePoolAsync(LicensePool licensePool)
        {
            LicensePool createdLicensePool = await _licensePoolRepository.AddLicensePoolAsync(licensePool);

            return createdLicensePool;
        }

        public async Task<KaplanPageable<LicensePoolListDisplayDto>> GetLicensePoolDisplayPages(PageQuery pageQuery, long? organizationId)
        {
            List<LicensePoolListDisplayDto> licensePoolList = await _licensePoolRepository.GetLicensePoolDisplayList(pageQuery, organizationId);

            int licensePoolCount = await _licensePoolRepository.GetLicensePoolCount(organizationId);
            int pageSize = pageQuery.PageSize;

            int maxPages = (int)Math.Ceiling((double)licensePoolCount / pageSize);

            KaplanPageable<LicensePoolListDisplayDto> pageable = new KaplanPageable<LicensePoolListDisplayDto>();
            pageable.PageContent = licensePoolList;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<KaplanPageable<LicensePool>> GetLicensePoolSummaryPages(PageQuery pageQuery, long? organizationId)
        {
            List<LicensePool> licensePoolList = await _licensePoolRepository.GetLicensePoolList(pageQuery, organizationId);

            int licensePoolCount = await _licensePoolRepository.GetLicensePoolCount(organizationId);
            int pageSize = pageQuery.PageSize;

            int maxPages = (int)Math.Ceiling((double)licensePoolCount / pageSize);

            KaplanPageable<LicensePool> pageable = new KaplanPageable<LicensePool>();
            pageable.PageContent = licensePoolList;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<bool> CheckActiveLicense(long? organizationId, string? product)
        {
            int licenseCount = await _licensePoolRepository.CountActiveLicense(organizationId, product);

            bool ret = false;
            if (licenseCount > 0)
            {
                ret = true;
            }

            return ret;
        }

        public async Task<long?> GetAvailableLicensePoolId(long? organizationId, string? product)
        {
            LicensePool? licensePool = await _licensePoolRepository.GetLicensePoolForIncrementation(organizationId, product);
            if (licensePool == null)
            {
                return null;
            }
            long licensePoolId = licensePool.Id;

            return licensePoolId;
        }

        public async Task<bool> IncrementLicense(long? organizationId, long? entityID, long? licensePoolId, ILicenseRepository licenseRepository)
        {
            if (organizationId == null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (entityID == null)
            {
                throw new ArgumentNullException(nameof(entityID));
            }

            if (licensePoolId == null)
            {
                throw new ArgumentNullException(nameof(licensePoolId));
            }

            // Get a DbContext from the repository
            ApplicationDbContext dbContext = _licensePoolRepository.CreateDbContext();

            // Create an execution strategy
            Microsoft.EntityFrameworkCore.Storage.IExecutionStrategy strategy = dbContext.Database.CreateExecutionStrategy();

            // The follow is to handle is a single transaction so that we can easily roll back.
            bool isValid = await strategy.ExecuteAsync<object?, bool>(
                null,
                async (dbCtx, state, cancellationToken) =>
                {
                    // Begin a transaction
                    using (Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction transaction =
                        await dbContext.Database.BeginTransactionAsync(cancellationToken))
                    {
                        try
                        {
                            // Increment the license pool
                            bool incrementSuccessful = await _licensePoolRepository.IncrementUsedLicensePoolWithContext(licensePoolId, dbContext);

                            if (!incrementSuccessful)
                            {
                                throw new InvalidOperationException("Failed to increment license pool");
                            }

                            // Create the license
                            await licenseRepository.CreateLicenseWithContext((long)organizationId, (long)licensePoolId, (long)entityID, dbContext);

                            // Commit the transaction
                            await transaction.CommitAsync(cancellationToken);

                            return true;
                        }
                        catch
                        {
                            // Rollback the transaction if any operation fails
                            await transaction.RollbackAsync(cancellationToken);
                            throw;
                        }
                    }
                },
                null,
                CancellationToken.None);

            return isValid;
        }
    }
}
