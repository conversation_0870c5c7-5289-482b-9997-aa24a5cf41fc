.navbar-toggler {
    appearance: none;
    cursor: pointer;
    width: 3.5rem;
    height: 2.5rem;
    color: white;
    position: absolute;
    top: 0.5rem;
    right: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") no-repeat center/1.75rem rgba(255, 255, 255, 0.1);
}

.navbar-toggler {
    &:checked {
        background-color: rgba(255, 255, 255, 0.5);

        & ~ .nav-scrollable {
            display: block;
            background-color: var(--neutral-900);
            border-block-start: 1px solid var(--neutral-500);
        }
    }
}

.sidebar-nav {
    --_nav-link-text-icon-gap: 0.75rem;
    background-color: var(--neutral-900);
}

.sidebar-dropdown-wrapper {
    height: 3.5rem;
    background-color: var(--neutral-900);
    padding-inline: clamp(1.5rem, 1.211rem + 1.233vw, 2rem);
}

::deep .nav-app-link {
    font-size: 1.25rem;
    color: var(--white);
    text-decoration: none;
}

.bi {
    display: flex;
    width: 1.25rem;
    height: 1.25rem;
    flex: 0 0 1.25rem;
}

.nav-links-wrapper {
    display: flex;
    gap: 0.25rem;
}

.nav-item ::deep .nav-link {
    font-size: 1rem;
    font-weight: 400;
    display: flex;
    gap: var(--_nav-link-text-icon-gap);
    align-items: center;
    background: none;
    border: none;
    border-radius: 0.25rem;
    width: 100%;
    height: 3rem;
    line-height: 1.375;
    padding-inline: 0.5rem;
    transition:
        color var(--transition-speed) ease,
        background-color var(--transition-speed) ease,
        border-color var(--transition-speed) ease;

    &.active {
        font-weight: 600;
        background-color: var(--c4l-primary-500);
        color: var(--white);
    }
}

.nav-item ::deep .nav-link:active,
.nav-item ::deep .nav-link:hover,
.nav-item ::deep .nav-link:focus {
    background-color: var(--c4l-primary-400);
    color: var(--white);
}

.nav-scrollable {
    display: none;
    z-index: 99;
    padding: 1rem;
}

.account-sidebar-nav-link,
.login-sidebar-nav-link,
.logout-sidebar-nav-link {
    display: block;
}

.account-links-wrapper {
    display: none;
}

.account-settings-link,
.account-logout-link {
    background-color: transparent;
    gap: var(--_nav-link-text-icon-gap);
    border-radius: 0.25rem;
    border: none;
    width: 100%;

    &:active,
    &.active,
    &:hover,
    &:focus {
        background-color: var(--c4l-primary-400);
        color: var(--white);
    }
}

@media (min-width: 64rem) {
    .sidebar-nav {
        height: 100vh;
        overflow-y: auto;
    }

    .sidebar-dropdown-wrapper {
        padding: 1rem;
        height: 5.375rem;
        border-block-end: 1px solid var(--neutral-500);

        & .dropdown,
        & .dropdown-toggle,
        & .dropdown-menu {
            width: 100%;
        }

        & .dropdown,
        & .dropdown-toggle {
            height: 100%;
        }

        & .compass-app-link {
            color: var(--white);
            background-color: var(--neutral-900);
            border: none;

            &::after {
                content: none;
            }
        }

        & .dropdown-menu {
            padding: 0.5rem;
            border: none;
            top: 56px;
        }

        & .dropdown-menu-li {
            padding: 0.5rem 1rem;
            background-color: var(--white);
            transition: background-color var(--transition-speed) ease;
            border-radius: 0.25rem;

            &:active,
            &:hover,
            &:focus {
                background-color: hsl(206 30% 95%);
            }
        }

        & ::deep .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: hsl(217 17% 15%);
            padding: 0;
            border-radius: 0.25rem;

            &.active {
                background-color: transparent;
            }
        }
    }

    .navbar-toggler {
        display: none;

        &:checked {
            & ~ .nav-scrollable {
                border: none;
            }
        }
    }

    .nav-scrollable {
        display: block;
    }

    .nav-item ::deep .nav-link {
        & svg g,
        & svg path {
            transition: fill var(--transition-speed) ease;
        }
    }
    
    .nav-item ::deep .nav-link:hover {
        & svg g,
        & svg path {
            fill: var(--black);
        }
    }

    .account-sidebar-nav-link,
    .login-sidebar-nav-link,
    .logout-sidebar-nav-link {
        display: none;
    }

    .account-links-wrapper {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        border-block-start: 1px solid var(--neutral-500);
        padding: 1rem;
    }
}
