﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.C4L.Models
{
    [Table("c4l_learning_objectives_lookup")]
    public class C4LLearningObjective
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("title")]
        public string Title { get; set; }

        [Column("content_area")]
        public string ContentArea { get; set; }

        [Column("domain")]
        public string Domain { get; set; }

        [Column("language")]
        public string Language { get; set; }
    }
}
