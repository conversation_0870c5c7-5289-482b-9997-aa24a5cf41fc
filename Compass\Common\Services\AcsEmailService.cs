﻿using Azure;
using Azure.Communication.Email;

namespace Compass.Common.Services
{
    public class AcsEmailService
    {
        private readonly EmailClient _emailClient;

        public AcsEmailService(string connectionString)
        {
            _emailClient = new EmailClient(connectionString);
        }

        public async Task SendEmailAsync(string sender, string recipient, string subject, string bodyHtml)
        {
            var emailAddress = new EmailAddress(recipient);
            var emailAddressList = new List<EmailAddress> { emailAddress };
            var emailContent = new EmailContent(subject)
            {
                Html = bodyHtml
            };
            EmailRecipients? emailRecipients = new EmailRecipients(emailAddressList);

            var emailMessage = new EmailMessage(sender, emailRecipients, emailContent);
            try
            {
                var sendOperation = await _emailClient.SendAsync(WaitUntil.Completed,emailMessage, default);
                // Handle the response
                if (sendOperation.HasCompleted)
                {
                    Console.WriteLine($"Email sent successfully. Message Id: {sendOperation.Id}");
                }
                else
                {
                    Console.WriteLine($"Email sending operation not completed yet. Message Id: {sendOperation.Id}");
                }
            }
            catch (RequestFailedException ex)
            {
                Console.WriteLine(ex.Message);
            }
        }
    }
}
