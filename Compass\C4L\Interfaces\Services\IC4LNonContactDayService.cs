using Compass.C4L.Models;

namespace Compass.C4L.Interfaces.Services
{
    public interface IC4LNonContactDayService
    {
        Task<IEnumerable<C4LNonContactDay>> GetNonContactDaysAsync(long classroomId);
        Task<C4LNonContactDay> CreateNonContactDayAsync(C4LNonContactDay nonContactDay);
        Task<C4LNonContactDay> UpdateNonContactDayAsync(C4LNonContactDay nonContactDay);
        Task DeleteNonContactDayAsync(long id);
        Task<bool> ValidateDateRange(long classroomId, DateTime startDate, DateTime endDate, int? excludeId = null);
        Task<bool> ValidateDateRange(long value, DateTime startDate, DateTime endDate, long? v);
        Task<C4LNonContactDay> GetNonContactDayAsync(long nonContactDayId);
        Task<List<C4LNonContactDay>> GetNonContactDaysWithinDateRange(long classroomId, DateTime startDate, DateTime endDate);
    }
}
