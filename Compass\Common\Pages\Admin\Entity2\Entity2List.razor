﻿@page "/entity2list"
@using Compass.Common.DTOs.Entity2
@using Compass.Common.DTOs.Generic
@using Compass.Common.Helpers
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject NavigationManager NavigationManager
@inject IEntity2Service Entity2Service;
@inject UserSessionService UserSessionService
@inject UserAccessor UserAccessor
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<h1 class="page-title horizontal-line">@entity2Hierarchy @Localizer["lbl_DistrictList"]</h1>

<div class="c4l-search-table-wrapper">
    <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

    <LoaderComponent IsLoading="isLoading">
        <div class="c4l-table-scroll-wrapper">
            <div class="c4l-table-wrapper has-links entity2-wrapper">
                <div class="c4l-table-headings-wrapper entity2-headings-wrapper">
                    <h6 class="c4l-table-heading">@Localizer["lbl_DistrictName"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_ContactName"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_ContactEmail"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_ContactPhone"]</h6>
                    @if (@entity1Hierarchy != string.Empty)
                    {
                        <h6 class="c4l-table-heading">@entity1Hierarchy</h6>
                    }
                </div>

                @foreach (Entity2ListDisplayDto entity in entity2Results)
                {
                    <button name="entity 2 select button" type="button" title="Select @entity.Name" @onclick="() => OnEntity2Selected(entity)">
                        <div class="c4l-table-result-wrapper entity2-result-wrapper">
                            <p class="c4l-table-result-item">@entity.Name</p>
                            <p class="c4l-table-result-item">@entity.ContactName</p>
                            <p class="c4l-table-result-item">@entity.ContactEmail</p>
                            <p class="c4l-table-result-item">@entity.ContactPhone</p>
                            @if (@entity.Entity1Name != null)
                            {
                                <p class="c4l-table-result-item">@entity.Entity1Name</p>
                            }
                        </div>
                    </button>
                }
            </div>
        </div>
        
        <div class="c4l-pagination-wrapper">
            <div class="c4l-pagination-buttons-wrapper">
                <div class="buttons-wrapper">
                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnPreviousClicked()"
                        disabled="@(currentPage <= 1)"
                    >
                        @Localizer["lbl_Previous"]
                    </button>

                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnNextClicked()"
                        disabled="@(currentPage >= maxPages)"
                    >
                        @Localizer["lbl_Next"]
                    </button>
                </div>
            </div>

            <div class="page-count-wrapper font-weight-500">
                <span class="current-page-number">@currentPage</span> @Localizer["lbl_of"] @maxPages
            </div>
        </div>
    </LoaderComponent>
</div>
