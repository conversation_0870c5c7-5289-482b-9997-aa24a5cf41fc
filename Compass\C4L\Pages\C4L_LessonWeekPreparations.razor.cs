using Compass.C4L.DTOs;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Data;
using Compass.Common.Pages.Admin.StudentGroup;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.C4L.Pages
{
    public partial class C4L_LessonWeekPreparations
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; } = default!;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private long? organizationId;
        private long? c4l_classroomId;

        private DateTime CurrentMondayDate { get; set; }
        private DateTime ClassroomStartDate { get; set; }
        private string CurrentCalendarUnitWeeks { get; set; }
        private int CurrentUnit { get; set; } = 1;
        private int CurrentWeek { get; set; } = 1;

        private List<LessonPreparationWithTitleDto> preparations = new List<LessonPreparationWithTitleDto>();
        private string SelectedLanguage { get; set; } = "English";

        // Dictionary to track changes to completion status
        private Dictionary<long, bool> completionChanges = new Dictionary<long, bool>();

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            (ApplicationUser? user, string? userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;

            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
                if (commonSessionData is not null)
                {
                    organizationId = commonSessionData.CurrentOrganizationId;
                }
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            // Get the shared data from the distributed session service
            C4LNavigationContext navigationContext = await SessionStateService.GetNavigationContextAsync();
            CurrentUnit = navigationContext.CurrentUnit;
            CurrentWeek = navigationContext.CurrentWeek;
            CurrentCalendarUnitWeeks = navigationContext.CurrentCalendarUnitWeeks;
            CurrentMondayDate = navigationContext.CurrentMondayDate;
            ClassroomStartDate = navigationContext.ClassroomStartDate;
            c4l_classroomId = navigationContext.C4L_ClassroomId;

            // If we don't have a classroom ID from the session state service, try to get it from the student group
            if (!c4l_classroomId.HasValue)
            {
                CommonSessionData? commonSessionData = await GetCommonSessionData();
                if (commonSessionData is not null)
                {
                    long? studentGroupId = commonSessionData.CurrentStudentGroupId;
                    if (studentGroupId.HasValue)
                    {
                        C4LClassroom? c4lc = await C4LClassroomService.GetByStudentGroupIdAsync(studentGroupId.Value);
                        if (c4lc is not null)
                        {
                            c4l_classroomId = c4lc.Id;
                            ClassroomStartDate = c4lc.StartDate;
                            CurrentMondayDate = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek + (int)DayOfWeek.Monday);

                            // Calculate the curriculum unit and week
                            await CalculateCurriculumUnitAndWeekFromDate();
                        }
                    }
                }
            }

            // Calculate the current week number for display
            CalculateCurrentWeekNumber();

            // Initialize with the current week's preparations
            await LoadWeekPreparationsAsync();
        }

        private void CalculateCurrentWeekNumber()
        {
            // We now use the calendar week number from the C4L_LessonComponent
            // This method is kept for backward compatibility
        }

        // This method is no longer needed as we get the unit and week directly from C4L_LessonComponent
        private async Task CalculateCurriculumUnitAndWeekFromDate()
        {
            // Method kept for backward compatibility
        }

        private async Task LoadWeekPreparationsAsync()
        {
            try
            {
                // Simply use the Unit and Week passed from the C4L_LessonComponent
                // No need for complex calculations since we already have the correct values

                // Clear the completion changes dictionary
                completionChanges.Clear();

                // Get preparations from the service, including completion status
                preparations = await LessonPreparationService.RetrievePreparationsWithTitle(
                    SelectedLanguage, CurrentUnit, CurrentWeek, c4l_classroomId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading week preparations: {ex.Message}");
            }
            StateHasChanged();
        }

        // This method is no longer needed as we get the unit and week directly from C4L_LessonComponent
        private void CalculateCurriculumUnitAndWeek(int weekdayCount)
        {
            // Method kept for backward compatibility
        }

        private async Task NavigateToPreviousWeek()
        {
            // Move to the previous week in the curriculum
            if (CurrentWeek > 1)
            {
                // Move to the previous week in the current unit
                CurrentWeek--;
            }
            else if (CurrentUnit > 1)
            {
                // Move to the last week of the previous unit
                CurrentUnit--;
                CurrentWeek = (CurrentUnit == 6) ? 2 : 6; // Unit 6 has only 2 weeks
            }
            else
            {
                // Already at the beginning of the curriculum
                return;
            }

            // Update the current Monday date
            CurrentMondayDate = CurrentMondayDate.AddDays(-7);

            await LoadWeekPreparationsAsync();
        }

        private async Task NavigateToNextWeek()
        {
            int maxWeeksInUnit = (CurrentUnit == 6) ? 2 : 6; // Unit 6 has only 2 weeks

            if (CurrentWeek < maxWeeksInUnit)
            {
                // Move to the next week in the current unit
                CurrentWeek++;
            }
            else if (CurrentUnit < 6)
            {
                // Move to the first week of the next unit
                CurrentUnit++;
                CurrentWeek = 1;
            }
            else
            {
                // Already at the end of the curriculum
                return;
            }

            // Update the current Monday date
            CurrentMondayDate = CurrentMondayDate.AddDays(7);

            await LoadWeekPreparationsAsync();
        }

        private async Task OnLanguageChanged(string language)
        {
            SelectedLanguage = language;
            await LoadWeekPreparationsAsync();
        }

        private void OnCompletedChanged(LessonPreparationWithTitleDto preparation, bool isCompleted)
        {
            // Track the change in our dictionary
            completionChanges[preparation.Id] = isCompleted;
        }

        private async Task SaveCompletionStatus()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (!c4l_classroomId.HasValue || !organizationId.HasValue)
            {
                Console.WriteLine("Cannot save completion status: Classroom ID or Organization ID is missing");
                return;
            }

            try
            {
                // Save each changed preparation completion status
                foreach (KeyValuePair<long, bool> change in completionChanges)
                {
                    long preparationId = change.Key;
                    bool isCompleted = change.Value;

                    await LessonPreparationCompletedService.SaveCompletionStatusAsync(
                        preparationId,
                        organizationId.Value,
                        c4l_classroomId.Value,
                        isCompleted);
                }

                // Clear the changes dictionary after saving
                completionChanges.Clear();

                // Reload the preparations to get the updated completion status
                await LoadWeekPreparationsAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving completion status: {ex.Message}");
            }
        }

        private async Task ReturnToLessons()
        {
            // Save the current state to the distributed session service
            C4LNavigationContext navigationContext = new Compass.C4L.Models.C4LNavigationContext
            {
                CurrentUnit = CurrentUnit,
                CurrentWeek = CurrentWeek,
                CurrentCalendarUnitWeeks = CurrentCalendarUnitWeeks,
                CurrentMondayDate = CurrentMondayDate,
                ClassroomStartDate = ClassroomStartDate,
                C4L_ClassroomId = c4l_classroomId
            };

            await SessionStateService.SetNavigationContextAsync(navigationContext);

            // Navigate back to the lessons page
            NavigationManager.NavigateTo($"/c4l-lessons");
        }
    }
}
