.c4l-form {
  border-radius: 0.75rem;
  border: 1px solid rgb(229, 231, 235);
  box-shadow: 
    0 1px 3px 0 rgba(0, 0, 0, .1),
    0 1px 2px -1px rgba(0, 0, 0, .1);
    padding: 1.5rem;
    width: min(100%, 45rem);
    margin-inline: auto;

    .form-label-input-wrapper {
      margin-block-end: 1rem;
    }

    .form-checkbox-label {
      display: flex;
      gap: 0.5rem;
      align-items: center;
      margin-block-end: 1rem;
    }

    .form-check-input {
      width: 1.25rem;
      height: 1.25rem;
      margin-block: 0;
      padding-inline: 0.5rem;

      &:checked {
        background-color: var(--c4l-primary-purple);
      }
    }

    &.modal-form {
      border: none;
      box-shadow: none;
      padding: 0;
    }
}

form {
  & label {
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--c4l-primary-purple);
    width: 100%;
  }

  & input,
  & select {
    font-size: 1rem;
    color: rgb(17, 24, 39);
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    width: 100%;
    border: 1px solid rgb(209, 213, 219);
    letter-spacing: 0.015em;
    background-color: var(--white);
  }

  & select {
    padding-block: 0.625rem;
  }

  & ul {
    list-style: none;
    text-align: center;
    width: 100%;
    border-radius: 0.25rem;
    padding-inline: 1rem;
    padding-block: 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    &[role="alert"] {
      border-inline-start: 0.375rem solid var(--c4l-danger);
      background: hsl(340 90% 92.5%);
    }
  }

  & .alert {
    margin-block: 0;
    border-radius: 0.25rem;
    transition: opacity var(--transition-speed) ease;

    &.fade {
      opacity: 0;

      &.show {
        opacity: 1;
      }
    }
  }

  & .validation-errors {
    border-inline-start: 0.375rem solid var(--c4l-danger);
    background: hsl(340 90% 92.5%);
  }
}

:where(.form-submit-buttons-wrapper) {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin-block-start: 2rem;

  & .c4l-button {
    width: 100%;
    text-align: center;
  }

  &.centered-buttons {
    justify-content: center;
  }
}

.form-control {
  letter-spacing: 0.015em;

  &:focus {
    box-shadow: none;
    border-color: var(--c4l-primary-purple);
  }
}

.form-input-wrapper {
  position: relative;
  width: 100%;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: -1;
    background-color: transparent;
    border-radius: 0.25rem;
    box-shadow: 0 0 6px hsl(278 37.3% 90%);
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
  }

  &:has(.form-control:focus) {
    &::before {
      opacity: 1;
    }
  }
}

.form-floating {
  & label {
    width: auto;
    opacity: 1 !important;
  }

   & .text-danger {
     padding-block-start: 0.3333rem;
   }
 }

.c4l-form-heading {
  margin-block-end: 1.75rem;
}

.form-label-input-wrapper:has(.form-input-wrapper input[type="checkbox"]) {
  display: flex;
  align-items: center;
  gap: 0.3333rem;
}

.form-input-wrapper {
  &:has(input[type="checkbox"]) {
    width: auto;
  }
}

.darker-border-checkbox.form-check-input {
  border-color: #929292;
}

@media (min-width: 48rem) {
  :where(.form-submit-buttons-wrapper) {
    flex-direction: row;
    
    & .c4l-button {
      width: fit-content;
    }
  }
}
