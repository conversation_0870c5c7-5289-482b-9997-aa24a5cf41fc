﻿@page "/account/disable-2fa"

@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity

@inject UserManager<ApplicationUser> UserManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager
@inject ILogger<Disable2fa> Logger

<PageTitle>Disable Two-Factor Authentication (2FA) | C4L</PageTitle>

<StatusMessage AlertType="@alertType" />

<form @formname="disable-2fa" @onsubmit="OnSubmitAsync" method="post" class="c4l-form">
    <AntiforgeryToken />
    <h3 class="c4l-form-heading mb-2">Disable two-factor authentication (2FA)</h3>

    <div class="alert alert-warning" role="alert">
        <strong>This action only disables 2FA.</strong>

        <p class="mb-0">Disabling 2FA does not change the keys used in authenticator apps. If you wish to change the key used in an authenticator app you should <a class="font-weight-500" href="account/reset-authenticator-key">reset your authenticator keys.</a>
        </p>
    </div>

    <button class="c4l-button c4l-form-button c4l-danger-button" type="submit">Disable 2FA</button>
</form>

@code {
    private string? alertType = "danger";
    private ApplicationUser user = default!;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);

        if (HttpMethods.IsGet(HttpContext.Request.Method) && !await UserManager.GetTwoFactorEnabledAsync(user))
        {
            throw new InvalidOperationException("Cannot disable 2FA for user as it's not currently enabled.");
        }
    }

    private async Task OnSubmitAsync()
    {
        var disable2faResult = await UserManager.SetTwoFactorEnabledAsync(user, false);
        if (!disable2faResult.Succeeded)
        {
            throw new InvalidOperationException("Unexpected error occurred disabling 2FA.");
        }

        var userId = await UserManager.GetUserIdAsync(user);
        Logger.LogInformation("User with ID '{UserId}' has disabled 2fa.", userId);
        RedirectManager.RedirectToWithStatus(
            "account/manage-2fa",
            "2fa has been disabled. You can reenable 2fa when you setup an authenticator app",
            HttpContext);
    }
}
