﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class UserStudentGroupLinkRepository : IUserStudentGroupLinkRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public UserStudentGroupLinkRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<UserStudentGroupLink> AddUserStudentGroupLinkAsync(UserStudentGroupLink userStudentGroupLink)
        {
            if (userStudentGroupLink is null)
            {
                throw new ArgumentNullException(nameof(userStudentGroupLink));
            }

            // Get the authentication state 
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            userStudentGroupLink.ModId = userId;
            userStudentGroupLink.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.UserStudentGroupLinks.AddAsync(userStudentGroupLink);
                await _dbContext.SaveChangesAsync();
            }

            return userStudentGroupLink;
        }

        public async Task<UserStudentGroupLink?> GetUserStudentGroupLinkAsync(long? organizationId, string? userId, long? accessId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (userId is null)
            {
                throw new ArgumentNullException(nameof(userId));
            }

            if (accessId is null)
            {
                throw new ArgumentNullException(nameof(accessId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.UserStudentGroupLinks.FirstOrDefaultAsync(o => o.OrganizationId == organizationId && o.UserId == userId && o.StudentGroupUserAccessId == accessId);
            }
        }

        public async Task<bool> RemoveUserStudentGroupLinkAsync(long? linkId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                UserStudentGroupLink? link = await _dbContext.UserStudentGroupLinks.FirstOrDefaultAsync(o => o.Id == linkId);

                if (link == null)
                {
                    return false; // link not found
                }

                _dbContext.UserStudentGroupLinks.Remove(link);
                await _dbContext.SaveChangesAsync();
                return true; // Successfully deleted
            }
        }
    }
}
