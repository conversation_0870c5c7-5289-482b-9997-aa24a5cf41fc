@inherits LayoutComponentBase
@inject IJSRuntime JSRuntime

<div class="empty-layout">
    @Body
</div>

<script>
    window.clearAllCookiesAndRedirect = function(url) {
        // Clear all cookies
        document.cookie.split(';').forEach(function(c) {
            document.cookie = c.trim().split('=')[0] + '=;' + 'expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/';
        });

        // Try to find and submit the logout form
        var logoutForm = document.getElementById('logoutForm');
        if (logoutForm) {
            logoutForm.submit();
            return true;
        }

        // If no form found, try direct redirect
        window.location.href = url;

        return true;
    }
</script>

<style>
    .empty-layout {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: #f5f5f5;
    }

    .invite-container {
        width: 100%;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
    }

    .invite-card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 30px;
    }

    .invite-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .invite-logo {
        max-width: 150px;
        margin-bottom: 20px;
    }

    .invite-header h1 {
        color: #6a1b9a; /* C4L purple color */
        font-size: 24px;
        margin-bottom: 10px;
    }

    .form-control {
        width: 100%;
        padding: 10px;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .btn-primary {
        background-color: #6a1b9a;
        border-color: #6a1b9a;
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
    }

    .btn-primary:hover {
        background-color: #5c1786;
    }

    .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 4px;
    }

    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .text-danger {
        color: #dc3545;
    }
</style>
