﻿@page "/c4l-noncontactday-addedit"

@using Compass.C4L.Interfaces.Services
@using Compass.C4L.Models
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Services
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserAccessor UserAccessor
@inject UserSessionService UserSessionService

<div class="component-content-wrapper non-contact-days-wrapper">
    <div class="header-wrapper">
        <h3 class="page-title">@(isEditMode ? "Edit" : "Add") Non-Contact Day</h3>
    </div>

    <EditForm Model="nonContactDay" FormName="formNonContactDay" class="c4l-form non-contact-days-form">
        <DataAnnotationsValidator />
        <ValidationSummary />

        <FieldComponent Label="Start Date">
            <Control>
                <InputDate @bind-Value="nonContactDay.StartDate" class="form-control" />
                <ValidationMessage For="@(() => nonContactDay.StartDate)" />
            </Control>
        </FieldComponent>

        <FieldComponent Label="End Date">
            <Control>
                <InputDate @bind-Value="nonContactDay.EndDate" class="form-control" />
                <ValidationMessage For="@(() => nonContactDay.EndDate)" />
            </Control>
        </FieldComponent>

        <FieldComponent Label="Description">
            <Control>
                <InputText @bind-Value="nonContactDay.Description" class="form-control" />
            </Control>
        </FieldComponent>

        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger" role="alert">
                @errorMessage
            </div>
        }

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success" role="alert">
                @successMessage
            </div>
        }

        <div class="form-submit-buttons-wrapper">
            <button class="c4l-button c4l-primary-button" type="button" @onclick="HandleSaveClick">Save</button>
            <button class="c4l-button c4l-secondary-button" type="button" @onclick="OnCancelClick">Cancel</button>
        </div>
    </EditForm>
</div>

<style>
    .header-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .page-title {
        margin: 0;
    }

    .form-submit-buttons-wrapper {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
    }

    .alert {
        margin-top: 1rem;
    }
</style>

