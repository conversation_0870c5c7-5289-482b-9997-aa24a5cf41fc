using Microsoft.AspNetCore.Components;

namespace Compass.Common.Controls.Generic
{
    public partial class NoTableDataMessage : IDisposable
    {
        [Parameter]
        public string? MessageText { get; set; }

        [Parameter]
        public string? MessageStyle { get; set; }

        protected override void OnInitialized()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);
            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
