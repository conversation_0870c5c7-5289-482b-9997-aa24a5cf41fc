﻿using Compass.Common.Data;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity2
{
    public partial class Entity2EditComponent : IDisposable
    {
        private long? entity2Id;
        private Compass.Common.Models.Entity2? entity2 { get; set; }

        private string? successMessage = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.entity2Id = commonSessionData.CurrentEntity2Id;
                if (this.entity2Id > 0)
                {
                    this.entity2 = await Entity2Repository.GetEntity2Async(this.entity2Id);
                }
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        protected async Task SubmitAsync()
        {
            if (this.entity2 is not null)
            {
                successMessage = string.Empty;

                CommonSessionData? commonSessionData = await GetCommonSessionData();
                if (commonSessionData is not null)
                {
                    this.entity2.OrganizationId = commonSessionData.CurrentOrganizationId;
                    this.entity2.Entity1Id = commonSessionData.CurrentEntity1Id;

                    this.entity2 = await Entity2Repository.UpdateEntity2Async(this.entity2Id, this.entity2);

                    if (this.entity2 != null)
                    {
                        commonSessionData.SelectedEntityName = this.entity2.Name;
                    }

                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();

                    successMessage = "Information saved successfully!";
                }
            }
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
