using Compass.LAP.Resources.Instruments;
using Compass.LAP.Resources.SmartAssessment.Diagnostic;
using Compass.LAP.Resources.SmartAssessment.Ongoing;
using Compass.LAP.Resources.SmartAssessment.Screen;

namespace Compass.LAP.Resources.SmartAssessment.Primary
{
    public abstract class LapAssessment
    {
        public long? Id { get; set; }
        public int? Instrument { get; set; }
        public DateTime? DateOfAssessment { get; set; }

        protected int? chronologicalAge;
        protected List<AssessmentItem> assessmentItems = new List<AssessmentItem>();

        public static LapAssessment? Create(int instrument, int? chronologicalAge = null)
        {
            LapAssessment? result = null;

            switch (instrument)
            {
                case AssessmentLevel.E_LAP:
                    result = new ElapAssessment();
                    break;
                case AssessmentLevel.LAP_3:
                    result = new Lap3Assessment();
                    break;
                case AssessmentLevel.LAP_D3:
                    result = new LapD3Assessment();
                    break;
                case AssessmentLevel.LAP_D_SCREEN:
                    int screenLevel = GetLapDScreenAge(chronologicalAge);
                    switch (screenLevel)
                    {
                        case AssessmentLevel.LAP_Screen_3YO:
                            result = new LapScreen3yoAssessment();
                            break;
                        case AssessmentLevel.LAP_Screen_4YO:
                            result = new LapScreen4yoAssessment();
                            break;
                        case AssessmentLevel.LAP_Screen_5YO:
                            result = new LapScreen5yoAssessment();
                            break;
                    }
                    break;
                case AssessmentLevel.LAP_BK:
                    result = new LapBK_Assessment();
                    break;
            }

            return result;
        }

        private static int GetLapDScreenAge(int? chronologicalAge)
        {
            if (chronologicalAge == null)
            {
                return AssessmentLevel.LAP_Screen_3YO; // Default to 3YO if age is not provided
            }

            int age = chronologicalAge.Value;

            if (age <= LapScreenAssessment.MAX_AGE_3YO)
            {
                return AssessmentLevel.LAP_Screen_3YO;
            }
            if (age >= LapScreenAssessment.MIN_AGE_4YO && age <= LapScreenAssessment.MAX_AGE_4YO)
            {
                return AssessmentLevel.LAP_Screen_4YO;
            }
            if (age >= LapScreenAssessment.MIN_AGE_5YO && age <= LapScreenAssessment.MAX_AGE_5YO)
            {
                return AssessmentLevel.LAP_Screen_5YO;
            }

            // Default to 3YO if age doesn't fall into any specific range
            return AssessmentLevel.LAP_Screen_3YO;
        }

        public int? ChronologicalAge
        {
            get { return chronologicalAge; }
            set { chronologicalAge = value; }
        }

        public List<AssessmentItem> AssessmentItems
        {
            get { return assessmentItems; }
        }

        public virtual void SetAssessmentItems(List<AssessmentItem>? items, bool replace)
        {
            if (replace || items == null)
            {
                assessmentItems = new List<AssessmentItem>();
            }
            else
            {
                if (assessmentItems == null)
                {
                    assessmentItems = new List<AssessmentItem>();
                }
                foreach (var item in items)
                {
                    assessmentItems.Add(item);
                }
            }
        }

        protected List<AssessmentItem> GetAssessmentItemsBySubscale(string? subscaleID)
        {
            List<AssessmentItem> result = new List<AssessmentItem>();
            foreach (AssessmentItem item in AssessmentItems)
            {
                int? assessmentLevel = item.Instrument;
                int? subscaleSequence = item.SubscaleSequence;

                if (item.SubscaleID != null && (item.SubscaleID.Equals(subscaleID) || Subscale.IsDualLanguage(assessmentLevel, subscaleSequence) && item.SubscaleID.StartsWith("D")))
                {
                    result.Add(item);
                }
            }
            result.Sort();

            return result;
        }

        protected virtual AssessmentItem? AddSkill(Milestone? masteredItem)
        {
            if (masteredItem != null)
            {
                List<AssessmentItem> items = AssessmentItems;
                AssessmentItem item = AssessmentItem.Create(masteredItem);

                if (AssessmentItems != null && AssessmentItems.Contains(item))
                {
                    item = items[items.IndexOf(item)];
                }
                else
                {
                    // create case
                    if (AssessmentItems == null)
                    {
                        SetAssessmentItems(new List<AssessmentItem>(50), true);
                        //There is no way for assessment items to be null after SetAssessmentItems is called
                        if (AssessmentItems != null)
                        {
                            items = AssessmentItems;
                        }
                    }
                    AddAssessmentItem(item);
                    item.ChronologicalAge = ChronologicalAge;
                    item.Domain = masteredItem.Domain;
                    item.DomainSequence = masteredItem.DomainSequence;
                    item.Instrument = masteredItem.Instrument;
                    item.ItemID = masteredItem.ItemID;
                    item.Subscale = masteredItem.Subscale;
                    item.SubscaleID = masteredItem.SubscaleID;
                    item.SubscaleSequence = masteredItem.SubscaleSequence;
                    item.SubscaleStaticID = masteredItem.SubscaleStaticID;
                    item.ItemStaticID = masteredItem.ItemStaticID;
                }
                return item;
            }
            else
            {
                return null;
            }
        }

        public void SetSkillValue(Skill skill, List<Milestone> milestones, int? lastItemSequence, bool autoScore, bool autoScoreFwd, int skillValue)
        {
            // add to assessment items
            AssessmentItem? item = AddSkill(skill.Milestone);
            if (item != null)
            {
                item.Scored = true;
                if (item.Id == null)
                {
                    item.AssessDate = skill.Date;
                    item.SchoolYear = skill.SchoolYear;
                    if (skill.Milestone != null)
                    {
                        item.Instrument = skill.Milestone.Instrument;
                    }
                }
                item.UserComment = skill.Notes;
                item.SelectedItems = skill.Selections;
                if (item.Value != null && item.Value != skillValue)
                {
                    item.OriginalValue = item.Value;
                }
                item.Value = skillValue;
                item.AchieveDate = skill.Date;

                UpdateItemBeforeAutoScore(item, skill, milestones, lastItemSequence);

                AutoScore(skill, milestones, lastItemSequence, autoScore, autoScoreFwd);
            }
        }

        public void SetMasteredSkill(Skill skill, List<Milestone> milestones, int? lastItemSequence, bool autoScore, bool autoScoreFwd)
        {
            // add to assessment items
            AssessmentItem? item = AddSkill(skill.Milestone);
            if (item != null)
            {
                item.Scored = true;
                if (item.Id == null)
                {
                    item.AssessDate = skill.Date;
                    item.SchoolYear = skill.SchoolYear;
                    if (skill.Milestone != null)
                    {
                        item.Instrument = skill.Milestone.Instrument;
                    }
                }
                item.UserComment = skill.Notes;
                item.SelectedItems = skill.Selections;
                if (item.Value != null && item.Value != Skill.MASTERED)
                {
                    item.OriginalValue = item.Value;
                }
                item.Value = Skill.MASTERED;
                item.AchieveDate = skill.Date;

                UpdateItemBeforeAutoScore(item, skill, milestones, lastItemSequence);

                AutoScore(skill, milestones, lastItemSequence, autoScore, autoScoreFwd);
            }
        }

        public void SetEmergingSkill(Skill skill, List<Milestone> milestones, int? lastItemSequence, bool autoScore, bool autoScoreFwd)
        {
            // add to assessment items
            AssessmentItem? item = AddSkill(skill.Milestone);
            if (item != null)
            {
                item.Scored = true;
                if (item.Id == null)
                {
                    item.AssessDate = skill.Date;
                    item.SchoolYear = skill.SchoolYear;
                    if (skill.Milestone != null)
                    {
                        item.Instrument = skill.Milestone.Instrument;
                    }
                }
                item.UserComment = skill.Notes;
                item.SelectedItems = skill.Selections;
                if (item.Value != null && item.Value != Skill.EMERGING)
                {
                    item.OriginalValue = item.Value;
                }
                item.Value = Skill.EMERGING;
                item.AchieveDate = null;

                UpdateItemBeforeAutoScore(item, skill, milestones, lastItemSequence);

                AutoScore(skill, milestones, lastItemSequence, autoScore, autoScoreFwd);
            }
        }

        protected abstract void UpdateItemBeforeAutoScore(AssessmentItem item, Skill skill, List<Milestone> milestones, int? lastItemSequence);

        public abstract Milestone GetNextItem(Milestone previousItem, int? previousScore, ICollection<Milestone> milestones);

        protected abstract void CopyAdditionalData(LapAssessment result);

        public void AddAssessmentItem(AssessmentItem item)
        {
            AssessmentItems.Add(item);
        }

        public void AutoScore(Skill skill, List<Milestone> milestones, int? lastItemSequence, bool autoScore, bool autoScoreFwd)
        {
            if (skill.Milestone != null)
            {
                Milestone milestone = skill.Milestone;
                if (milestone.AutoScoreItemNo == null && milestone.AutoScoreItemNoFwd == null)
                {
                    return;
                }

                if (milestone.AutoScoreItemNo != null && autoScore)
                {
                    Milestone m = new Milestone();
                    m.Instrument = milestone.Instrument;
                    m.Language = milestone.Language;
                    m.ItemID = milestone.AutoScoreItemNo;
                    m = milestones[milestones.IndexOf(m)];
                    skill.Milestone = m;

                    int? score = GetAutoScoreListScore(skill, m);

                    if (score == Skill.MASTERED)
                    {
                        SetMasteredSkill(skill, milestones, lastItemSequence, true, false);
                    }
                    else
                    {
                        SetEmergingSkill(skill, milestones, lastItemSequence, true, false);
                    }
                }

                if (milestone.AutoScoreItemNoFwd != null && autoScoreFwd)
                {
                    Milestone m = new Milestone();
                    m.Instrument = milestone.Instrument;
                    m.Language = milestone.Language;
                    m.ItemID = milestone.AutoScoreItemNoFwd;
                    m = milestones[milestones.IndexOf(m)];
                    skill.Milestone = m;

                    int? score = GetAutoScoreListScore(skill, m);

                    if (score == Skill.MASTERED)
                    {
                        SetMasteredSkill(skill, milestones, lastItemSequence, false, true);
                    }
                    else
                    {
                        SetEmergingSkill(skill, milestones, lastItemSequence, false, true);
                    }
                }
            }
        }

        private int? GetAutoScoreListScore(Skill skill, Milestone milestone)
        {
            int? ret = skill.Type;
            if (skill.Selections != null && milestone.MinSelectionRequired != null)
            {
                if (skill.Selections.Split(Milestone.ITEM_SEPARATOR).Length >= milestone.MinSelectionRequired.Value)
                {
                    ret = Skill.MASTERED;
                }
                else
                {
                    ret = Skill.EMERGING;
                }
            }
            return ret;
        }

        public abstract Milestone GetStartingPoint(ICollection<Milestone> milestones);

        public abstract Milestone? FindFirstItem(ICollection<Milestone> milestones);

        public Milestone? GetLastStartingPoint(ICollection<Milestone> milestones)
        {
            List<Milestone> milestonesList = milestones.ToList();
            int i = milestonesList.Count - 1;
            int? highestAge = milestonesList[i].Months;
            bool found = false;
            for (; milestonesList[i].Months >= highestAge; i--)
            {
                found = true;
            }
            Milestone? milestone = found ? milestonesList[++i] : null;

            return milestone;
        }

        public static Milestone? GetManualStartingPoint(ICollection<Milestone> milestones, int? chronologicalAge)
        {
            List<Milestone> milestonesList = milestones.ToList();
            int i = milestonesList.Count - 1;
            bool found = false;
            for (; i >= 0 && milestonesList[i].Months >= chronologicalAge; i--)
            {
                found = true;
            }
            Milestone? milestone = found ? milestonesList[++i] : null;
            return milestone;
        }
    }
}




