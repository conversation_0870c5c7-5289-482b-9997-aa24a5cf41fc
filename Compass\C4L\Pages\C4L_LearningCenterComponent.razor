@using Compass.C4L.Interfaces.Services
@using Compass.C4L.Models
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@inject UserAccessor UserAccessor
@inject UserSessionService UserSessionService
@* Remove this line as it's already in the code-behind file *@
@* @inject IC4LLearningCenterService LearningCenterService *@

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="component-content-wrapper learning-center-wrapper">
    <div class="filter-controls d-flex align-items-center">
        <label for="unitSelect" class="me-2">Unit</label>
        <select id="unitSelect" class="form-select me-2" @bind="SelectedUnit">
            <option value="1">Unit 1</option>
            <option value="2">Unit 2</option>
            <option value="3">Unit 3</option>
            <option value="4">Unit 4</option>
            <option value="5">Unit 5</option>
            <option value="6">Unit 6</option>
        </select>
        <button class="c4l-button c4l-primary-button" @onclick="LoadLearningCenters">Select</button>
    </div>

    <div class="learning-centers-table mt-4">
        <table class="w-100">
            @{
                int cellCount = 0;
                int totalCells = LearningCenters.Count;
                int cellsPerRow = 3;
            }

            @for (int row = 0; row < Math.Ceiling((double)totalCells / cellsPerRow); row++)
            {
                <tr>
                    @for (int col = 0; col < cellsPerRow; col++)
                    {
                        int index = row * cellsPerRow + col;
                        if (index < totalCells)
                        {
                            C4LLearningCenter center = LearningCenters[index];
                            <td style="width: 33.33%; padding: 10px; vertical-align: top;">
                                <div class="learning-center-item">
                                    <div class="d-flex align-items-start">
                                        <i class="bi bi-book me-2"></i>
                                        <span>@center.Title</span>
                                    </div>
                                    <div class="activity-status mt-2 text-muted">
                                        No Activities Selected
                                    </div>
                                </div>
                            </td>
                        }
                        else
                        {
                            <td style="width: 33.33%;"></td>
                        }
                    }
                </tr>
            }
        </table>
    </div>
</div>
