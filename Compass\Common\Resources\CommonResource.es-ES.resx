﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="login-text" xml:space="preserve">
    <value>Iniciar sesión</value>
  </data>
  <data name="login-page-heading" xml:space="preserve">
    <value>Iniciar sesión con una cuenta personal</value>
  </data>
  <data name="login-page-form-heading" xml:space="preserve">
    <value>Usar una cuenta personal para iniciar sesión</value>
  </data>
  <data name="login-page-remember-me" xml:space="preserve">
    <value>¿Mantenerme conectado?</value>
  </data>
  <data name="login-page-forgot-password" xml:space="preserve">
    <value>¿Olvidó la contraseña?</value>
  </data>
  <data name="login-page-resend-email" xml:space="preserve">
    <value>Reenviar correo electrónico de confirmación</value>
  </data>
  <data name="lbl_Address2" xml:space="preserve">
    <value>Dirección 2</value>
  </data>
  <data name="btn_Add" xml:space="preserve">
    <value>Agregar</value>
  </data>
  <data name="btn-update" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="lbl_Address1" xml:space="preserve">
    <value>Dirección 1</value>
  </data>
  <data name="lbl_Name" xml:space="preserve">
    <value>Nombre</value>
  </data>
  <data name="msg_InfoSaved" xml:space="preserve">
    <value>¡Información guardada exitosamente!</value>
  </data>
  <data name="msg_InfoUpdated" xml:space="preserve">
    <value>¡Información actualizada exitosamente!</value>
  </data>
  <data name="lbl_City" xml:space="preserve">
    <value>Ciudad</value>
  </data>
  <data name="lbl_State" xml:space="preserve">
    <value>Estado</value>
  </data>
  <data name="lbl_ZipCode" xml:space="preserve">
    <value>Código Postal</value>
  </data>
  <data name="lbl_ContactFirstName" xml:space="preserve">
    <value>Nombre del Contacto</value>
  </data>
  <data name="lbl_ContactLastName" xml:space="preserve">
    <value>Contacto Apellido</value>
  </data>
  <data name="lbl_ContactEmail" xml:space="preserve">
    <value>Correo Electrónico de Contacto</value>
  </data>
  <data name="lbl_ContactPhone" xml:space="preserve">
    <value>Teléfono de Contacto</value>
  </data>
  <data name="lbl_ContactFax" xml:space="preserve">
    <value>Contacto Fax</value>
  </data>
  <data name="lbl_Fax" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="lbl_About" xml:space="preserve">
    <value>Acerca de</value>
  </data>
  <data name="btn_save" xml:space="preserve">
    <value>Ahorrar</value>
  </data>
  <data name="btn_close" xml:space="preserve">
    <value>Cerrar</value>
  </data>
  <data name="err_HierarchyOrder" xml:space="preserve">
    <value>Hierarchy Order is incorrect</value>
  </data>
  <data name="err_Entity1HasActiveEntity2s" xml:space="preserve">
    <value>The Entity 1 you are trying to delete still has active Entity 2s</value>
  </data>
  <data name="err_Entity1HasActiveSites" xml:space="preserve">
    <value>The Entity 1 you are trying to delete still has active Sites</value>
  </data>
  <data name="err_Entity2HasActiveEntity3s" xml:space="preserve">
    <value>The Entity 2 you are trying to delete still has active Entity 3s</value>
  </data>
  <data name="err_Entity2HasActiveSites" xml:space="preserve">
    <value>The Entity 2 you are trying to delete still has active Sites</value>
  </data>
  <data name="err_Entity3HasActiveSites" xml:space="preserve">
    <value>The Entity 3 you are trying to delete still has active Sites</value>
  </data>
  <data name="err_SiteHasActiveClassrooms" xml:space="preserve">
    <value>The Site you are trying to delete still has active Student Groups</value>
  </data>
  <data name="err_UsersAssigned" xml:space="preserve">
    <value>Asignado por el usuario</value>
  </data>
  <data name="lbl_Search" xml:space="preserve">
    <value>Buscar</value>
  </data>
  <data name="lbl_AssignUser" xml:space="preserve">
    <value>Asignar Usuario</value>
  </data>
  <data name="lbl_InviteUser" xml:space="preserve">
    <value>Invitar Usuario</value>
  </data>
  <data name="lbl_Username" xml:space="preserve">
    <value>Nombre de Usuario</value>
  </data>
  <data name="password-label" xml:space="preserve">
    <value>Contraseña</value>
  </data>
  <data name="lbl_Email" xml:space="preserve">
    <value>Correo Electrónico</value>
  </data>
  <data name="lbl_OrganizationUserList" xml:space="preserve">
    <value>Lista de Usuarios Organizaciones</value>
  </data>
  <data name="org-addedit-details" xml:space="preserve">
    <value>Información de la Organización</value>
  </data>
  <data name="lbl_SearchAllFields" xml:space="preserve">
    <value>Buscar en todos los campos</value>
  </data>
  <data name="lbl_FirstName" xml:space="preserve">
    <value>Nombre de Pila</value>
  </data>
  <data name="lbl_LastName" xml:space="preserve">
    <value>Apellido</value>
  </data>
  <data name="lbl_Previous" xml:space="preserve">
    <value>Previo</value>
  </data>
  <data name="lbl_Next" xml:space="preserve">
    <value>Próximo</value>
  </data>
  <data name="lbl_Remove" xml:space="preserve">
    <value>Eliminar</value>
  </data>
  <data name="lbl_CurrentPage" xml:space="preserve">
    <value>Página actual</value>
  </data>
  <data name="lbl_of" xml:space="preserve">
    <value>de</value>
  </data>
  <data name="lbl_Summary" xml:space="preserve">
    <value>Resumen</value>
  </data>
  <data name="lbl_Manage" xml:space="preserve">
    <value>Administrar</value>
  </data>
  <data name="lbl_Edit" xml:space="preserve">
    <value>Editar</value>
  </data>
  <data name="lbl_Support_Resources" xml:space="preserve">
    <value>Soporte y Recursos</value>
  </data>
  <data name="lbl_Reports" xml:space="preserve">
    <value>Informes</value>
  </data>
  <data name="lbl_Users" xml:space="preserve">
    <value>Usuarios</value>
  </data>
  <data name="lbl_ContactInformation" xml:space="preserve">
    <value>Información del contacto</value>
  </data>
  <data name="lbl_ProductName" xml:space="preserve">
    <value>Nombre del producto</value>
  </data>
  <data name="lbl_Begin" xml:space="preserve">
    <value>Comenzar</value>
  </data>
  <data name="lbl_End" xml:space="preserve">
    <value>Fin</value>
  </data>
  <data name="lbl_AccountType" xml:space="preserve">
    <value>Tipo de cuenta</value>
  </data>
  <data name="lbl_ActiveLicenses" xml:space="preserve">
    <value>Licencias activas</value>
  </data>
  <data name="lbl_ArchiveLicenses" xml:space="preserve">
    <value>Licencias de archivo</value>
  </data>
  <data name="lbl_ActiveInUse" xml:space="preserve">
    <value>Activo en uso</value>
  </data>
  <data name="lbl_ArchiveInUse" xml:space="preserve">
    <value>Archivo en uso</value>
  </data>
  <data name="lbl_Entity" xml:space="preserve">
    <value>Entidad</value>
  </data>
  <data name="lbl_OrganizationList" xml:space="preserve">
    <value>Listo de Organizaciones</value>
  </data>
  <data name="lbl_AddOrganization" xml:space="preserve">
    <value>Añadir Organización</value>
  </data>
  <data name="lbl_EditOrganization" xml:space="preserve">
    <value>Editar Organización</value>
  </data>
  <data name="lbl_OrganizationName" xml:space="preserve">
    <value>Nombre de Organización</value>
  </data>
  <data name="lbl_MFARequired" xml:space="preserve">
    <value>Se requiere maestría en bellas artes</value>
  </data>
  <data name="lbl_Update" xml:space="preserve">
    <value>Actualizar</value>
  </data>
  <data name="lbl_AddRegion" xml:space="preserve">
    <value>Agregar </value>
  </data>
  <data name="lbl_Add" xml:space="preserve">
    <value>Agregar</value>
  </data>
  <data name="lbl_Save" xml:space="preserve">
    <value>Ahorrar</value>
  </data>
  <data name="lbl_Phone" xml:space="preserve">
    <value>Teléfono</value>
  </data>
  <data name="lbl_list" xml:space="preserve">
    <value>Lista</value>
  </data>
  <data name="lbl_DistrictName" xml:space="preserve">
    <value>Nombre</value>
  </data>
  <data name="lbl_DistrictList" xml:space="preserve">
    <value>Lista</value>
  </data>
  <data name="lbl_ContactName" xml:space="preserve">
    <value>Nombre de contacto</value>
  </data>
  <data name="lbl_UserList" xml:space="preserve">
    <value>Lista de Usuarios</value>
  </data>
  <data name="lbl_adduser" xml:space="preserve">
    <value>Agregar Usuario</value>
  </data>
  <data name="lbl_RemoveUser" xml:space="preserve">
    <value>¿Eliminar Usuario?</value>
  </data>
  <data name="lbl_home" xml:space="preserve">
    <value>Inicio</value>
  </data>
  <data name="lbl_RegionName" xml:space="preserve">
    <value>Nombre</value>
  </data>
  <data name="lbl_RegionList" xml:space="preserve">
    <value>Lista</value>
  </data>
  <data name="lbl_AddDistrict" xml:space="preserve">
    <value>Agregar </value>
  </data>
  <data name="lbl_ZoneName" xml:space="preserve">
    <value>Nombre</value>
  </data>
  <data name="lbl_AddZone" xml:space="preserve">
    <value>Agregar zona</value>
  </data>
  <data name="lbl_QuickViews" xml:space="preserve">
    <value>Vistas rápidas</value>
  </data>
  <data name="lbl_AddSchool" xml:space="preserve">
    <value>Agregar escuela</value>
  </data>
  <data name="lbl_Region" xml:space="preserve">
    <value>Región</value>
  </data>
  <data name="lgl_District" xml:space="preserve">
    <value>Distrito</value>
  </data>
  <data name="lbl_Zone" xml:space="preserve">
    <value>Zona</value>
  </data>
  <data name="lbl_School" xml:space="preserve">
    <value>Escuela</value>
  </data>
  <data name="classroom-information-text" xml:space="preserve">
    <value>Información del Aula</value>
  </data>
  <data name="lbl_Classrooms" xml:space="preserve">
    <value>Aulas</value>
  </data>
  <data name="lbl_Numberof" xml:space="preserve">
    <value>Total de Aulas</value>
  </data>
  <data name="lbl_Children" xml:space="preserve">
    <value>Niños</value>
  </data>
  <data name="lbl_Active" xml:space="preserve">
    <value>Activo</value>
  </data>
  <data name="lbl_Archived" xml:space="preserve">
    <value>Archivado</value>
  </data>
  <data name="lbl_Pending" xml:space="preserve">
    <value>Pendiente</value>
  </data>
  <data name="lbl_AddClassroom" xml:space="preserve">
    <value>Agregar aula</value>
  </data>
  <data name="lbl_Checkpoints" xml:space="preserve">
    <value>Puestos de control</value>
  </data>
  <data name="lbl_SchoolYears" xml:space="preserve">
    <value>Años escolares</value>
  </data>
  <data name="lbl_accessibility" xml:space="preserve">
    <value>Accesibilidad</value>
  </data>
  <data name="lbl_teachers" xml:space="preserve">
    <value>Maestro(s)</value>
  </data>
  <data name="classroom_standard" xml:space="preserve">
    <value>Estándar</value>
  </data>
  <data name="classroom_specialist" xml:space="preserve">
    <value>Especializado</value>
  </data>
  <data name="no-users-text" xml:space="preserve">
    <value>No hay usuarios para esta entidad. Agrega un usuario para poder verlo en la lista de usuarios.</value>
  </data>
  <data name="no-data-text" xml:space="preserve">
    <value>No hay información para mostrar.</value>
  </data>
  <data name="manage-account-settings" xml:space="preserve">
    <value>Administra la configuración de tu cuenta</value>
  </data>
</root>