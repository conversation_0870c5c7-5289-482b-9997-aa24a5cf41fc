using System.Text.Json.Serialization;

namespace Compass.C4L.DTOs
{
    /// <summary>
    /// DTO for C4L lesson content retrieved from the CMS API
    /// </summary>
    public class C4LLessonContentDto
    {
        /// <summary>
        /// At a glance summary of the lesson
        /// </summary>
        public string AtAGlance { get; set; } = string.Empty;

        /// <summary>
        /// Materials needed for the lesson
        /// </summary>
        public string Materials { get; set; } = string.Empty;

        /// <summary>
        /// Objectives for the lesson
        /// </summary>
        public C4LLessonObjectivesDto Objectives { get; set; } = new C4LLessonObjectivesDto();

        /// <summary>
        /// Instructions for the lesson
        /// </summary>
        public List<string> Instructions { get; set; } = new List<string>();

        /// <summary>
        /// Preparations for the lesson
        /// </summary>
        public List<string> PreparationTasks { get; set; } = new List<string>();

        /// <summary>
        /// Metadata about the lesson
        /// </summary>
        public C4LLessonMetadataDto Metadata { get; set; } = new C4LLessonMetadataDto();
    }

    /// <summary>
    /// DTO for C4L lesson objectives
    /// </summary>
    public class C4LLessonObjectivesDto
    {
        /// <summary>
        /// Assessed objectives for the lesson
        /// </summary>
        public List<string> Assessed { get; set; } = new List<string>();

        /// <summary>
        /// Non-assessed objectives for the lesson
        /// </summary>
        public List<string> NonAssessed { get; set; } = new List<string>();
    }

    /// <summary>
    /// DTO for C4L lesson metadata
    /// </summary>
    public class C4LLessonMetadataDto
    {
        /// <summary>
        /// Language of the lesson
        /// </summary>
        public string Language { get; set; } = string.Empty;

        /// <summary>
        /// Unit number
        /// </summary>
        public int Unit { get; set; }

        /// <summary>
        /// Week number
        /// </summary>
        public int Week { get; set; }

        /// <summary>
        /// Day number
        /// </summary>
        public int Day { get; set; }

        /// <summary>
        /// Lesson type sequence
        /// </summary>
        public int LessonTypeSequence { get; set; }

        /// <summary>
        /// Title sequence
        /// </summary>
        public int TitleSequence { get; set; }
    }
}
