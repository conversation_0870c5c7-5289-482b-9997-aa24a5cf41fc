﻿@page "/"
@using System.Globalization
@using Compass.Common.Resources
@using Compass.Common.Resources.Home
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.Extensions.Localization
@inject IStringLocalizer<HomeResource> HomeLocalizer
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService
@implements IDisposable

@rendermode @(new InteractiveServerRenderMode(prerender: true))

<PageTitle>Homepage | C4L</PageTitle>

<h1 class="page-title horizontal-line">@HomeLocalizer["lbl_HelloWorld"]!</h1>

@code {
    protected override void OnInitialized()
    {
        CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);
    }

    private void UpdateLocalizedValues()
    {
        var culture = CurrentLanguageObserver.GetCurrentCulture();
        CultureService.SetCulture(culture);

        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
    }
}
