﻿using Compass.C4L.DTOs;
using Compass.C4L.Helpers;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Data;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Options;

namespace Compass.C4L.Pages.Student
{
    public partial class GameCenterComponent(IOptions<GodotGameSettings> godotGameSettingsAccessor)
    {
        [Inject]
        public required IGameTokenService GameTokenService { get; set; }
        [Inject]
        public required UserSessionService UserSessionService { get; set; }
        [Inject]
        public required UserAccessor UserAccessor { get; set; }
        [Inject]
        public required IC4LClassroomService C4LClassroomService { get; set; }

        public GodotGameSettings GameSettings { get; } = godotGameSettingsAccessor.Value;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private long? organizationId;
        private long? studentId;
        private string? studentName;

        private bool hasClassroomSelected = false;

        private int? unit;
        private int? week;

        private List<C4LClassroomSelectionDisplayDto> c4lClassroomSelectionList = new List<C4LClassroomSelectionDisplayDto>();

        private string GameUrl = string.Empty;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            (ApplicationUser? user, string? userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;

            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            hasClassroomSelected = false;
            CommonSessionData? sessionData = await GetCommonSessionData();
            GameUrl = string.Empty;

            if (sessionData != null)
            {
                organizationId = sessionData.CurrentOrganizationId;
                studentId = sessionData.CurrentStudentId;
                studentName = sessionData.SelectedEntityName;

                c4lClassroomSelectionList = await C4LClassroomService.GetStudentC4LClassroomsAsync(organizationId, studentId);

                if (c4lClassroomSelectionList.Count == 1)
                {
                    C4LClassroomSelectionDisplayDto dto = c4lClassroomSelectionList[0];
                    await SelectC4LClassroom(dto);
                }
            }
        }

        private async Task SelectC4LClassroom(C4LClassroomSelectionDisplayDto dto)
        {
            hasClassroomSelected = true;
            //TODO figure out the unit and week
            unit = 9;
            week = 9;

            long c4lClassroomId = dto.Id;
            await GenerateGameUrl(c4lClassroomId);
        }

        private async Task GenerateGameUrl(long c4lClassroomId)
        {
            if (this.studentId != null && this.unit != null && this.week != null && this.organizationId != null)
            {
                GameParameters gameParams = new GameParameters();
                gameParams.Unit = (int)this.unit;
                gameParams.Week = (int)this.week;
                gameParams.StudentId = (long)this.studentId;
                gameParams.C4LClassroomId = c4lClassroomId;
                gameParams.OrganizationId = (long)this.organizationId;
                gameParams.StudentName = this.studentName ?? string.Empty;
                gameParams.ExpiresAt = DateTime.UtcNow.AddMinutes(15);

                C4LGameToken gameToken = await GameTokenService.CreateOneTimeToken(gameParams);
                string token = gameToken.Token;
                //TODO Need to get this from a json file
                GameUrl = GameSettings.GodotGameUrl + $"?access_token={token}";
            }
            else
            {
                GameUrl = string.Empty;
            }
        }
    }
}
