﻿using Compass.Common.DTOs.Site;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface ISiteRepository
    {
        Task<List<Site>?> GetSitesAsync(long organizationId, long entity3Id);
        Task<Site> CreateSiteAsync(Site site);
        Task<Site?> GetSiteAsync(long? id);
        Task<Site?> UpdateSiteAsync(long? id, Site site);
        Task<List<SiteListDisplayDto>> GetSiteList(SiteListAction action);
        Task<int> GetSiteCount(long? organizationId, long? entity1Id, long? entity2Id, long? entity3Id, string queryText);
        Task<bool> DeleteSite(long? id);
        Task<int> GetActiveStudentCount(long? organizationId, long? siteId);
    }
}