using Compass.Common.Data;
using Compass.Common.DTOs.LicensePool;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Runtime.CompilerServices;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class LicensePoolRepository : ILicensePoolRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public LicensePoolRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public ApplicationDbContext CreateDbContext()
        {
            return _contextFactory.CreateDbContext();
        }

        public async Task<LicensePool?> GetLicensePoolAsync(long? licensePoolId)
        {
            if (licensePoolId is null)
            {
                throw new ArgumentNullException(nameof(licensePoolId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.LicensePools.FirstOrDefaultAsync(lp => lp.Id == licensePoolId);
            }
        }

        public async Task<LicensePool?> UpdateLicensePoolAsync(long? id, LicensePool licensePool)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (licensePool is null)
            {
                throw new ArgumentNullException(nameof(licensePool));
            }

            if (licensePool.Id != id)
            {
                throw new ArgumentException("License pool id does not match the id in the license pool object.");
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                LicensePool? existingLicensePool = await _dbContext.LicensePools.FindAsync(id);

                if (existingLicensePool is null)
                {
                    return null;
                }

                // Get the authentication state
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                // Update properties
                existingLicensePool.ModId = userId;
                existingLicensePool.ModTs = DateTime.Now;

                existingLicensePool.Status = licensePool.Status;
                existingLicensePool.Name = licensePool.Name;
                existingLicensePool.Product = licensePool.Product;
                existingLicensePool.AccountingType = licensePool.AccountingType;
                existingLicensePool.PurchasedLicenses = licensePool.PurchasedLicenses;
                existingLicensePool.PurchasedArchivedLicenses = licensePool.PurchasedArchivedLicenses;

                if (licensePool.UsedLicenses != null)
                {
                    existingLicensePool.UsedLicenses = licensePool.UsedLicenses;
                }
                if (licensePool.UsedArchivedLicenses != null)
                {
                    existingLicensePool.UsedArchivedLicenses = licensePool.UsedArchivedLicenses;
                }

                existingLicensePool.BeginTs = licensePool.BeginTs;
                existingLicensePool.EndTs = licensePool.EndTs;
                existingLicensePool.Notes = licensePool.Notes;

                _dbContext.LicensePools.Update(existingLicensePool);
                await _dbContext.SaveChangesAsync();
                return existingLicensePool;
            }
        }

        public async Task<List<LicensePoolListDisplayDto>> GetLicensePoolDisplayList(PageQuery pageQuery, long? organizationId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            string sqlQuery = @"SELECT lp.id, lp.[status], lp.name, lp.product, lp.purchased_licenses AS PurchasedLicenses,
                                lp.purchased_archived_licenses AS PurchasedArchivedLicenses, lp.used_licenses AS UsedLicenses,
                                lp.used_archived_licenses AS UsedArchivedLicenses, lp.begin_ts AS BeginTs, lp.end_ts AS EndTs,
                                lp.notes
                            FROM cmn_license_pools AS lp
                            WHERE lp.organization_id = {0}
                                AND
                                (
                                    lp.name LIKE {1}
                                    OR lp.[status] LIKE {1}
                                    OR lp.product LIKE {1}
                                    OR lp.notes LIKE {1}
                                )
                            ORDER BY id OFFSET {2} ROWS FETCH NEXT {3} ROWS ONLY";

            string searchText = "%" + pageQuery.QueryText + "%";
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<LicensePoolListDisplayDto> resultList = await _dbContext.LicensePoolListDisplayDtos
                .FromSqlRaw(sqlQuery, organizationId, searchText, pageOffset, pageSize)
                .Select(o => new LicensePoolListDisplayDto
                {
                    Id = o.Id,
                    Status = o.Status,
                    Name = o.Name,
                    Product = o.Product,
                    PurchasedLicenses = o.PurchasedLicenses,
                    PurchasedArchivedLicenses = o.PurchasedArchivedLicenses,
                    UsedLicenses = o.UsedLicenses,
                    UsedArchivedLicenses = o.UsedArchivedLicenses,
                    BeginTs = o.BeginTs,
                    EndTs = o.EndTs,
                    Notes = o.Notes
                })
                .ToListAsync();

                return resultList;
            }
        }

        public async Task<int> GetLicensePoolCount(long? organizationId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            string sqlQuery = "SELECT COUNT(id) AS Value FROM cmn_license_pools WHERE organization_id = {0} ";
            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();
                return count;
            }
        }

        public async Task<LicensePool> AddLicensePoolAsync(LicensePool licensePool)
        {
            if (licensePool is null)
            {
                throw new ArgumentNullException(nameof(licensePool));
            }

            // Get the authentication state
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            licensePool.ModId = userId;
            licensePool.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.LicensePools.AddAsync(licensePool);
                await _dbContext.SaveChangesAsync();
            }

            return licensePool;
        }

        public async Task<List<LicensePool>> GetLicensePoolList(PageQuery pageQuery, long? organizationId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            string sqlQuery = @"SELECT lp.id, lp.organization_id, lp.mod_id, lp.mod_ts, lp.[status], lp.name, lp.product,
                                lp.accounting_type, lp.purchased_licenses, lp.purchased_archived_licenses,
                                lp.used_licenses, lp.used_archived_licenses, lp.begin_ts, lp.end_ts, lp.notes
                            FROM cmn_license_pools AS lp
                            WHERE lp.organization_id = {0}
                                AND lp.begin_ts < GETDATE()
                                AND lp.end_ts > GETDATE()
                                AND lp.status = 'Active'
                            ORDER BY id OFFSET {1} ROWS FETCH NEXT {2} ROWS ONLY";

            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<LicensePool> resultList = await _dbContext.LicensePools
                .FromSqlRaw(sqlQuery, organizationId, pageOffset, pageSize)
                .ToListAsync();

                return resultList;
            }
        }

        public async Task<int> CountActiveLicense(long? organizationId, string? product)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            string sqlQuery = @"SELECT COUNT(lp.id) AS Value
                                FROM cmn_license_pools AS lp 
                                WHERE organization_id = {0} 
	                                AND lp.begin_ts < GETDATE() 
	                                AND lp.end_ts > GETDATE() 
	                                AND lp.status = 'Active' 
	                                AND lp.product = {1} ";
            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, product);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();
                return count;
            }
        }

        public async Task<LicensePool?> GetLicensePoolForIncrementation(long? organizationId, string? product)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (product is null)
            {
                throw new ArgumentNullException(nameof(product));
            }

            string sqlQuery = @"SELECT TOP 1 id, organization_id, mod_id, mod_ts, status, name, product, accounting_type, purchased_licenses, purchased_archived_licenses,
	                                used_licenses, used_archived_licenses, begin_ts, end_ts, notes 
                                FROM cmn_license_pools
                                WHERE organization_id = {0}
	                                AND product = {1}
	                                AND begin_ts < GETDATE()
	                                AND end_ts > GETDATE()
	                                AND used_licenses < purchased_licenses
                                    AND status = 'Active'
                                ORDER BY end_ts";

            LicensePool? licensePool = null;
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                licensePool = await _dbContext.LicensePools
                    .FromSqlRaw(sqlQuery, organizationId, product)
                    .FirstOrDefaultAsync();
            }

            return licensePool;
        }

        public async Task<bool> IncrementUsedLicensePool(long? licensePoolId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                LicensePool? existingLicensePool = await _dbContext.LicensePools.FindAsync(licensePoolId);

                if (existingLicensePool is null)
                {
                    return false;
                }

                // Get the authentication state
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                // Update properties
                existingLicensePool.ModId = userId;
                existingLicensePool.ModTs = DateTime.Now;

                if (existingLicensePool.UsedLicenses == null)
                {
                    existingLicensePool.UsedLicenses = 0;
                }

                existingLicensePool.UsedLicenses++;

                _dbContext.LicensePools.Update(existingLicensePool);
                await _dbContext.SaveChangesAsync();

                return true;
            }
        }

        public async Task<bool> IncrementUsedLicensePoolWithContext(long? licensePoolId, ApplicationDbContext dbContext)
        {
            if (licensePoolId == null)
            {
                return false;
            }

            LicensePool? existingLicensePool = await dbContext.LicensePools.FindAsync(licensePoolId);

            if (existingLicensePool == null)
            {
                return false;
            }

            // Get the authentication state
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

            // Update properties
            existingLicensePool.ModId = userId;
            existingLicensePool.ModTs = DateTime.Now;

            if (existingLicensePool.UsedLicenses == null)
            {
                existingLicensePool.UsedLicenses = 0;
            }

            existingLicensePool.UsedLicenses++;

            dbContext.LicensePools.Update(existingLicensePool);
            await dbContext.SaveChangesAsync();

            return true;
        }
    }
}
