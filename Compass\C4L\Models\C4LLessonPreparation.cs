﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.C4L.Models
{
    [Table("c4l_lesson_preparations_lookup")]
    public class C4LLessonPreparation
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("lesson_id")]
        public int LessonId { get; set; }

        [Column("language")]
        public string Language { get; set; } = "English";

        [Column("unit")]
        public int Unit { get; set; }

        [Column("week")]
        public int Week { get; set; }

        [Column("day")]
        public int Day { get; set; }

        [Column("lesson_type_sequence")]
        public int LessonTypeSequence { get; set; }

        [Column("title_sequence")]
        public int TitleSequence { get; set; }

        [Column("preparation_tasks")]
        public string PreparationTasks { get; set; }
    }
}
