﻿@using Compass.C4L.Interfaces.Services
@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject IStudentGroupService StudentGroupService
@inject IC4LClassroomService C4LClassroomService
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject UserAccessor UserAccessor
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService


<EditForm Model="Input" FormName="formStudentGroup" OnValidSubmit="SubmitAsync" class="c4l-form student-group-edit-form">
    <h3 class="c4l-form-heading">@(studentGroupId != 0 ? @Localizer["lbl_Edit"] : @Localizer["lbl_Add"]) @studentGroupName</h3>

    <DataAnnotationsValidator></DataAnnotationsValidator>
    <ValidationSummary></ValidationSummary>

    <div>
        <label class="col-form-label" for="studentgroup-name">
            @Localizer["lbl_Name"]
            <InputText @bind-Value="Input.StudentGroupName"
            class="form-control mt-2"
            id="studentgroup-name"
            aria-required="true"
            />
        </label>
        <ValidationMessage For="() => Input.StudentGroupName" class="mt-2 text-danger" />
    </div>

    <div>
        <label class="col-form-label" for="studentgroup-group-type">
            @studentGroupTypeLabel
            <InputSelect @bind-Value="Input.GroupType"
            id="studentgroup-group-type"
            >
                <option disabled value="" selected>-- Choose One --</option>
                <option value="Standard">@Localizer["classroom_standard"]</option>
                <option value="Specialist">@Localizer["classroom_specialist"]</option>
            </InputSelect>
        </label>
    </div>

    @if (hasC4LAccess)
    {
        <div class="mb-3">
            <label class="form-label form-checkbox-label d-flex" for="entity1-checkbox">
                <InputCheckbox @bind-Value="Input.IsC4LClassroom"
                    class="darker-border-checkbox form-check-input"
                    id="entity1-checkbox"
                    disabled="@isC4LOptionLocked" />
                Is C4L Classroom
            </label>
        </div>

        @if (Input.IsC4LClassroom)
        {
            <FieldComponent Label="Start Date">
                <Control>
                    <InputDate @bind-Value="Input.StartDate" class="form-control" />
                </Control>
            </FieldComponent>
        }
    }

    <div class="form-submit-buttons-wrapper">
        <button class="c4l-button c4l-secondary-button c4l-form-button" type="submit">@Localizer["btn_save"]</button>

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success" role="alert">
                @successMessage
            </div>
        }
    </div>
</EditForm>
