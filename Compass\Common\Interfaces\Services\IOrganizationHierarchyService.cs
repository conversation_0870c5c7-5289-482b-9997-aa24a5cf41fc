﻿using Compass.Common.DTOs.OrganizationHierarchies;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Services
{
    public interface IOrganizationHierarchyService
    {
        public Task<ValidateRemovedEntitiesDto> ValidateRemovedEntities(OrganizationHierarchy organizationHierarchy);
        public Task<SaveHierarchyDto?> SaveHierarchy(OrganizationHierarchy organizationHierarchy);
    }
}
