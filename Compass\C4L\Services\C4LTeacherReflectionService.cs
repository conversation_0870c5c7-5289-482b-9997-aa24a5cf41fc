using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Serilog;

namespace Compass.C4L.Services
{
    public class C4LTeacherReflectionService : IC4LTeacherReflectionService
    {
        private readonly IC4LTeacherReflectionRepository _repository;

        public C4LTeacherReflectionService(IC4LTeacherReflectionRepository repository)
        {
            _repository = repository;
        }

        public async Task<C4LTeacherReflection?> GetByIdAsync(long id)
        {
            try
            {
                return await _repository.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in service retrieving teacher reflection with ID {Id}", id);
                throw;
            }
        }

        public async Task<C4LTeacherReflection> CreateAsync(C4LTeacherReflection teacherReflection)
        {
            try
            {
                return await _repository.CreateAsync(teacherReflection);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in service creating teacher reflection for classroom {ClassroomId} and lesson {LessonId}", 
                    teacherReflection.ClassroomId, teacherReflection.LessonId);
                throw;
            }
        }

        public async Task<C4LTeacherReflection?> UpdateAsync(C4LTeacherReflection teacherReflection)
        {
            try
            {
                return await _repository.UpdateAsync(teacherReflection);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in service updating teacher reflection with ID {Id}", teacherReflection.Id);
                throw;
            }
        }

        public async Task<List<C4LTeacherReflection>> GetByClassroomIdAndLessonIdAsync(long classroomId, int lessonId)
        {
            try
            {
                return await _repository.GetByClassroomIdAndLessonIdAsync(classroomId, lessonId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in service retrieving teacher reflections for classroom {ClassroomId} and lesson {LessonId}", 
                    classroomId, lessonId);
                throw;
            }
        }
    }
}
