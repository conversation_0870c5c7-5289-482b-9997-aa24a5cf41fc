﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.C4L.Models
{
    [Table("c4l_classrooms")]
    public class C4LClassroom
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("mod_id")]
        public string ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Column("student_group_id")]
        public long? StudentGroupId { get; set; }

        [Column("start_date")]
        public DateTime StartDate { get; set; }

        [Column("school_year_id")]
        public long SchoolYearId { get; set; }
    }
}
