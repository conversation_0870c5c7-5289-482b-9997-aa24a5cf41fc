﻿using Compass.Common.Data;
using Compass.Common.DTOs.Entity3;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity3
{
    public partial class Entity3List : IDisposable
    {
        private List<Entity3ListDisplayDto> entity3Results = new();
        private string entity3Hierarchy = string.Empty;
        private string entity2Hierarchy = string.Empty;
        private string entity1Hierarchy = string.Empty;

        private int maxPages;
        private int currentPage;

        private long? currentOrganizationId;
        private long? currentEntity1Id;
        private long? currentEntity2Id;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        private bool noSearchResults = false;
        private bool isLoading = true;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
                if (commonSessionData != null)
                {
                    currentOrganizationId = commonSessionData.CurrentOrganizationId;
                }
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            searchText = string.Empty;
            currentSearchText = string.Empty;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                entity3Hierarchy = commonSessionData.Entity3Hierarchy;
                entity2Hierarchy = commonSessionData.Entity2Hierarchy;
                entity1Hierarchy = commonSessionData.Entity1Hierarchy;

                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                currentEntity1Id = commonSessionData.CurrentEntity1Id;
                currentEntity2Id = commonSessionData.CurrentEntity2Id;

                currentPage = 1;
                maxPages = 0;
                await GetEntity3Page();
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task GetEntity3Page()
        {
            isLoading = true;
            Entity3ListAction action = new();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            if (currentOrganizationId is not null)
            {
                action.pageQuery = pageQuery;
                action.organizationId = currentOrganizationId;
                action.entity1Id = currentEntity1Id;
                action.entity2Id = currentEntity2Id;

                action.organizationId = currentOrganizationId;
                action.userId = _currentUserId;

                KaplanPageable<Entity3ListDisplayDto> currentPage = await Entity3Service.GetEntity3Page(action);

                entity3Results = currentPage.PageContent;
                maxPages = currentPage.MaxPages;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && entity3Results.Count == 0;
                isLoading = false;

                StateHasChanged();
            }
        }

        protected async Task OnEntity3Selected(Entity3ListDisplayDto entity)
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                commonSessionData.CurrentEntity3Id = entity.Id;
                commonSessionData.CurrentEntity2Id = entity.Entity2Id;
                commonSessionData.CurrentEntity1Id = entity.Entity1Id;

                if (entity.Name != null)
                {
                    commonSessionData.SelectedEntityName = entity.Name;
                }
                else
                {
                    commonSessionData.SelectedEntityName = string.Empty;
                }

                if (_currentUser != null)
                {
                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();

                    NavigationManager.NavigateTo($"/entity3");
                }
            }
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetEntity3Page();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetEntity3Page();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetEntity3Page();
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
