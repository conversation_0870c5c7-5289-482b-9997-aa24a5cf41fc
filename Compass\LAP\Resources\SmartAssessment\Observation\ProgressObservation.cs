namespace Compass.LAP.Resources.SmartAssessment.Observation
{
    public class ProgressObservation
    {
        public long? CustomerInstID { get; set; }
        public long? ObserverInstID { get; set; }
        public DateTime? DateOfObservation { get; set; }
        public long? ChildInstID { get; set; }
        public string? Note { get; set; }
        public long? UploadedFileInstID { get; set; }
        public long? CrossDomainScoreStaticId { get; set; }

        // TODO: Replace with actual UploadedFile type when available
        //public object? UploadedFile { get; set; }

        public List<ProgressObservationItem>? Items { get; set; }

        public int? SchoolYear { get; set; }

        public ProgressObservationItem? GetItem(long? itemStaticID)
        {
            ProgressObservationItem? ret = null;

            if (Items != null)
            {
                foreach (ProgressObservationItem item in Items)
                {
                    if (item.ItemStaticID != null)
                    {
                        if (item.ItemStaticID.Equals(itemStaticID))
                        {
                            ret = item;
                            break;
                        }
                    }
                }
            }

            return ret;
        }
    }
}
