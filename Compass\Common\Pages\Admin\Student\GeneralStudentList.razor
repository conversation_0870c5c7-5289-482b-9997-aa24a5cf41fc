﻿@page "/studentlist"
@using Compass.Common.Data
@using Compass.Common.DTOs.Student
@using Compass.Common.Interfaces.Services
@using Compass.Common.Services
@using Compass.Common.Resources
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Compass.Common.Controls.Generic
@using Microsoft.Extensions.Localization
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserSessionService UserSessionService
@inject NavigationManager NavigationManager
@inject UserAccessor UserAccessor
@inject IStudentService StudentService;
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<PageTitle>Student List | C4L</PageTitle>

<h1 class="page-title horizontal-line">General Student List</h1>

<div class="c4l-search-table-wrapper">
    <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

    <LoaderComponent IsLoading="isLoading">
        @if(studentResults is not null && studentResults.Count > 0)
        {
            <div class="c4l-table-scroll-wrapper">
                <div class="c4l-table-wrapper has-links users-wrapper">
                    <div class="c4l-table-headings-wrapper users-heading-wrapper">
                        <h6 class="c4l-table-heading">First Name</h6>
                        <h6 class="c4l-table-heading">Last Name</h6>
                        <h6 class="c4l-table-heading">Birthdate</h6>
                        <h6 class="c4l-table-heading">School ID</h6>
                    </div>

                    @foreach (StudentDisplayDto student in studentResults)
                    {
                        <button type="button" @onclick="() => OnStudentClick(student)">
                            <div class="c4l-table-result-wrapper user-result-username">
                                <p class="c4l-table-result-item">@student.FirstName</p>
                                <p class="c4l-table-result-item">@student.LastName</p>
                                <p class="c4l-table-result-item">@(student.BirthDate?.ToString("d"))</p>
                                <p class="c4l-table-result-item">@student.SchoolId</p>
                            </div>
                        </button>
                    }
                </div>
            </div>
        }

        @if(!isLoading && studentResults.Count == 0)
        {
            <NoTableDataMessage MessageText="@($"There are no students to show for {organizationName}")" />
        }
    </LoaderComponent>
</div>

<div class="c4l-pagination-wrapper">
    <div class="c4l-pagination-buttons-wrapper">
        <div class="buttons-wrapper">
            <button
                class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                @onclick="() => OnPreviousClicked()" 
                disabled="@(currentPage <= 1)"
                type="button"
            >
                @Localizer["lbl_Previous"]
            </button>

            <button 
                class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                @onclick="() => OnNextClicked()"
                disabled="@(currentPage >= maxPages)"
                type="button"
            >
                @Localizer["lbl_Next"]
            </button>
        </div>
    </div>

    <div class="page-count-wrapper font-weight-500">
        <span class="current-page-number">@currentPage</span> @Localizer["lbl_of"] @maxPages
    </div>
</div>
