﻿using Compass.Common.Data;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Routing;

namespace Compass.Components.Account.Pages
{
    public partial class MfaEnforcementWorkflow : ComponentBase, IDisposable
    {
        [Parameter]
        public RenderFragment ChildContent { get; set; }

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private bool isMfaRequirementsValid;
        private readonly string[] allowedPaths = new[]
        {
            "/Account/Manage/EnableAuthenticator",
            "/Account/Logout",
            "/login"
        };

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        private async Task<bool> CheckMfaRequirements()
        {
            if (_currentUser != null)
            {
                // I tried optimizing by saying if the user already has mfa then dont query however this does not work if the user disables their mfa
                // There are a lot of ompimizations that can be done here but must be put off in order to hit milestone
                long? organizationId = _currentUser.OrganizationId;

                // Checking to make sure user does not have a null organization
                if (organizationId != null)
                {
                    bool mfaRequired = await OrganizationService.CheckOrganizationMfaRequirements(organizationId);

                    _currentUser = await UserAccessor.GetUserAsync(_currentUserId);
                    if (_currentUser != null)
                    {
                        bool mfaEnabled = _currentUser.TwoFactorEnabled;

                        if (mfaRequired && !mfaEnabled)
                        {
                            return false;
                        }
                        else
                        {
                            return true;
                        }
                    }
                }
            }

            return true;
        }

        protected override async Task OnInitializedAsync()
        {
            await base.OnInitializedAsync();
            await GetCommonSessionData();
            isMfaRequirementsValid = await CheckMfaRequirements();

            NavigationManager.LocationChanged += HandleLocationChanged;
        }

        private async void HandleLocationChanged(object? sender, LocationChangedEventArgs e)
        {
            if (_currentUser != null)
            {
                isMfaRequirementsValid = await CheckMfaRequirements();

                if (!isMfaRequirementsValid && !allowedPaths.Any(p =>
                    e.Location.EndsWith(p, StringComparison.OrdinalIgnoreCase)))
                {
                    NavigationManager.NavigateTo("/Account/Manage/EnableAuthenticator");
                }
            }
        }

        public void Dispose()
        {
            NavigationManager.LocationChanged -= HandleLocationChanged;
        }
    }
}
