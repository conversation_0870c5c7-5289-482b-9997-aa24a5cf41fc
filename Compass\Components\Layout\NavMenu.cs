﻿using Compass.Common.Data;
using Compass.Common.Models;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components.Routing;

namespace Compass.Components.Layout
{
    public partial class NavMenu
    {
        private string? currentUrl;
        private string? organizationName;
        private string? entity1EntityHierarchy;
        private string? entity2EntityHierarchy;
        private string? entity3EntityHierarchy;
        private string? siteEntityHierarchy;
        private string? studentGroupEntityHierarchy;
        private string? studentEntityHierarchy;

        private long? currentOrganizationId;
        private bool isSuperAdmin;
        private bool isUserLoggedIn = false;

        private string? _currentUserId;

        private bool isAppDropdownVisible = false;

        private string selectedApp = "Connect4Learning";
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
                if (commonSessionData != null)
                {
                    currentOrganizationId = commonSessionData.CurrentOrganizationId;
                }
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            isSuperAdmin = false;
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            isUserLoggedIn = authState.User.Identity?.IsAuthenticated ?? false;

            currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
            NavigationManager.LocationChanged += OnLocationChanged;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (_currentUser != null)
            {
                bool isCurrentUserSuperAdmin = await UserAccessor.IsUserInRoleAsync(_currentUserId, UserAccessor.USER_ROLE_SUPER_ADMIN);

                if (isCurrentUserSuperAdmin)
                {
                    isSuperAdmin = true;
                }
            }

            CommonSessionDataObserver.AddStateChangeAsyncListeners(EntityNamesAsync);

            bool hasChanged = await UpdateNavButtonsAsync(commonSessionData);
            if (hasChanged)
            {
                StateHasChanged();
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task EntityNamesAsync()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                if (currentOrganizationId != null)
                {
                    currentOrganizationId = commonSessionData.CurrentOrganizationId;

                    bool hasChanged = await UpdateNavButtonsAsync(commonSessionData);
                    if (hasChanged)
                    {
                        await InvokeAsync(StateHasChanged);
                    }
                }
            }
        }

        private async Task<bool> UpdateNavButtonsAsync(CommonSessionData? commonSessionData)
        {
            bool hasChanged = false;
            if (_currentUser != null)
            {
                bool isCurrentUserSuperAdmin = await UserAccessor.IsUserInRoleAsync(_currentUserId, UserAccessor.USER_ROLE_SUPER_ADMIN);
                List<VisibleEntity> visibleEntities = await OrganizationHierarchyRepository.GetVisibleEntities(_currentUserId, currentOrganizationId);
                int highestLevel = 99;
                foreach (VisibleEntity ve in visibleEntities)
                {
                    if (ve.EntityLevel == 0 && (ve.AccessCount > 0 || isCurrentUserSuperAdmin))
                    {
                        if (this.organizationName != ve.EntityHierarchy)
                        {
                            this.organizationName = ve.EntityHierarchy ?? "";
                            hasChanged = true;
                        }
                        highestLevel = 0;
                    }
                    else if (ve.EntityLevel <= 1 && (ve.AccessCount > 0 || highestLevel <= 1))
                    {
                        if (this.entity1EntityHierarchy != ve.EntityHierarchy)
                        {
                            this.entity1EntityHierarchy = ve.EntityHierarchy;
                            hasChanged = true;
                        }

                        if (highestLevel > 1)
                        {
                            highestLevel = 1;
                        }
                    }
                    else if (ve.EntityLevel <= 2 && (ve.AccessCount > 0 || highestLevel <= 2))
                    {
                        if (this.entity2EntityHierarchy != ve.EntityHierarchy)
                        {
                            this.entity2EntityHierarchy = ve.EntityHierarchy;
                            hasChanged = true;
                        }

                        if (highestLevel > 2)
                        {
                            highestLevel = 2;
                        }
                    }
                    else if (ve.EntityLevel <= 3 && (ve.AccessCount > 0 || highestLevel <= 3))
                    {
                        if (this.entity3EntityHierarchy != ve.EntityHierarchy)
                        {
                            this.entity3EntityHierarchy = ve.EntityHierarchy;
                            hasChanged = true;
                        }

                        if (highestLevel > 3)
                        {
                            highestLevel = 3;
                        }
                    }
                    else if (ve.EntityLevel <= 4 && (ve.AccessCount > 0 || highestLevel <= 4))
                    {
                        if (this.siteEntityHierarchy != ve.EntityHierarchy)
                        {
                            this.siteEntityHierarchy = ve.EntityHierarchy;
                            hasChanged = true;
                        }

                        if (highestLevel > 4)
                        {
                            highestLevel = 4;
                        }
                    }
                    else if (ve.EntityLevel <= 5 && (ve.AccessCount > 0 || highestLevel <= 5))
                    {
                        if (this.studentGroupEntityHierarchy != ve.EntityHierarchy)
                        {
                            this.studentGroupEntityHierarchy = ve.EntityHierarchy;
                            hasChanged = true;
                        }

                        if (highestLevel > 5)
                        {
                            highestLevel = 5;
                        }
                    }
                    else if (ve.EntityLevel <= 6 && (ve.AccessCount > 0 || highestLevel <= 6))
                    {
                        if (this.studentEntityHierarchy != ve.EntityHierarchy)
                        {
                            this.studentEntityHierarchy = ve.EntityHierarchy;
                            hasChanged = true;
                        }

                        if (highestLevel > 6)
                        {
                            highestLevel = 6;
                        }
                    }
                }
            }

            return hasChanged;
        }

        private async Task OnNavBtnClick()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                commonSessionData.ResetCurrentIdValues();
                if (_currentUser != null)
                {
                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                }
            }
        }

        private void ToggleAppDropdown()
        {
            isAppDropdownVisible = !isAppDropdownVisible;
        }

        private string DropdownStyle => isAppDropdownVisible ? "display: block;" : "display: none;";

        private void UpdateSelectedPage(string page)
        {
            selectedApp = page;
            isAppDropdownVisible = false;
            StateHasChanged();
        }

        private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
        {
            currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
            StateHasChanged();
        }

        public void Dispose()
        {
            NavigationManager.LocationChanged -= OnLocationChanged;

            CommonSessionDataObserver.RemoveStateChangeAsyncListeners(EntityNamesAsync);
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
