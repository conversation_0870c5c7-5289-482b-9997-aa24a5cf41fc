﻿@using Compass.Common.Data
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Interfaces.Services
@using Compass.Common.Models
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject IEntity1Repository Entity1Repository
@inject ILicensePoolService LicensePoolService;
@inject UserAccessor UserAccessor
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<div class="container mt-4">
    <LoaderComponent IsLoading="isLoading">
        <ContactSummary ContactName="@contactName" Email="@email" Phone="@phone" />

        <div class="c4l-table-scroll-wrapper">
            <div class="c4l-table-wrapper has-license-info org-summary-table-wrapper">
                <div class="c4l-table-title-wrapper">
                    <h3 class="c4l-table-title">@($"License Summary For {entityName}")</h3>
                </div>

                <div class="c4l-table-headings-wrapper">
                    <h6 class="c4l-table-heading">@Localizer["lbl_ProductName"]</h6>
                    <h6 class="c4l-table-heading">Status</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_AccountType"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_Begin"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_End"]</h6>
                    <h6 class="c4l-table-heading">Purchased Licenses</h6>
                    <TooltipText Text="Purchased Archive Licenses" Placement="TooltipPlacement.Bottom">
                        <h6 class="c4l-table-heading">Purchased Archived Licenses</h6>
                    </TooltipText>
                    <h6 class="c4l-table-heading">Used Licenses</h6>
                    <TooltipText Text="Used Archive Licenses" Placement="TooltipPlacement.Bottom">
                        <h6 class="c4l-table-heading">Used Archived Licenses</h6>
                    </TooltipText>
                </div>

                @foreach (LicensePool licensePool in licensePoolPage)
                {
                    <div class="c4l-table-result-wrapper">
                        <p class="c4l-table-result-item">@licensePool.Product</p>
                        <p class="c4l-table-result-item">@licensePool.Status</p>
                        <p class="c4l-table-result-item">@licensePool.AccountingType</p>
                        <p class="c4l-table-result-item">@(licensePool.BeginTs?.ToString("d"))</p>
                        <p class="c4l-table-result-item">@(licensePool.EndTs?.ToString("d"))</p>
                        <p class="c4l-table-result-item">@licensePool.PurchasedLicenses</p>
                        <p class="c4l-table-result-item">@licensePool.PurchasedArchivedLicenses</p>
                        <p class="c4l-table-result-item">@licensePool.UsedLicenses</p>
                        <p class="c4l-table-result-item">@licensePool.UsedArchivedLicenses</p>
                    </div>
                }
            </div>
        </div>

        <div class="c4l-pagination-wrapper">
            <div class="c4l-pagination-buttons-wrapper">
                <div class="buttons-wrapper">
                    <button class="c4l-button c4l-ghost-primary c4l-pagination-button"
                            @onclick="() => OnPreviousClicked()"
                            disabled="@(currentPage <= 1)">
                        @Localizer["lbl_Previous"]
                    </button>

                    <button class="c4l-button c4l-ghost-primary c4l-pagination-button"
                            @onclick="() => OnNextClicked()"
                            disabled="@(currentPage >= maxPages)">
                        @Localizer["lbl_Next"]
                    </button>
                </div>
            </div>

            <div class="page-count-wrapper font-weight-500">
                <span class="current-page-number">@currentPage</span> @Localizer["lbl_of"] @maxPages
            </div>
        </div>
    </LoaderComponent>
</div>
