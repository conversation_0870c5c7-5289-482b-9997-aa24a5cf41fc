﻿@page "/deca-student-group-rating-summary"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Compass.DECA.DTOs

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<h1 class="deca-rating-summary-title">DECA Rating Summary</h1>

@if (isLoading)
{
    <div class="loading-wrapper">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
}
else if (studentRatings != null && studentRatings.Any())
{
    <section class="ratings-container">
        <header class="ratings-header">
            <div class="header-item">Student Name</div>
            <div class="header-item">Date of Birth</div>
            <div class="header-item">Rating Date</div>
            <div class="header-item">Rating Type</div>
            <div class="header-item">Checkpoint</div>
            <div class="header-item">Rater Type</div>
            <div class="header-item">Description</div>
            <div class="header-item">IN</div>
            <div class="header-item">SC</div>
            <div class="header-item">AT</div>
            <div class="header-item">TPF</div>
            <div class="header-item">BC</div>
        </header>

        <div class="ratings-content">
            @foreach (var rating in studentRatings)
            {
                <div class="rating-row">
                    <div class="rating-item">@(GetStudentName(rating.FirstName, rating.LastName))</div>
                    <div class="rating-item">@(GetFormattedDate(rating.BirthDate))</div>
                    <div class="rating-item">@(GetFormattedDate(rating.RatingDate))</div>
                    <div class="rating-item">@(GetSafeString(rating.RecordForm))</div>
                    <div class="rating-item">@(GetSafeString(rating.RatingPeriod))</div>
                    <div class="rating-item">@(GetSafeString(rating.RaterType))</div>
                    <div class="rating-item">Raw Score</div>
                    <div class="rating-item">@(GetSafeNumber(rating.InRaw))</div>
                    <div class="rating-item">@(GetSafeNumber(rating.ScRaw))</div>
                    <div class="rating-item">@(GetSafeNumber(rating.ArRaw))</div>
                    <div class="rating-item">@(GetSafeNumber(rating.TpfRaw))</div>
                    <div class="rating-item">@(GetSafeNumber(rating.BcRaw))</div>
                </div>
            }
        </div>
    </section>
}
else
{
    <div class="alert alert-info no-data-message">
        <i class="bi bi-info-circle" aria-hidden="true"></i>
        <p>No student ratings found for this student group.</p>
    </div>
}

@code {
    private string GetStudentName(string? firstName, string? lastName)
    {
        var first = GetSafeString(firstName);
        var last = GetSafeString(lastName);

        if (string.IsNullOrWhiteSpace(first) && string.IsNullOrWhiteSpace(last))
        {
            return "Unknown Student";
        }

        return $"{first} {last}".Trim();
    }

    private string GetFormattedDate(DateTime? date)
    {
        return date?.ToString("MM/dd/yyyy") ?? "";
    }

    private string GetSafeString(string? value)
    {
        return value ?? "";
    }

    private string GetSafeNumber(int? value)
    {
        return value?.ToString() ?? "";
    }
}
