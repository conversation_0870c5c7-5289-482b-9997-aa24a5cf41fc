﻿using Compass.Common.Data;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Admin.Organization
{
    public partial class OrganizationAddEdit : IDisposable
    {
        [Parameter]
        public long organizationId { get; set; }

        [SupplyParameterFromForm]
        private Compass.Common.Models.Organization? organization { get; set; }

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override void OnInitialized()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        protected override async Task OnParametersSetAsync()
        {
            if (this.organizationId > 0)
            {
                organization ??= await OrganizationService.GetOrganizationAsync(this.organizationId);
            }
            else
            {
                organization ??= new Compass.Common.Models.Organization();
            }
        }

        protected async Task OnValidateSubmit()
        {
            if (organization.Id > 0)
            {
                await OrganizationService.UpdateOrganizationAsync(this.organizationId, organization);
            }
            else
            {
                await OrganizationService.AddOrganizationAsync(organization);
            }

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                commonSessionData.ResetCurrentIdValues();
                commonSessionData.UnSelectOrganization();
                if (_currentUser != null)
                {
                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                }
            }

            NavigationManager.NavigateTo("/organization-list");
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
