﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.User;
using Compass.Common.Helpers;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Prompts.Generic
{
    public partial class AssignBox
    {
        [Parameter]
        public EventCallback<string> AssignResult { get; set; }

        [Parameter]
        public bool IsVisible { get; set; }

        [Parameter]
        public long? OrganizationId { get; set; }

        [Parameter]
        public long? EntityId { get; set; }

        [Parameter]
        public int AssignLevel { get; set; }

        [Parameter]
        public string CustomModalClass { get; set; } = string.Empty;

        private List<UserListDisplayDto> userResults = new();

        private int maxPages;
        private int currentPage;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        protected override async Task OnInitializedAsync()
        {
            searchText = string.Empty;
            currentSearchText = string.Empty;

            currentPage = 1;
            maxPages = 0;
            await GetUserPage();
        }

        protected override async Task OnParametersSetAsync()
        {
            if (IsVisible)
            {
                await GetUserPage();
            }
        }

        private async Task GetUserPage()
        {
            UserAssignListAction action = new UserAssignListAction();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            action.PageQuery = pageQuery;
            if (OrganizationId != null)
            {
                action.OrganizationId = OrganizationId;
                action.EntityId = EntityId;
                action.AssignLevel = AssignLevel;

                KaplanPageable<UserListDisplayDto> currentPage = await UserService.GetUserAssignList(action);

                userResults = currentPage.PageContent;
                maxPages = currentPage.MaxPages;

                StateHasChanged();
            }
        }

        protected async Task OnSearch()
        {
            this.currentPage = 1;
            currentSearchText = searchText;
            await GetUserPage();
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetUserPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetUserPage();
            }
        }

        protected async Task OnUserAssignClick(string userId)
        {
            await AssignResult.InvokeAsync(userId);
        }

        protected async Task OnUserCancelClick()
        {
            await AssignResult.InvokeAsync(string.Empty);
        }
    }
}
