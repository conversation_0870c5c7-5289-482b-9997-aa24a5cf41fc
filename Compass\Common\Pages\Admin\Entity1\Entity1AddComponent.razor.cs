﻿using Compass.Common.Data;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity1
{
    public partial class Entity1AddComponent : IDisposable
    {
        private Compass.Common.Models.Entity1? entity1 { get; set; }
        private string? entity1Name { get; set; }

        private string? successMessage = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                entity1Name = commonSessionData.Entity1Hierarchy;
            }

            this.entity1 = new Compass.Common.Models.Entity1();
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        protected async Task SubmitAsync()
        {
            if (this.entity1 is not null)
            {
                this.successMessage = string.Empty;

                CommonSessionData? commonSessionData = await GetCommonSessionData();
                if (commonSessionData is not null)
                {
                    this.entity1.OrganizationId = commonSessionData.CurrentOrganizationId;

                    this.entity1 = await Entity1Service.CreateEntity1Async(this.entity1);

                    successMessage = "Information saved successfully!";
                }

                this.entity1 = new Compass.Common.Models.Entity1();
            }
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
