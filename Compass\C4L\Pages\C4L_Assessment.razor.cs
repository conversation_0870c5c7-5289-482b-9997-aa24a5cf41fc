using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Microsoft.AspNetCore.Components;

namespace Compass.C4L.Pages
{
    public partial class C4L_Assessment
    {
        [Inject]
        public required IC4LLearningObjectiveService LearningObjectiveService { get; set; }

        private IEnumerable<C4LLearningObjective> LearningObjectives { get; set; } = Enumerable.Empty<C4LLearningObjective>();
        private string SelectedLanguage { get; set; } = "English";

        protected override async Task OnInitializedAsync()
        {
            await LoadLearningObjectivesAsync();
        }

        private async Task LoadLearningObjectivesAsync()
        {
            try
            {
                LearningObjectives = await LearningObjectiveService.GetLearningObjectivesAsync(SelectedLanguage);
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading learning objectives: {ex.Message}");
            }
        }

        private async Task OnLanguageChanged(string language)
        {
            SelectedLanguage = language;
            await LoadLearningObjectivesAsync();
        }
    }
}