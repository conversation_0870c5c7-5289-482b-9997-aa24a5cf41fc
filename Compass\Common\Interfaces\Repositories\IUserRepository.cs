﻿using Compass.Common.DTOs.User;
using Compass.Common.Helpers;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IUserRepository
    {
        Task<List<UserListDisplayDto>> GetUserList(UserListAction action);
        Task<List<UserListDisplayDto>> GetOrganizationUserList(UserListAction action);
        Task<List<UserListDisplayDto>> GetEntity1UserList(UserListAction action);
        Task<List<UserListDisplayDto>> GetEntity2UserList(UserListAction action);
        Task<List<UserListDisplayDto>> GetEntity3UserList(UserListAction action);
        Task<List<UserListDisplayDto>> GetSiteUserList(UserListAction action);
        Task<List<UserListDisplayDto>> GetStudentGroupUserList(UserListAction action);
        Task<List<UserListDisplayDto>> GetUserInviteList(UserAssignListAction action);
        Task<int> GetUserCount(long? organizationId, string queryText);
        Task<int> GetOrganizationUserCount(long? organizationId, string queryText);
        Task<int> GetEntity1UserCount(long? organizationId, long? entity1Id, string queryText);
        Task<int> GetEntity2UserCount(long? organizationId, long? entity2Id, string queryText);
        Task<int> GetEntity3UserCount(long? organizationId, long? entity3Id, string queryText);
        Task<int> GetSiteUserCount(long? organizationId, long? siteId, string queryText);
        Task<int> GetStudentGroupUserCount(long? organizationId, long? studentGroupId, string queryText);
        Task<int> GetUserInviteCount(long? organizationId, long? entityId, int inviteLevel, string queryText);
        public Task<List<UserAssignmentDto>> GetUserAssignments(string userId);
    }
}
