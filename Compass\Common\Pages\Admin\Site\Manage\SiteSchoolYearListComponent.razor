﻿@using Compass.Common.Interfaces.Services
@using Compass.Common.Models
@using Compass.Common.Services
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserSessionService UserSessionService
@inject UserAccessor UserAccessor
@inject ISiteService SiteService

<h2>Site School Year List</h2>

<div class="c4l-search-table-wrapper mt-3">
    <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

    @if (currentSchoolYear is not null)
    {
        <h3 class="text-center mt-5 mb-4">Current School Year: @currentSchoolYear.SchoolYearValue</h3>
    }

    <div class="c4l-table-scroll-wrapper">
        <div class="c4l-table-wrapper">
            <div class="c4l-table-headings-wrapper">
                <h6 class="c4l-table-heading">School Year</h6>
                <h6 class="c4l-table-heading">Description</h6>
                <h6 class="c4l-table-heading">Set as Current</h6>
            </div>

            @foreach (SchoolYear schoolYear in schoolYearPage)
            {
                <div class="c4l-table-result-wrapper">
                    <p class="c4l-table-result-item">@schoolYear.SchoolYearValue</p>
                    <p class="c4l-table-result-item">@schoolYear.Description</p>
                    <button class="c4l-button c4l-ghost-primary" @onclick="() => SetToCurrentSchoolYear(schoolYear)">Set As Current School Year</button>
                </div>
            }
        </div>
    </div>

    <div class="c4l-pagination-wrapper">
        <div class="c4l-pagination-buttons-wrapper">
            <div class="buttons-wrapper">
                <button class="c4l-button c4l-ghost-primary c4l-pagination-button"
                        @onclick="() => OnPreviousClicked()"
                        disabled="@(currentPage <= 1)">
                    Previous
                </button>

                <button class="c4l-button c4l-ghost-primary c4l-pagination-button"
                        @onclick="() => OnNextClicked()"
                        disabled="@(currentPage >= maxPages)">
                    Next
                </button>
            </div>

            <div class="add-user-button-wrapper">
                <button class="c4l-button c4l-secondary-button add-user-button" type="button" @onclick="OnCreateSchoolYear">Create School Year</button>
            </div>
        </div>

        <div class="page-count-wrapper font-weight-500">
            <span class="current-page-number">@currentPage</span> of @maxPages
        </div>
    </div>
</div>

<CreateSchoolYearBox 
    IsVisible="@isSchoolYearBoxVisible"
    CreateSchoolYearResult="OnSchoolYearResult"
    OrganizationId="currentOrganizationId"
    SiteId="currentSiteId" />