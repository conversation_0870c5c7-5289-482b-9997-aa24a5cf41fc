﻿@page "/c4l-noncontactdays"

@using Compass.C4L.Interfaces.Services
@using Compass.C4L.Models
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserAccessor UserAccessor
@inject UserSessionService UserSessionService

<div class="component-content-wrapper non-contact-day-list-wrapper">
    <div class="header-wrapper">
        <h3 class="page-title">Non-Contact Days</h3>
        <div class="button-wrapper">
            <button class="c4l-button c4l-secondary-button" @onclick="OnAddClick">Add</button>
            <button class="c4l-button c4l-primary-button" @onclick="OnDoneClick">Done</button>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger" role="alert">
            @errorMessage
        </div>
    }

    @if (!string.IsNullOrEmpty(successMessage))
    {
        <div class="alert alert-success" role="alert">
            @successMessage
        </div>
    }

    <div class="non-contact-days-list">
        <table class="table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var nonContactDay in nonContactDays)
                {
                    <tr>
                        <td>@nonContactDay.Description</td>
                        <td>@nonContactDay.StartDate.ToShortDateString()</td>
                        <td>@nonContactDay.EndDate.ToShortDateString()</td>
                        <td>
                            <button class="c4l-button c4l-secondary-button btn-sm" @onclick="() => OnEditClick(nonContactDay.Id)">
                                Edit
                            </button>
                            <button class="c4l-button c4l-danger-button btn-sm" @onclick="() => OnDeleteClick(nonContactDay.Id)">
                                Delete
                            </button>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

@if (showDeleteConfirmation)
{
    <div class="modal fade show" style="display: block" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" @onclick="CancelDelete"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this non-contact day?
                </div>
                <div class="modal-footer">
                    <button type="button" class="c4l-button c4l-secondary-button" @onclick="CancelDelete">Cancel</button>
                    <button type="button" class="c4l-button c4l-danger-button" @onclick="ConfirmDelete">Delete</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show"></div>
}

<style>
    .header-wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
    }

    .button-wrapper {
        display: flex;
        gap: 1rem;
    }

    .page-title {
        margin: 0;
    }

    .non-contact-day-list-wrapper {
        padding: 20px;
    }

    .page-title {
        margin-bottom: 2rem;
    }

    .non-contact-days-list {
        margin-top: 1rem;
    }

    .table {
        width: 100%;
        margin-bottom: 1rem;
        background-color: transparent;
    }

        .table th,
        .table td {
            padding: 0.75rem;
            vertical-align: top;
            border-top: 1px solid #dee2e6;
        }

        .table thead th {
            vertical-align: bottom;
            border-bottom: 2px solid #dee2e6;
        }
</style>
