using Compass.C4L.DTOs;
using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Runtime.CompilerServices;
using System.Security.Claims;

namespace Compass.C4L.Repositories
{
    public class C4LClassroomRepository : IC4LClassroomRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public C4LClassroomRepository(
            IDbContextFactory<ApplicationDbContext> contextFactory,
            AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<C4LClassroom?> GetC4LClassroomByIdAsync(long id)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                C4LClassroom? ret = await _dbContext.C4LClassrooms.FindAsync(id);
                return ret;
            }
        }

        public async Task<C4LClassroom?> GetByStudentGroupIdAsync(long studentGroupId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                C4LClassroom? ret = await _dbContext.C4LClassrooms
                .FirstOrDefaultAsync(c => c.StudentGroupId == studentGroupId);
                return ret;
            }
        }

        public async Task<IEnumerable<C4LClassroom>> GetC4LClassroomByOrganizationIdAsync(long organizationId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                IEnumerable<C4LClassroom> ret = await _dbContext.C4LClassrooms
                .Where(c => c.OrganizationId == organizationId)
                .ToListAsync();

                return ret;
            }
        }

        public async Task<C4LClassroom?> UpdateC4LClassroomAsync(long? id, C4LClassroom classroom)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (classroom is null)
            {
                throw new ArgumentNullException(nameof(classroom));
            }


            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                C4LClassroom? existingClassroom = await _dbContext.C4LClassrooms.FindAsync(id);

                if (existingClassroom is null)
                {
                    return null;
                }

                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                existingClassroom.ModId = userId;
                existingClassroom.ModTs = DateTime.Now;

                existingClassroom.StartDate = DateTime.Now;

                _dbContext.C4LClassrooms.Update(existingClassroom);
                await _dbContext.SaveChangesAsync();
                return existingClassroom;
            }
        }

        public async Task<C4LClassroom> CreateC4LClassroomAsync(C4LClassroom classroom)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                classroom.ModId = userId;
                classroom.ModTs = DateTime.Now;

                await _dbContext.C4LClassrooms.AddAsync(classroom);
                await _dbContext.SaveChangesAsync();
                return classroom;
            }
        }

        public async Task<C4LClassroom?> GetCurrentByStudentGroupIdAsync(long? organizationId, long? studentGroupId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (studentGroupId is null)
            {
                throw new ArgumentNullException(nameof(studentGroupId));
            }

            string sqlQuery = @"SELECT cls.id, cls.organization_id, cls.mod_id, cls.mod_ts, cls.student_group_id, cls.start_date, cls.school_year_id
                                FROM c4l_classrooms AS cls
                                INNER JOIN cmn_student_groups AS g
	                                ON g.id = cls.student_group_id
		                                AND g.organization_id = cls.organization_id
                                INNER JOIN cmn_school_years AS sy
	                                ON sy.site_id = g.site_id
		                                AND sy.organization_id = g.organization_id
		                                AND cls.school_year_id = sy.id
                                WHERE sy.status = 'current'
	                                AND g.organization_id = {0}
	                                AND g.id = {1}";

            C4LClassroom? c4lClassroom = null;
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                c4lClassroom = await _dbContext.C4LClassrooms
                    .FromSqlRaw(sqlQuery, organizationId, studentGroupId)
                    .FirstOrDefaultAsync();
            }

            return c4lClassroom;
        }

        public async Task<List<C4LClassroomSelectionDisplayDto>> GetCurrentClassroomsByAssignedStudentIdAsync(long? organizationId, long? studentId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (studentId is null)
            {
                throw new ArgumentNullException(nameof(studentId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                string sqlQuery = @"SELECT cls.id, cls.student_group_id AS StudentGroupId, cls.organization_id AS OrganizationId, g.name AS StudentGroupName, cls.start_date AS StartDate
                                FROM cmn_students AS s
                                INNER JOIN cmn_student_group_roster AS gr
	                                ON gr.student_id = s.id
                                INNER JOIN c4l_classrooms AS cls
	                                ON cls.student_group_id = gr.student_group_id
                                INNER JOIN cmn_student_groups AS g
                                    ON g.id = cls.student_group_id
                                        AND g.organization_id = cls.organization_id
                                INNER JOIN cmn_school_years AS sy
                                    ON sy.site_id = g.site_id
                                        AND sy.organization_id = g.organization_id
                                        AND cls.school_year_id = sy.id
                                WHERE sy.status = 'current'
	                                AND g.organization_id = {0}
	                                AND s.id = {1}";

                // Use FormattableString to safely format the SQL query
                FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, studentId);

                // Execute the raw SQL query and map directly to the DTO
                List<C4LClassroomSelectionDisplayDto> c4lClassroomList = await _dbContext.Database
                    .SqlQuery<C4LClassroomSelectionDisplayDto>(formattedQuery)
                    .ToListAsync();

                return c4lClassroomList;
            }
        }

        public async Task<int> GetCurrentClassroomsByAssignedStudentIdCountAsync(long? organizationId, long? studentId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (studentId is null)
            {
                throw new ArgumentNullException(nameof(studentId));
            }

            string sqlQuery = @"SELECT COUNT(DISTINCT cls.id) As Value
                                FROM cmn_students AS s
                                INNER JOIN cmn_student_group_roster AS gr
	                                ON gr.student_id = s.id
                                INNER JOIN c4l_classrooms AS cls
	                                ON cls.student_group_id = gr.student_group_id
                                INNER JOIN cmn_student_groups AS g
                                    ON g.id = cls.student_group_id
                                        AND g.organization_id = cls.organization_id
                                INNER JOIN cmn_school_years AS sy
                                    ON sy.site_id = g.site_id
                                        AND sy.organization_id = g.organization_id
                                        AND cls.school_year_id = sy.id
                                WHERE sy.status = 'current'
	                                AND g.organization_id = {0}
	                                AND s.id = {1}";

            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, studentId);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();
                return count;
            }
        }
    }
}