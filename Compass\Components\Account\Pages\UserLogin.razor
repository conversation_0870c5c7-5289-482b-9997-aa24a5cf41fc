﻿@page "/login"

@using Compass.Common.Data
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@inject SignInManager<ApplicationUser> SignInManager
@inject ILogger<Login> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager
@inject CommonSessionDataObserver CommonSessionDataObserver;
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserManager<ApplicationUser> UserManager
@inject UserSessionService UserSessionService
@inject UserAccessor UserAccessor
@inject CommonSessionDataObserver CommonSessionDataObserver;
@inject IOrganizationHierarchyRepository OrganizationHierarchyRepository
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<PageTitle>@Localizer["login-text"] | C4L</PageTitle>

<section class="login-component-section">
    <h1 class="page-title block-end-margin">@Localizer["login-page-heading"]</h1>

    <StatusMessage Message="@errorMessage" AlertType="@alertType" />

    <EditForm Model="Input" method="post" OnValidSubmit="LoginUser" FormName="login" class="c4l-form c4l-login-form">
        <DataAnnotationsValidator />
        <ValidationSummary class="text-danger" role="alert" />
        <h3 class="c4l-form-heading">@Localizer["login-page-form-heading"]</h3>

        <div class="form-floating mb-3">
            <InputText @bind-Value="Input.Username" id="username" class="form-control" autocomplete="username" aria-required="true" placeholder="Username" />
            <label for="username" class="form-label">@Localizer["lbl_Username"]</label>
            <ValidationMessage For="() => Input.Username" class="text-danger" />
        </div>

        <div class="form-floating mb-3">
            <InputText type="password" id="password" @bind-Value="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" placeholder="Password" />
            <label for="password" class="form-label">@Localizer["password-label"]</label>
            <ValidationMessage For="() => Input.Password" class="text-danger" />
        </div>

        <div class="mb-3">
            <label for="remember-me" class="form-label form-checkbox-label d-flex">
                <InputCheckbox @bind-Value="Input.RememberMe" id="remember-me" class="darker-border-checkbox form-check-input" />
                @Localizer["login-page-remember-me"]
            </label>
        </div>

        <button type="submit" class="c4l-button c4l-form-button c4l-primary-button">@Localizer["login-text"]</button>

        <div class="additional-login-links d-flex">
            <a class="c4l-button c4l-ghost-primary text-center" href="forgot-password">@Localizer["login-page-forgot-password"]</a>
            <a class="c4l-button c4l-ghost-primary text-center" href="Account/ResendEmailConfirmation">@Localizer["login-page-resend-email"]</a>
        </div>
    </EditForm>

    <div>
        <CascadingValue Value="@ReturnUrl">
            <ExternalLoginPicker />
        </CascadingValue>
    </div>
</section>
