﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Common.Models
{
    [Table("cmn_school_years")]
    public class SchoolYear
    {
        [Key]
        public long Id { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Column("site_id")]
        public long? SiteId { get; set; }

        [Column("school_year")]
        public int? SchoolYearValue { get; set; }

        [Column("description")]
        public string? Description { get; set; }

        [Column("status")]
        public string? Status { get; set; }

    }
}
