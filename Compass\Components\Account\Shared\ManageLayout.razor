﻿@inherits LayoutComponentBase
@layout AccountLayout

@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<h1 class="page-title horizontal-line">@Localizer["manage-account-settings"]</h1>

<div class="manage-account-grid-wrapper d-grid">
    <div class="manage-account-nav-tabs">
        <ManageNavMenu />
    </div>
    
    <div class="manage-account-content text-center">
        @Body
    </div>
</div>
