using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.EntityFrameworkCore;

namespace Compass.C4L.Repositories
{
    public class C4LLearningCenterRepository : IC4LLearningCenterRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

        public C4LLearningCenterRepository(IDbContextFactory<ApplicationDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<List<C4LLearningCenter>> GetLearningCentersByUnitAsync(string language, string unit)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                string sql = @"
                    SELECT * FROM c4l_learning_center_lookup
                    WHERE language = {0} AND unit = {1}
                    ORDER BY title";

                List<C4LLearningCenter> learningCenters = await _dbContext.C4LLearningCenters
                    .FromSqlRaw(sql, language, unit)
                    .ToListAsync();

                return learningCenters;
            }
        }

        public async Task<List<C4LLearningCenter>> GetAllLearningCentersAsync(string language)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                string sql = @"
                    SELECT * FROM c4l_learning_center_lookup
                    WHERE language = {0}
                    ORDER BY unit, title";

                List<C4LLearningCenter> learningCenters = await _dbContext.C4LLearningCenters
                    .FromSqlRaw(sql, language)
                    .ToListAsync();

                return learningCenters;
            }
        }
    }
}

