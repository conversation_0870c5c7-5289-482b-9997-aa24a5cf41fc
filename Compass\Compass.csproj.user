﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ActiveDebugProfile>https</ActiveDebugProfile>
    <NameOfLastUsedPublishProfile>C:\Workspace\GitRepos\KELC\Compass-Blazor-branches\main\Compass\Properties\PublishProfiles\kaplancompassdev - Web Deploy.pubxml</NameOfLastUsedPublishProfile>
    <Controller_SelectedScaffolderID>MvcControllerWithActionsScaffolder</Controller_SelectedScaffolderID>
    <Controller_SelectedScaffolderCategoryPath>root/Common/MVC/Controller</Controller_SelectedScaffolderCategoryPath>
  </PropertyGroup>
  <ItemGroup>
    <EmbeddedResource Update="Common\Resources\CommonResource.es-ES.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Update="Common\Resources\CommonResource.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
</Project>