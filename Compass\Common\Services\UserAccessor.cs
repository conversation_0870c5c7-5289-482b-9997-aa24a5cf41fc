﻿using Compass.Common.Data;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using System.Security.Claims;
using System.Text.Json;

namespace Compass.Common.Services
{
    public class UserAccessor
    {
        public static readonly string USER_ROLE_SUPER_ADMIN = "SuperAdmin";

        private readonly UserManager<ApplicationUser> _userManager;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger<UserAccessor> _logger;

        public UserAccessor(UserManager<ApplicationUser> userManager,
                            AuthenticationStateProvider authenticationStateProvider,
                             IDbContextFactory<ApplicationDbContext> contextFactory,
                             IDistributedCache distributedCache,
                             ILogger<UserAccessor> logger)
        {
            _userManager = userManager;
            _authenticationStateProvider = authenticationStateProvider;
            _contextFactory = contextFactory;
            _distributedCache = distributedCache;
            _logger = logger;
        }

        public async Task<ApplicationUser?> GetUserAsync(string? userId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.ApplicationUsers.FirstOrDefaultAsync(o => o.Id == userId);
            }
        }

        public async Task<(ApplicationUser? User, string? UserId)> GetUserAndIdAsync()
        {
            var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user.Identity is { IsAuthenticated: true })
            {
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
                if (userId != null)
                {
                    string? cachedUserJson = await _distributedCache.GetStringAsync($"user:{userId}");
                    if (!string.IsNullOrEmpty(cachedUserJson))
                    {
                        try
                        {
                            var cachedUser = JsonSerializer.Deserialize<ApplicationUser>(cachedUserJson);
                            if (cachedUser != null)
                            {
                                return (cachedUser, userId);
                            }
                            else
                            {
                                _logger.LogWarning("Deserialized user from cache was null for user ID: {UserId}", userId);
                                await _distributedCache.RemoveAsync($"user:{userId}"); // Remove invalid cache entry
                            }
                        }
                        catch (JsonException ex)
                        {
                            _logger.LogError(ex, "Error deserializing user from cache for user ID: {UserId}", userId);
                            await _distributedCache.RemoveAsync($"user:{userId}"); // Remove invalid cache entry
                        }
                    }

                    // var identityUser = await _userManager.GetUserAsync(user);
                    using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                    {
                        try
                        {
                            string sqlQuery = $@"
                            SELECT id, UserName, NormalizedUserName, Email, NormalizedEmail, EmailConfirmed
                                ,PasswordHash ,SecurityStamp ,ConcurrencyStamp
                                ,PhoneNumber ,PhoneNumberConfirmed
                                ,TwoFactorEnabled ,LockoutEnd ,LockoutEnabled ,AccessFailedCount
                                ,FirstName ,LastName ,OrganizationId
                            FROM AspNetUsers
                            WHERE id = '{userId}'";

                            ApplicationUser? identityUser = await _dbContext.Set<ApplicationUser>().FromSqlRaw(sqlQuery)
                            .Select(o => new ApplicationUser
                            {
                                Id = o.Id,
                                UserName = o.UserName,
                                NormalizedUserName = o.NormalizedUserName,
                                Email = o.Email,
                                NormalizedEmail = o.NormalizedEmail,
                                EmailConfirmed = o.EmailConfirmed,
                                PasswordHash = o.PasswordHash,
                                SecurityStamp = o.SecurityStamp,
                                ConcurrencyStamp = o.ConcurrencyStamp,
                                PhoneNumber = o.PhoneNumber,
                                PhoneNumberConfirmed = o.PhoneNumberConfirmed,
                                TwoFactorEnabled = o.TwoFactorEnabled,
                                LockoutEnd = o.LockoutEnd,
                                LockoutEnabled = o.LockoutEnabled,
                                AccessFailedCount = o.AccessFailedCount,
                                FirstName = o.FirstName,
                                LastName = o.LastName,
                                OrganizationId = o.OrganizationId
                            }).SingleOrDefaultAsync();

                            if (identityUser != null)
                            {
                                var jsonToCache = JsonSerializer.Serialize(identityUser);
                                var cacheOptions = new DistributedCacheEntryOptions { AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30) }; // Set appropriate expiration
                                await _distributedCache.SetStringAsync($"user:{userId}", jsonToCache, cacheOptions);
                                return (identityUser, userId);
                            }
                            else
                            {
                                _logger.LogWarning("User not found in database for user ID: {UserId}", userId);
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine(ex.Message);
                            _logger.LogError(ex, "Error trying to retrieve user from database: {UserId}", userId);
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("NameIdentifier claim not found for authenticated user.");
                }
            }

            return (null, null);
        }

        public async Task<List<string>?> GetUserRolesAsync()
        {
            var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user == null)
            {
                return null;
            }

            if (user.Identity == null)
            {
                return null;
            }

            if (!user.Identity.IsAuthenticated)
            {
                return null;
            }

            if (user.Identity is { IsAuthenticated: true })
            {
                // Get the user from UserManager
                var identityUser = await _userManager.FindByNameAsync(user.Identity.Name);
                // Get user roles
                var userRoles = (await _userManager.GetRolesAsync(identityUser)).ToList();
                return userRoles;
            }

            return null;
        }

        public async Task<bool> IsUserInRoleAsync(string roleName)
        {
            var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;

            if (user == null)
            {
                return false;
            }

            if (user.Identity == null)
            {
                return false;
            }

            if (!user.Identity.IsAuthenticated)
            {
                return false;
            }

            using (var context = await _contextFactory.CreateDbContextAsync())
            {
                var userId = user.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var applicationUser = await context.Users
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (applicationUser == null)
                {
                    return false;
                }

                bool returnValue = await _userManager.IsInRoleAsync(applicationUser, roleName);
                return returnValue;
            }
        }

        public async Task<bool> IsUserInRoleAsync(string? userId, string roleName)
        {
            string sqlQuery = string.Empty;
            sqlQuery = @"
                    SELECT COUNT(distinct anr.Name)
                    FROM AspNetUsers anu  
                        JOIN AspNetUserRoles anur on anu.Id = anur.UserId 
                        JOIN AspNetRoles anr on anur.RoleId = anr.Id
                    WHERE anu.Id = @userId AND anr.Name = @roleName";

            using (var context = await _contextFactory.CreateDbContextAsync())
            {
                using var command = context.Database.GetDbConnection().CreateCommand();
                command.CommandText = sqlQuery;
                command.Parameters.Add(new SqlParameter("@userId", userId));
                command.Parameters.Add(new SqlParameter("@roleName", roleName));

                await context.Database.OpenConnectionAsync();
                int count = Convert.ToInt32(await command.ExecuteScalarAsync());

                return count > 0;
            }
        }
    }
}
