using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.DTOs
{
    public class SubscaleContainer
    {
        public List<Milestone> Lap3Subscales { get; set; } = new List<Milestone>();
        public List<Milestone> LapDSubscales { get; set; } = new List<Milestone>();
        public List<Milestone> ElapSubscales { get; set; } = new List<Milestone>();
        public List<Milestone> UlapSubscales { get; set; } = new List<Milestone>();
        public List<Milestone> LapD3Subscales { get; set; } = new List<Milestone>();
        public List<Milestone> LapD3SpanishSubscales { get; set; } = new List<Milestone>();
    }
}
