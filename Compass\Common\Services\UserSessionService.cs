﻿using Compass.Common.SessionHandlers;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

namespace Compass.Common.Services
{
    public class UserSessionService
    {
        private readonly IDistributedCache _cache;

        public UserSessionService(IDistributedCache cache)
        {
            _cache = cache;
        }

        public async Task SetUserSessionAsync(string userId, CommonSessionData sessionData, TimeSpan expiration)
        {
            var options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration
            };

            var serializedData = JsonSerializer.Serialize(sessionData);
            await _cache.SetStringAsync(userId, serializedData, options);
        }

        public async Task SetUserSessionAsync(string userId, CommonSessionData sessionData)
        {
            var expiration = TimeSpan.FromDays(1);
            await SetUserSessionAsync(userId, sessionData, expiration);
        }

        public async Task<CommonSessionData?> GetUserSessionAsync(string userId)
        {
            var serializedData = await _cache.GetStringAsync(userId);

            return serializedData is null
                ? default
                : JsonSerializer.Deserialize<CommonSessionData>(serializedData);
        }

        public async Task ClearUserSessionAsync(string userId)
        {
            await _cache.RemoveAsync(userId);
        }
    }
}
