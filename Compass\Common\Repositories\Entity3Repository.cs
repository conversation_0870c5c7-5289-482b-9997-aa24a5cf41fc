﻿using Compass.Common.Data;
using Compass.Common.DTOs.Entity3;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Runtime.CompilerServices;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class Entity3Repository : IEntity3Repository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public Entity3Repository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        async Task<Entity3> IEntity3Repository.CreateEntity3Async(Entity3 entity3)
        {
            // Get the authentication state
            var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            entity3.ModId = userId;
            entity3.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.Entities3.AddAsync(entity3);
                await _dbContext.SaveChangesAsync();
            }

            return entity3;
        }

        public async Task<List<Entity3>> GetEntities3Async(long? organizationId, long? entity2Id)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Entities3.Where(o => o.OrganizationId == organizationId && o.Entity2Id == entity2Id).ToListAsync();
            }
        }

        async Task<Entity3?> IEntity3Repository.GetEntity3Async(long? id)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Entities3.FirstOrDefaultAsync(o => o.Id == id);
            }
        }

        async Task<Entity3?> IEntity3Repository.UpdateEntity3Async(long? id, Entity3 entity3)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                var existingEntity3 = await _dbContext.Entities3.FindAsync(id);

                if (existingEntity3 is null)
                {
                    return null;
                }

                // Get the authentication state
                var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                var user = authState.User;
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                existingEntity3.ModId = userId;
                existingEntity3.ModTs = DateTime.Now;

                existingEntity3.Name = entity3.Name;
                existingEntity3.Address1 = entity3.Address1;
                existingEntity3.Address2 = entity3.Address2;
                existingEntity3.City = entity3.City;
                existingEntity3.State = entity3.State;
                existingEntity3.ZipCode = entity3.ZipCode;
                existingEntity3.Country = entity3.Country;

                existingEntity3.ContactEmail = entity3.ContactEmail;
                existingEntity3.ContactFirstName = entity3.ContactFirstName;
                existingEntity3.ContactLastName = entity3.ContactLastName;
                existingEntity3.ContactFax = entity3.ContactFax;
                existingEntity3.ContactPhone = entity3.ContactPhone;
                existingEntity3.Fax = entity3.Fax;

                _dbContext.Entities3.Update(existingEntity3);
                await _dbContext.SaveChangesAsync();

                return existingEntity3;
            }
        }

        public async Task<List<Entity3ListDisplayDto>> GetEntity3List(Entity3ListAction action)
        {
            List<Entity3ListDisplayDto> results = new List<Entity3ListDisplayDto>();
            PageQuery pageQuery = action.pageQuery;
            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", action.userId),
                new SqlParameter("@OrganizationId", action.organizationId),
                new SqlParameter("@SearchCriteria", pageQuery.QueryText),
                new SqlParameter("@PageOffset", pageQuery.GetOffset()),
                new SqlParameter("@PageSize", pageQuery.PageSize)
            };

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                // Use ADO.NET directly for more control
                using var command = _dbContext.Database.GetDbConnection().CreateCommand();
                command.CommandText = "cmn_accessible_entity3_get_list";
                command.CommandType = CommandType.StoredProcedure;

                foreach (var param in parameters)
                {
                    command.Parameters.Add(param);
                }

                // Ensure connection is open
                if (command.Connection.State != ConnectionState.Open)
                {
                    await command.Connection.OpenAsync();
                }

                try
                {
                    using var reader = await command.ExecuteReaderAsync();

                    // Check if we have any rows
                    if (!reader.HasRows)
                    {
                        return results; // Return empty list
                    }

                    while (await reader.ReadAsync())
                    {
                        try
                        {
                            var entity3 = new Entity3ListDisplayDto
                            {
                                // Use GetOrdinal to find column positions and safely get values
                                Id = reader.GetInt64(reader.GetOrdinal("id")),
                                Name = reader.IsDBNull(reader.GetOrdinal("name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("name")),
                                ContactName = reader.IsDBNull(reader.GetOrdinal("ContactName"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactName")),
                                ContactEmail = reader.IsDBNull(reader.GetOrdinal("ContactEmail"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactEmail")),
                                ContactPhone = reader.IsDBNull(reader.GetOrdinal("ContactPhone"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactPhone")),
                                Entity1Name = reader.IsDBNull(reader.GetOrdinal("Entity1Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity1Name")),
                                Entity1Id = reader.GetInt64(reader.GetOrdinal("Entity1Id")),
                                Entity2Name = reader.IsDBNull(reader.GetOrdinal("Entity2Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity2Name")),
                                Entity2Id = reader.GetInt64(reader.GetOrdinal("Entity2Id"))
                            };

                            results.Add(entity3);
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue processing other rows
                            System.Diagnostics.Debug.WriteLine($"Error processing row: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error executing stored procedure: {ex.Message}");
                    throw; // Rethrow the exception after logging
                }
            }

            return results;
        }

        public async Task<int> GetEntity3Count(long? organizationId, long? entity1Id, long? entity2Id, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

            SqlParameter[] parameters;
            string sqlQuery;
            if (entity2Id != null)
            {
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OrganizationId", organizationId),
                    new SqlParameter("@Entity1Id", entity1Id),
                    new SqlParameter("@Entity2Id", entity2Id),
                    new SqlParameter("@SearchCriteria", queryText)
                };

                sqlQuery = "EXEC [cmn_accessible_entity3_get_count] @UserId, @OrganizationId, NULL, @Entity2Id, @SearchCriteria";
            }
            else if (entity1Id != null)
            {
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OrganizationId", organizationId),
                    new SqlParameter("@Entity1Id", entity1Id),
                    new SqlParameter("@SearchCriteria", queryText)
                };

                sqlQuery = "EXEC [cmn_accessible_entity3_get_count] @UserId, @OrganizationId, @Entity1Id, NULL, @SearchCriteria";
            }
            else
            {
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OrganizationId", organizationId),
                    new SqlParameter("@SearchCriteria", queryText)
                };

                sqlQuery = "EXEC [cmn_accessible_entity3_get_count] @UserId, @OrganizationId, NULL, NULL, @SearchCriteria";
            }

            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, parameters);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<int> results = await _dbContext.Database
                                .SqlQuery<int>(formattedQuery)
                                .ToListAsync();

                int count = results.FirstOrDefault();

                return count;
            }
        }

        public async Task<bool> DeleteEntity3(long? id)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                Entity3? existingEntity3 = await _dbContext.Entities3.FindAsync(id);

                if (existingEntity3 is null)
                {
                    throw new Exception("Entity3 not found");
                }

                // Get the authentication state
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                if (userId != null)
                {
                    existingEntity3.ModId = userId;
                    existingEntity3.ModTs = DateTime.Now;

                    existingEntity3.IsDeleted = "Y";

                    _dbContext.Entities3.Update(existingEntity3);
                    await _dbContext.SaveChangesAsync();

                    return true;
                }
                else
                {
                    throw new Exception("UserID not found");
                }
            }
        }
    }
}
