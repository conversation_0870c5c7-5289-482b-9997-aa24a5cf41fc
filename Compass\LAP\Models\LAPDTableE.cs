using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_lapd_table_e")]
    public class LAPDTableE
    {
        [Key]
        [Column("InstID")]
        public long Id { get; set; }

        [Required]
        [Column("MinAge")]
        public int MinAge { get; set; }

        [Required]
        [Column("MaxAge")]
        public int MaxAge { get; set; }

        [Required]
        [Column("MinScore")]
        public int MinScore { get; set; }

        [Required]
        [Column("MaxScore")]
        public int MaxScore { get; set; }

        [Required]
        [Column("PercentileRank")]
        public int PercentileRank { get; set; }

        [Required]
        [Column("Language")]
        public int Language { get; set; }

        [Required]
        [Column("Instrument")]
        public int Instrument { get; set; }
    }
}