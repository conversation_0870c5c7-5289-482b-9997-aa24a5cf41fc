using Compass.Common.Data;

namespace Compass.LAP.DTOs
{
    public class LapChildRowContainer
    {
        public ApplicationUser Child { get; set; }
        public string ChildName { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public int Checkpoint { get; set; }

        public List<LapAssessmentContainerDto> ElapAssessments { get; set; } = new List<LapAssessmentContainerDto>();
        public List<LapAssessmentContainerDto> UlapAssessments { get; set; } = new List<LapAssessmentContainerDto>();
        public List<LapAssessmentContainerDto> Lap3Assessments { get; set; } = new List<LapAssessmentContainerDto>();
        public LapAssessmentContainerDto LapD3Assessment { get; set; }
        public LapAssessmentContainerDto LapD3SpanishAssessment { get; set; }
        public LapAssessmentContainerDto LapDAssessment { get; set; }
        public List<LapScreenContainer> LapScreenContainerList { get; set; } = new List<LapScreenContainer>();
        public List<LapScreenContainer> LapSpanishScreenContainerList { get; set; } = new List<LapScreenContainer>();
    }
}
