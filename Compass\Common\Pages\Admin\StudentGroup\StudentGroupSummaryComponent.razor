﻿@using Compass.Common.Data
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject IStudentGroupRepository StudentGroupRepository;
@inject UserAccessor UserAccessor
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<div class="container mt-4">
    <LoaderComponent IsLoading="isLoading">
        <ContactSummary TeacherName="Teacher <PERSON>" SchoolName="@siteHierarchy" NumberOfChildren="9" />
        <div>
            @if(hasLAPLicense)
            {
                <button @onclick="OnLAPOptionClick">LAP</button>
            }
            @if (hasDECALicense)
            {
                <button @onclick="OnDECAOptionClick">DECA</button>
            }
            @if (hasC4LLicense)
            {
                <button @onclick="OnC4LOptionClick">C4L</button>
            }
        </div>
    </LoaderComponent>
</div>
