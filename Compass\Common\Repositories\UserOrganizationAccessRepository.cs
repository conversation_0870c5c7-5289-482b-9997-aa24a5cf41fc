﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class UserOrganizationAccessRepository : IUserOrganizationAccessRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public UserOrganizationAccessRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<UserOrganizationAccess?> GetUserOrganizationAccessAsync(long? organizationId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.UserOrganizationAccesses.FirstOrDefaultAsync(o => o.OrganizationId == organizationId);
            }
        }

        public async Task<UserOrganizationAccess?> AddUserOrganizationAccessAsync(UserOrganizationAccess? userOrganizationAccess)
        {
            if (userOrganizationAccess is null)
            {
                throw new ArgumentNullException(nameof(userOrganizationAccess));
            }

            // Get the authentication state 
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            userOrganizationAccess.ModId = userId;
            userOrganizationAccess.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.UserOrganizationAccesses.AddAsync(userOrganizationAccess);
                await _dbContext.SaveChangesAsync();
            }

            return userOrganizationAccess;
        }
    }
}
