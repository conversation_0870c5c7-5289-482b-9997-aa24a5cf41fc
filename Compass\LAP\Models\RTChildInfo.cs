using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_rt_child_info")]
    public class RTChildInfo
    {
        [Key]
        [Column("InstID")]
        public long InstId { get; set; }

        [Required]
        [Column("ChildInstID")]
        public long ChildInstId { get; set; }

        [Required]
        [Column("ModTS")]
        public DateTime ModTs { get; set; }

        [Required]
        [Column("ModInstID")]
        public long ModInstId { get; set; }

        [Required]
        [Column("IndividualInstID")]
        public long IndividualInstId { get; set; }

        [Column("FamilyInstID")]
        public long? FamilyInstId { get; set; }

        [Column("FirstName")]
        [StringLength(30)]
        public string? FirstName { get; set; }

        [Column("LastName")]
        [StringLength(30)]
        public string? LastName { get; set; }

        [Column("SchoolID")]
        [StringLength(50)]
        public string? SchoolId { get; set; }

        [Required]
        [Column("SchoolYearInstID")]
        public long SchoolYearInstId { get; set; }

        [Required]
        [Column("SchoolYear")]
        public short SchoolYear { get; set; }

        [Column("Birthdate")]
        public DateTime? Birthdate { get; set; }

        [Column("ChronologicalAge")]
        public int? ChronologicalAge { get; set; }

        [Column("SchoolAge")]
        public int? SchoolAge { get; set; }

        [Column("EnrollmentDate")]
        public DateTime? EnrollmentDate { get; set; }

        [Column("AgeAtKindergarten")]
        public int? AgeAtKindergarten { get; set; }

        [Column("PrematureBirthWeeks")]
        public int? PrematureBirthWeeks { get; set; }

        [Column("EnteringKindergarten")]
        [StringLength(3)]
        public string? EnteringKindergarten { get; set; }

        [Column("UserDefined1")]
        public int? UserDefined1 { get; set; }

        [Column("UserDefined2")]
        public int? UserDefined2 { get; set; }

        [Column("UserDefined3")]
        public int? UserDefined3 { get; set; }

        [Column("UserDefined4")]
        public int? UserDefined4 { get; set; }

        [Column("UserDefined5")]
        public int? UserDefined5 { get; set; }

        [Column("Gender")]
        public int? Gender { get; set; }

        [Column("Race")]
        public int? Race { get; set; }

        [Column("RaceSelections")]
        [StringLength(9)]
        public string? RaceSelections { get; set; }

        [Column("Ethnicity")]
        public byte? Ethnicity { get; set; }

        [Column("HispanicLatino")]
        public int? HispanicLatino { get; set; }

        [Column("IEPIFSP")]
        public int? IepIfsp { get; set; }

        [Column("IepIfspUpdateType")]
        [StringLength(4)]
        public string? IepIfspUpdateType { get; set; }

        [Column("IepIfspUpdateDate")]
        public DateTime? IepIfspUpdateDate { get; set; }

        [Column("PrimaryLanguage")]
        public short? PrimaryLanguage { get; set; }

        [Column("PrimaryTestingLanguage")]
        public int? PrimaryTestingLanguage { get; set; }

        [Column("RetestLanguage")]
        [StringLength(20)]
        public string? RetestLanguage { get; set; }

        [Column("ell")]
        public int? Ell { get; set; }

        [Column("PrimaryDiagnosis")]
        public int? PrimaryDiagnosis { get; set; }

        [Column("SecondaryDiagnosis")]
        public int? SecondaryDiagnosis { get; set; }

        [Column("PrimaryDisability")]
        [StringLength(50)]
        public string? PrimaryDisability { get; set; }

        [Column("DevelopmentDisability")]
        [StringLength(1)]
        public string? DevelopmentDisability { get; set; }

        [Column("FundingSource")]
        public short? FundingSource { get; set; }

        [Column("singleParentFamily")]
        public int? SingleParentFamily { get; set; }

        [Column("parentDidNotGraduateHighschool")]
        public int? ParentDidNotGraduateHighschool { get; set; }

        [Column("bothParentsAbsent")]
        public int? BothParentsAbsent { get; set; }

        [Column("freeReducedLunch")]
        public int? FreeReducedLunch { get; set; }

        [Column("prematureBirth")]
        public int? PrematureBirth { get; set; }

        [Column("ReceivesESLServices")]
        [StringLength(1)]
        public string? ReceivesEslServices { get; set; }
    }
}