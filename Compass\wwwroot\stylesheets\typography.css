:root {
  --transition-speed: 0.3s;
}

html,
body {
  font-family: 'Avenir Next', sans-serif;
}

:where(.page-title) {
  text-align: center;
  margin-block: 2rem 4rem;
  color: var(--c4l-primary-purple);

  &.horizontal-line {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      bottom: -3rem;
      width: min(100%, 1600px);
      height: 1px;
      background-color: var(--c4l-primary-purple);
      transform: translateX(-50%);
    }
  }

  &.block-end-margin {
    margin-block-end: 4rem;
  }
}

:where(h1) {
  &:focus-visible {
    outline: 1px solid transparent;
  }
}

h1, h2, h3, h4, h5, h6 {
  margin-block: 0;
}

.font-weight-200 {
  font-weight: 200;
}

.font-weight-400 {
  font-weight: 400;
}

.font-weight-500 {
  font-weight: 500;
}

.font-weight-600 {
  font-weight: 600;
}

.font-weight-700 {
  font-weight: 700;
}

.font-weight-900 {
  font-weight: 900;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.current-page-number {
  color: var(--c4l-secondary-teal);
}

kbd {
  background-color: var(--c4l-primary-purple);
}

.alert {
  border: none;
  border-inline-start: 0.25rem solid transparent;

  &.alert-success {
    border-color: #0f5132;
  }

  &.alert-info {
    border-color: #055160;
  }

  &.alert-warning {
    border-color: #664d03;
  }

  &.alert-danger {
    border-color: var(--c4l-danger);
  }
}
