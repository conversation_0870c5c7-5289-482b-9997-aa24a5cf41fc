﻿using Compass.Common.Data;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity2
{
    public partial class Entity2AddComponent : IDisposable
    {
        private Compass.Common.Models.Entity2? entity2 { get; set; }

        private string? entity2Name = string.Empty;

        private string successMessage = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.entity2Name = commonSessionData.Entity2Hierarchy;
            }
            this.entity2 = new Compass.Common.Models.Entity2();
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        protected async Task SubmitAsync()
        {
            if (this.entity2 is not null)
            {
                CommonSessionData? commonSessionData = await GetCommonSessionData();
                if (commonSessionData is not null)
                {
                    this.entity2.OrganizationId = commonSessionData.CurrentOrganizationId;
                    this.entity2.Entity1Id = commonSessionData.CurrentEntity1Id;

                    this.entity2 = await Entity2Service.CreateEntity2Async(this.entity2);

                    successMessage = "Information saved successfully!";

                    this.entity2 = new Compass.Common.Models.Entity2();
                }
            }
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
