﻿@using Compass.Components.Account.Pages
@using Microsoft.AspNetCore.Components.Authorization
@inject AuthenticationStateProvider AuthenticationStateProvider

@inherits LayoutComponentBase

<div class="page d-grid" @attributes="@(isUserLoggedIn ? new Dictionary<string, object> { { "data-logged-in", "true" } } : null)">
    <NavMenu />

    <MfaEnforcementWorkflow />
    
    <main class="main-wrapper d-flex">
        <div class="page-content-wrapper d-flex">
            <HeaderComponent />
            <div class="page-content">@Body</div>
        </div>
    </main>
    
    @if (!isUserLoggedIn) 
    {
        <FooterComponent />
    }
</div>


<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">Dismiss</a>
</div>

@code 
{
    private bool isUserLoggedIn = false;

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        isUserLoggedIn = authState.User.Identity?.IsAuthenticated ?? false;
    }
}
