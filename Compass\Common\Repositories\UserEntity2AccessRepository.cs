﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class UserEntity2AccessRepository : IUserEntity2AccessRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public UserEntity2AccessRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<UserEntity2Access?> GetUserEntity2AccessAsync(long? organizationId, long? entity2Id)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (entity2Id is null)
            {
                throw new ArgumentNullException(nameof(entity2Id));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.UserEntity2Accesses.FirstOrDefaultAsync(o => o.OrganizationId == organizationId && o.Entity2Id == entity2Id);
            }
        }

        public async Task<UserEntity2Access?> AddUserEntity2AccessAsync(UserEntity2Access? userEntity2Access)
        {
            if (userEntity2Access is null)
            {
                throw new ArgumentNullException(nameof(userEntity2Access));
            }

            // Get the authentication state 
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            userEntity2Access.ModId = userId;
            userEntity2Access.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.UserEntity2Accesses.AddAsync(userEntity2Access);
                await _dbContext.SaveChangesAsync();
            }

            return userEntity2Access;
        }
    }
}
