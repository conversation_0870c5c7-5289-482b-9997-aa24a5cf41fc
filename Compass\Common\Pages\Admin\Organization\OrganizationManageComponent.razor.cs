﻿using Compass.Common.Pages.Admin.Organization.Manage;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Admin.Organization
{
    public partial class OrganizationManageComponent
    {
        private bool ShowMenu = true;

        private Type? currentTabComponent;

        protected void OnManageHierarchy()
        {
            ShowMenu = false;
            currentTabComponent = typeof(OrganizationHierarchiesComponent);
        }

        protected Dictionary<string, object> GetDynamicParameters()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();

            if (currentTabComponent != null)
            {
                // Add parameters conditionally
                if (currentTabComponent == typeof(OrganizationHierarchiesComponent))
                {
                    parameters[nameof(OrganizationHierarchiesComponent.OnReturn)] = EventCallback.Factory.Create(this, HandleReturn);
                }
            }

            return parameters;
        }

        private void HandleReturn()
        {
            ShowMenu = true;
        }
    }
}
