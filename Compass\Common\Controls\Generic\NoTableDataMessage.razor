@using Compass.Common.DTOs.User
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Pages.Prompts.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<div class="no-data-message-wrapper @MessageStyle text-center font-weight-500">
    @if (!string.IsNullOrEmpty(MessageText))
    {
        @MessageText
    }
    else
    {
        @Localizer["no-data-text"]
    }
</div>
