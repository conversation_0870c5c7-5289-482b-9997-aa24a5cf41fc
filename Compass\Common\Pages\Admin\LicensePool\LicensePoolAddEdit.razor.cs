﻿using Compass.Common.Data;
using Compass.Common.Resources;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;

namespace Compass.Common.Pages.Admin.LicensePool
{
    public partial class LicensePoolAddEdit
    {
        [Parameter]
        public long? LicensePoolId { get; set; }

        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        private long? currentOrganizationId;

        private string successMessage = "Information saved successfully!";
        private bool showSuccessMessage = false;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private bool isCurrentUserSuperAdmin = false;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        private void SetInitialValues(Compass.Common.Models.LicensePool? licensePool)
        {
            if (licensePool != null && isCurrentUserSuperAdmin)
            {
                Input.Product ??= licensePool.Product;
                Input.AccountingType ??= licensePool.AccountingType;
                Input.Name ??= licensePool.Name;
                Input.PurchasedLicenses ??= licensePool.PurchasedLicenses;
                Input.PurchasedArchivedLicenses ??= licensePool.PurchasedArchivedLicenses;
                Input.BeginTs ??= licensePool.BeginTs;
                Input.EndTs ??= licensePool.EndTs;
                Input.Notes ??= licensePool.Notes;
                Input.Status ??= licensePool.Status;
            }
        }

        protected override async Task OnInitializedAsync()
        {
            showSuccessMessage = false;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            isCurrentUserSuperAdmin = await UserAccessor.IsUserInRoleAsync(_currentUserId, UserAccessor.USER_ROLE_SUPER_ADMIN);
            if (commonSessionData != null)
            {
                currentOrganizationId = commonSessionData.CurrentOrganizationId;
            }

            Compass.Common.Models.LicensePool? licensePool;
            if (LicensePoolId.HasValue)
            {
                // Edit mode - load from service
                licensePool = await LicensePoolService.GetLicensePoolAsync(LicensePoolId.Value);
            }
            else
            {
                // Add mode - keep default/new instance
                licensePool = new Compass.Common.Models.LicensePool();
            }

            SetInitialValues(licensePool);
        }

        private Compass.Common.Models.LicensePool GetFormValues()
        {
            Compass.Common.Models.LicensePool licensePool = new Compass.Common.Models.LicensePool();

            if (isCurrentUserSuperAdmin)
            {
                if (LicensePoolId.HasValue)
                {
                    licensePool.Id = (long)LicensePoolId;
                }
                else
                {
                    licensePool.UsedArchivedLicenses = 0;
                    licensePool.UsedLicenses = 0;
                }

                licensePool.Product = Input.Product;
                licensePool.AccountingType = Input.AccountingType;
                licensePool.Name = Input.Name;
                licensePool.PurchasedLicenses = Input.PurchasedLicenses;
                licensePool.PurchasedArchivedLicenses = Input.PurchasedArchivedLicenses;
                licensePool.BeginTs = Input.BeginTs;
                licensePool.EndTs = Input.EndTs;
                licensePool.Notes = Input.Notes;
                licensePool.Status = Input.Status;

                licensePool.OrganizationId = currentOrganizationId;

            }

            return licensePool;
        }

        protected async Task OnValidSubmit()
        {
            if (isCurrentUserSuperAdmin)
            {
                showSuccessMessage = false;
                Compass.Common.Models.LicensePool licensePool = GetFormValues();
                if (LicensePoolId.HasValue)
                {
                    await LicensePoolService.UpdateLicensePoolAsync(LicensePoolId, licensePool);
                }
                else
                {
                    Compass.Common.Models.LicensePool savedLicensePool = await LicensePoolService.AddLicensePoolAsync(licensePool);
                    LicensePoolId = savedLicensePool.Id;
                }
                showSuccessMessage = true;
            }
        }

        protected void OnCancelClick()
        {
            NavigationManager.NavigateTo("/license-pool-list/");
        }

        public sealed class InputModel
        {
            [Required]
            public string? Product { get; set; }
            [CustomValidation(typeof(InputModel), nameof(ValidateAccountingType))]
            public string? AccountingType { get; set; }
            [Required]
            public string? Name { get; set; }
            [Required]
            public int? PurchasedLicenses { get; set; }
            [Required]
            public int? PurchasedArchivedLicenses { get; set; }
            [Required]
            public DateTime? BeginTs { get; set; }
            [Required]
            public DateTime? EndTs { get; set; }
            public string? Notes { get; set; }
            [Required]
            public string? Status { get; set; }

            public static ValidationResult? ValidateAccountingType(string? accountingType, ValidationContext context)
            {
                InputModel? instance = context.ObjectInstance as InputModel;
                if (instance is null)
                {
                    return ValidationResult.Success;
                }

                if (instance.Product == CompassResource.LAP && string.IsNullOrWhiteSpace(accountingType))
                {
                    return new ValidationResult("Accounting Type is required");
                }

                return ValidationResult.Success;
            }
        }
    }
}
