﻿using Microsoft.JSInterop;
using System.Globalization;

namespace Compass.Common.Services
{
    public class CultureService
    {
        private readonly IJSRuntime _js;

        public CultureService(IJSRuntime js)
        {
            _js = js;
        }

        public void SetCulture(string culture)
        {
            var currentCulture = new CultureInfo(culture);
            CultureInfo.CurrentCulture = currentCulture;

            var currentUICulture = new CultureInfo(culture);
            CultureInfo.CurrentUICulture = currentUICulture;

            // Optionally, save culture in local storage to persist across sessions
            //await _js.InvokeVoidAsync("localStorage.setItem", "preferredCulture", culture);
        }

        public async Task InitializeCultureAsync()
        {
            var preferredCulture = await _js.InvokeAsync<string>("localStorage.getItem", "preferredCulture");
            if (!string.IsNullOrEmpty(preferredCulture))
            {
                CultureInfo.CurrentCulture = new CultureInfo(preferredCulture);
                CultureInfo.CurrentUICulture = new CultureInfo(preferredCulture);
            }
        }
    }
}
