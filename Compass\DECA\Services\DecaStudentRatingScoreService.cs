﻿﻿using Compass.DECA.Interfaces.Repositories;
using Compass.DECA.Interfaces.Services;
using Compass.Deca.Models;
using Serilog;

namespace Compass.DECA.Services
{
    public class DecaStudentRatingScoreService : IDecaStudentRatingScoreService
    {
        private readonly IDecaStudentRatingScoreRepository _repository;

        public DecaStudentRatingScoreService(IDecaStudentRatingScoreRepository repository)
        {
            _repository = repository;
        }

        public async Task<DecaStudentRatingScore?> GetByIdAsync(long id)
        {
            try
            {
                return await _repository.GetByIdAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting student rating score {Id}", id);
                throw;
            }
        }

        public async Task<List<DecaStudentRatingScore>> GetByStudentRatingIdAsync(long edecaStudentRatingId)
        {
            try
            {
                return await _repository.GetByStudentRatingIdAsync(edecaStudentRatingId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting student rating scores for student rating {StudentRatingId}", edecaStudentRatingId);
                throw;
            }
        }

        public async Task<List<DecaStudentRatingScore>> GetByStudentIdAsync(long studentId)
        {
            try
            {
                return await _repository.GetByStudentIdAsync(studentId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting student rating scores for student {StudentId}", studentId);
                throw;
            }
        }

        public async Task<DecaStudentRatingScore?> GetByStudentRatingIdAndQuestionNumberAsync(long edecaStudentRatingId, int questionNumber)
        {
            try
            {
                return await _repository.GetByStudentRatingIdAndQuestionNumberAsync(edecaStudentRatingId, questionNumber);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting student rating score for student rating {StudentRatingId} and question {QuestionNumber}", edecaStudentRatingId, questionNumber);
                throw;
            }
        }

        public async Task<DecaStudentRatingScore> CreateAsync(DecaStudentRatingScore ratingScore)
        {
            try
            {
                return await _repository.CreateAsync(ratingScore);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error creating student rating score for student {StudentId}", ratingScore.StudentId);
                throw;
            }
        }

        public async Task<DecaStudentRatingScore?> UpdateAsync(long id, DecaStudentRatingScore ratingScore)
        {
            try
            {
                return await _repository.UpdateAsync(id, ratingScore);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error updating student rating score {Id}", id);
                throw;
            }
        }

        public async Task<bool> DeleteAsync(long id)
        {
            try
            {
                return await _repository.DeleteAsync(id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error deleting student rating score {Id}", id);
                throw;
            }
        }

        public async Task<bool> DeleteByStudentRatingIdAsync(long edecaStudentRatingId)
        {
            try
            {
                return await _repository.DeleteByStudentRatingIdAsync(edecaStudentRatingId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error deleting student rating scores for student rating {StudentRatingId}", edecaStudentRatingId);
                throw;
            }
        }
    }
}
