using Compass.C4L.DTOs;

namespace Compass.C4L.Interfaces.Services
{
    /// <summary>
    /// Interface for the C4L CMS API service
    /// </summary>
    public interface IC4LCmsApiService
    {
        /// <summary>
        /// Gets lesson content from the C4L CMS API
        /// </summary>
        /// <param name="language">The language of the lesson content</param>
        /// <param name="unit">The unit number</param>
        /// <param name="week">The week number</param>
        /// <param name="day">The day number</param>
        /// <param name="lessonTypeSequence">The lesson type sequence</param>
        /// <param name="titleSequence">The title sequence</param>
        /// <returns>A DTO containing the lesson content</returns>
        Task<C4LLessonContentDto> GetC4LLessonContent(string language, int unit, int week, int day, int lessonTypeSequence, int titleSequence);
    }
}
