﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Common.Models
{
    [Table("cmn_student_group_roster")]
    public class StudentGroupRoster
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Column("school_year_id")]
        public long? SchoolYearId { get; set; }

        [Column("school_year")]
        public int? SchoolYear { get; set; }

        [Column("link_status")]
        public string? LinkStatus { get; set; }

        [Column("site_id")]
        public long? SiteId { get; set; }

        [Column("student_group_id")]
        public long? StudentGroupId { get; set; }

        [Column("student_id")]
        public long? StudentId { get; set; }
    }
}
