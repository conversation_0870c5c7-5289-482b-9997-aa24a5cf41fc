﻿using Compass.C4L.DTOs;
using Compass.C4L.Models;
using Compass.Common.Data;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;

namespace Compass.Common.Pages.Admin.StudentGroup
{
    public partial class StudentGroupAddEditComponent : IDisposable
    {
        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        [CascadingParameter]
        public StudentGroupTabs? ParentTabs { get; set; }

        private long? studentGroupId;
        private long? organizationId;
        private long? siteId;
        private long? c4lClassroomId;
        private string studentGroupTypeLabel = string.Empty;

        private string studentGroupName = string.Empty;

        private bool hasC4LAccess = false;
        private bool isC4LOptionLocked = false;

        private string successMessage = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.studentGroupName = commonSessionData.StudentGroupHierarchy;
                this.studentGroupTypeLabel = this.studentGroupName + " Type";

                this.studentGroupId = commonSessionData.CurrentStudentGroupId;
                this.organizationId = commonSessionData.CurrentOrganizationId;
                this.siteId = commonSessionData.CurrentSiteId;

                Compass.Common.Models.StudentGroup? studentGroup;
                if (this.studentGroupId is not null)
                {
                    studentGroup = await StudentGroupService.GetStudentGroupAsync(this.studentGroupId);

                    if (studentGroup is not null)
                    {
                        this.siteId = studentGroup.SiteId;
                    }
                }
                else
                {
                    studentGroup = new Compass.Common.Models.StudentGroup();
                }

                C4LClassroomAccessDto c4lAccessDto = await C4LClassroomService.GetCurrentByStudentGroupIdAsync(this.organizationId, this.studentGroupId);
                hasC4LAccess = c4lAccessDto.HasC4LAccess;

                SetInitialValues(studentGroup, c4lAccessDto);
            }
        }

        private void SetInitialValues(Compass.Common.Models.StudentGroup? studentGroup, C4LClassroomAccessDto c4lAccessDto)
        {
            if (studentGroup == null)
            {
                studentGroup = new Compass.Common.Models.StudentGroup();
            }

            Input.StudentGroupName ??= studentGroup.Name;
            Input.GroupType ??= studentGroup.GroupType;

            C4LClassroom? c4lClassroom = c4lAccessDto.C4LClassroom;
            if (c4lClassroom != null)
            {
                this.c4lClassroomId = c4lClassroom.Id;
                Input.IsC4LClassroom = true;
                Input.StartDate ??= c4lClassroom.StartDate;
                isC4LOptionLocked = true;
            }
            else
            {
                this.c4lClassroomId = null;
                Input.IsC4LClassroom = false;
                Input.StartDate ??= null;
                isC4LOptionLocked = false;
            }
        }

        private Compass.Common.Models.StudentGroup GetStudentGroupInput()
        {
            Compass.Common.Models.StudentGroup studentGroup = new Compass.Common.Models.StudentGroup();

            if (this.studentGroupId is not null)
            {
                studentGroup.Id = (long)this.studentGroupId;
            }

            studentGroup.OrganizationId = this.organizationId;
            studentGroup.SiteId = this.siteId;
            studentGroup.Name = Input.StudentGroupName;
            studentGroup.GroupType = Input.GroupType;

            return studentGroup;
        }

        private C4LClassroom? GetC4LClassroomInput()
        {
            if (hasC4LAccess && Input.IsC4LClassroom)
            {
                C4LClassroom? c4lClassroom = new C4LClassroom();

                if (this.c4lClassroomId is not null)
                {
                    c4lClassroom.Id = (long)this.c4lClassroomId;
                }
                c4lClassroom.OrganizationId = this.organizationId;
                c4lClassroom.StudentGroupId = this.studentGroupId;
                c4lClassroom.StartDate = (DateTime)Input.StartDate;

                return c4lClassroom;
            }
            else
            {
                return null;
            }
        }

        private async Task SaveStudentGroup(Compass.Common.Models.StudentGroup studentGroup)
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                studentGroup.OrganizationId = commonSessionData.CurrentOrganizationId;
                studentGroup.SiteId = commonSessionData.CurrentSiteId;

                long? studentGroupId = commonSessionData.CurrentStudentGroupId;

                Compass.Common.Models.StudentGroup? savedStudentGroup;
                if (studentGroupId == null)
                {
                    savedStudentGroup = await StudentGroupService.CreateStudentGroupAsync(studentGroup);
                }
                else
                {
                    savedStudentGroup = await StudentGroupService.UpdateStudentGroupAsync(studentGroupId, studentGroup);
                }

                if (savedStudentGroup != null)
                {
                    if (savedStudentGroup.Name != null)
                    {
                        commonSessionData.SelectedEntityName = savedStudentGroup.Name;
                    }
                }

                await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                await CommonSessionDataObserver.BroadcastStateChangeAsync();

                this.successMessage = "Information updated successfully!";
            }
        }

        private async Task SaveC4LClassroom(C4LClassroom? c4lClassroom)
        {
            C4LClassroom? savedC4LClassroom;
            if (this.c4lClassroomId is null)
            {
                //SAVE NEW
                C4LClassroomCreateResultDto result = await C4LClassroomService.CreateC4LClassroomAsync(c4lClassroom);
                savedC4LClassroom = result.C4LClassroom;

                if (savedC4LClassroom == null)
                {
                    this.successMessage = result.ResultError;
                }
            }
            else
            {
                //UPDATE
                savedC4LClassroom = await C4LClassroomService.UpdateC4LClassroomAsync(this.c4lClassroomId, c4lClassroom);
            }

            if (savedC4LClassroom != null)
            {
                this.c4lClassroomId = savedC4LClassroom.Id;
                isC4LOptionLocked = true;
            }
        }

        protected async Task SubmitAsync()
        {
            Compass.Common.Models.StudentGroup studentGroup = GetStudentGroupInput();
            C4LClassroom? c4lClassroom = GetC4LClassroomInput();
            this.successMessage = string.Empty;

            await SaveStudentGroup(studentGroup);

            if (c4lClassroom != null)
            {
                await SaveC4LClassroom(c4lClassroom);
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }

        public sealed class InputModel
        {
            [Required]
            [Display(Name = "Student Group Name")]
            public string? StudentGroupName { get; set; }

            [Required]
            [Display(Name = "Group Type")]
            public string? GroupType { get; set; }

            public bool IsC4LClassroom { get; set; }

            [Display(Name = "Start Date")]
            public DateTime? StartDate { get; set; }

            public static ValidationResult? ValidateStartDate(DateTime? StartDate, ValidationContext context)
            {
                InputModel? instance = context.ObjectInstance as InputModel;
                if (instance is null)
                {
                    return ValidationResult.Success;
                }

                if (StartDate == null)
                {
                    return new ValidationResult("Start Date is required");
                }

                return ValidationResult.Success;
            }
        }
    }
}
