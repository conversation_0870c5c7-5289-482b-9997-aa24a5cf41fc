﻿namespace Compass.Common.SessionHandlers
{
    public class CommonSessionData
    {
        public long? CurrentOrganizationId { get; set; }
        public long? CurrentEntity1Id { get; set; }
        public long? CurrentEntity2Id { get; set; }
        public long? CurrentEntity3Id { get; set; }
        public long? CurrentSiteId { get; set; }
        public long? CurrentStudentGroupId { get; set; }
        public long? CurrentStudentId { get; set; }

        public string CurrentOrganizationName { get; set; } = string.Empty;
        public string SelectedEntityName { get; set; } = string.Empty;

        public string Entity1Hierarchy { get; set; } = string.Empty;
        public string Entity2Hierarchy { get; set; } = string.Empty;
        public string Entity3Hierarchy { get; set; } = string.Empty;
        public string SiteHierarchy { get; set; } = string.Empty;
        public string StudentGroupHierarchy { get; set; } = string.Empty;
        public string StudentHierarchy { get; set; } = string.Empty;

        public void ResetCurrentIdValues()
        {
            //Do not reset organizationId
            CurrentEntity1Id = null;
            CurrentEntity2Id = null;
            CurrentEntity3Id = null;
            CurrentSiteId = null;
            CurrentStudentGroupId = null;
            CurrentStudentId = null;
        }

        public void UnSelectOrganization()
        {
            CurrentOrganizationId = null;
            CurrentOrganizationName = string.Empty;
            SelectedEntityName = string.Empty;
            Entity1Hierarchy = string.Empty;
            Entity2Hierarchy = string.Empty;
            Entity3Hierarchy = string.Empty;
            SiteHierarchy = string.Empty;
            StudentGroupHierarchy = string.Empty;
            StudentHierarchy = string.Empty;
        }

    }
}
