﻿@using Compass.Common.Pages.Admin.Organization.Manage
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<section class="manage-organization-component">
    @if (ShowMenu)
    {
        <button type="button" class="c4l-button c4l-ghost-secondary" @onclick="() => OnManageHierarchy()">
            Manage Hierarchy
        </button>
    }
    else
    {
        if (currentTabComponent is not null)
        {
            <div class="component-content-wrapper organization-manage-component-wrapper">
                <DynamicComponent Type="currentTabComponent" Parameters="GetDynamicParameters()" />
            </div>
        }
    }
</section>
