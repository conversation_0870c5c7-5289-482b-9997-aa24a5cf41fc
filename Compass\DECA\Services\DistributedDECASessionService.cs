using Compass.DECA.Interfaces.Services;
using Compass.DECA.Models;
using Compass.Common.Data;
using Compass.Common.Services;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

namespace Compass.DECA.Services
{
    /// <summary>
    /// Distributed session state service for DECA components using Redis cache
    /// </summary>
    public class DistributedDECASessionService : IDECASessionStateService
    {
        private readonly IDistributedCache _cache;
        private readonly UserAccessor _userAccessor;
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(30);
        
        private const string SESSION_KEY_PREFIX = "deca:session:";

        public DistributedDECASessionService(IDistributedCache cache, UserAccessor userAccessor)
        {
            _cache = cache;
            _userAccessor = userAccessor;
        }

        #region Session Context Properties

        public long RatingId
        {
            get => GetSessionContextAsync().Result.RatingId;
            set => UpdateSessionPropertyAsync(ctx => ctx.RatingId = value).Wait();
        }

        public int Checkpoint
        {
            get => GetSessionContextAsync().Result.Checkpoint;
            set => UpdateSessionPropertyAsync(ctx => ctx.Checkpoint = value).Wait();
        }

        #endregion

        #region Batch Operations (Recommended for better performance)

        /// <summary>
        /// Set session context in a single operation
        /// </summary>
        public async Task SetSessionContextAsync(DECASessionContext context)
        {
            string? userId = await GetCurrentUserIdAsync();
            if (userId == null) return;

            string key = SESSION_KEY_PREFIX + userId;
            string serializedData = JsonSerializer.Serialize(context);
            DistributedCacheEntryOptions options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _defaultExpiration
            };

            await _cache.SetStringAsync(key, serializedData, options);
        }

        /// <summary>
        /// Get session context in a single operation
        /// </summary>
        public async Task<DECASessionContext> GetSessionContextAsync()
        {
            string? userId = await GetCurrentUserIdAsync();
            if (userId == null) return new DECASessionContext();

            string key = SESSION_KEY_PREFIX + userId;
            string? serializedData = await _cache.GetStringAsync(key);

            return serializedData == null 
                ? new DECASessionContext() 
                : JsonSerializer.Deserialize<DECASessionContext>(serializedData) ?? new DECASessionContext();
        }

        #endregion

        #region Private Helper Methods

        private async Task<string?> GetCurrentUserIdAsync()
        {
            (ApplicationUser?, string?) result = await _userAccessor.GetUserAndIdAsync();
            return result.Item2;
        }

        private async Task UpdateSessionPropertyAsync(Action<DECASessionContext> updateAction)
        {
            DECASessionContext context = await GetSessionContextAsync();
            updateAction(context);
            await SetSessionContextAsync(context);
        }

        #endregion
    }
}
