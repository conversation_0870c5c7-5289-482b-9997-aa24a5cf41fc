using Compass.C4L.Models;

namespace Compass.C4L.Interfaces.Repositories
{
    public interface IC4LTeacherReflectionRepository
    {
        Task<C4LTeacherReflection?> GetByIdAsync(long id);
        Task<C4LTeacherReflection> CreateAsync(C4LTeacherReflection teacherReflection);
        Task<C4LTeacherReflection?> UpdateAsync(C4LTeacherReflection teacherReflection);
        Task<List<C4LTeacherReflection>> GetByClassroomIdAndLessonIdAsync(long classroomId, int lessonId);
    }
}
