﻿using Compass.Common.DTOs.Student;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IStudentRepository
    {
        public Task<Student> CreateStudentAsync(Student student);
        public Task<Student?> UpdateStudentAsync(long? id, Student student);
        public Task<List<StudentRace>> GetStudentRaces();
        public Task<List<StudentLanguage>> GetStudentLanguages();
        public Task<List<StudentRaceLink>> GetStudentRaceLinkList(long? studentId);
        public Task<StudentRaceLink> CreateStudentRaceLink(StudentRaceLink link);
        public Task DeleteStudentRaceLinks(List<StudentRaceLink> linkList);
        public Task<Student?> GetStudentAsync(long? id);
        public Task<List<StudentDisplayDto>> GetStudentList(StudentListAction action);
        public Task<int> GetStudentCount(long? organizationId);
    }
}
