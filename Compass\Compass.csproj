﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-Compass-1c8f2876-80b6-45c4-9f61-f923b312f38c</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Communication.Email" Version="1.0.1" />
    <PackageReference Include="LanguageExt.Core" Version="4.4.7" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="9.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="9.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.1" />
    <PackageReference Include="Microsoft.Azure.SignalR" Version="1.24.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.1">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="9.0.1" />
    <PackageReference Include="StackExchange.Redis" Version="2.7.27" />
    <PackageReference Include="Microsoft.Identity.Web" Version="3.8.3" />
    <PackageReference Include="Net.Codecrete.QrCodeGenerator" Version="2.0.6" />
    <PackageReference Include="OpenIddict.AspNetCore" Version="6.0.0" />
    <PackageReference Include="OpenIddict.Client" Version="6.0.0" />
    <PackageReference Include="OpenIddict.Server.DataProtection" Version="6.1.0" />
    <PackageReference Include="OpenIddict.Validation.AspNetCore" Version="6.1.0" />
    <PackageReference Include="OpenIddict.Validation.DataProtection" Version="6.1.0" />
    <PackageReference Include="SendGrid" Version="9.29.3" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Enrichers.WithCaller" Version="1.3.0" />
    <PackageReference Include="Serilog.Sinks.AzureBlobStorage" Version="4.0.5" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Components\Pages\Common\" />
  </ItemGroup>

</Project>
