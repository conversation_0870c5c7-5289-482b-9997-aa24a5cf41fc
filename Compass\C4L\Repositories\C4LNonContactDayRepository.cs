using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System.Security.Claims;

namespace Compass.C4L.Repositories
{
    public class C4LNonContactDayRepository : IC4LNonContactDayRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public C4LNonContactDayRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<IEnumerable<C4LNonContactDay>> GetByClassroomIdAsync(long c4l_classroomId)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.C4LNonContactDays
                        .Where(ncd => ncd.C4L_ClassroomId == c4l_classroomId)
                        .OrderBy(ncd => ncd.StartDate)
                        .ToListAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting non-contact days for classroom {ClassroomId}", c4l_classroomId);
                throw;
            }
        }

        public async Task<C4LNonContactDay> CreateAsync(C4LNonContactDay nonContactDay)
        {
            try
            {
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                nonContactDay.ModId = userId;
                nonContactDay.ModTs = DateTime.Now;

                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    await _dbContext.C4LNonContactDays.AddAsync(nonContactDay);
                    await _dbContext.SaveChangesAsync();
                    return nonContactDay;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error creating non-contact day for classroom {ClassroomId}", nonContactDay.C4L_ClassroomId);
                throw;
            }
        }

        public async Task<C4LNonContactDay> UpdateAsync(C4LNonContactDay nonContactDay)
        {
            try
            {
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                nonContactDay.ModId = userId;
                nonContactDay.ModTs = DateTime.Now;

                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    _dbContext.C4LNonContactDays.Update(nonContactDay);
                    await _dbContext.SaveChangesAsync();
                    return nonContactDay;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error updating non-contact day {Id} for classroom {ClassroomId}",
                    nonContactDay.Id, nonContactDay.C4L_ClassroomId);
                throw;
            }
        }

        public async Task DeleteAsync(long id)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    C4LNonContactDay? nonContactDay = await _dbContext.C4LNonContactDays.FindAsync(id);
                    if (nonContactDay != null)
                    {
                        _dbContext.C4LNonContactDays.Remove(nonContactDay);
                        await _dbContext.SaveChangesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error deleting non-contact day {Id}", id);
                throw;
            }
        }

        public async Task<bool> HasOverlappingDates(long c4L_classroomId, DateTime startDate, DateTime endDate, long? excludeId = null)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    IQueryable<C4LNonContactDay> query = _dbContext.C4LNonContactDays
                        .Where(ncd => ncd.C4L_ClassroomId == c4L_classroomId);

                    if (excludeId.HasValue)
                    {
                        query = query.Where(ncd => ncd.Id != excludeId.Value);
                    }

                    return await query.AnyAsync(ncd =>
                        (startDate <= ncd.EndDate && endDate >= ncd.StartDate));
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error checking overlapping dates for classroom {ClassroomId} between {StartDate} and {EndDate}",
                    c4L_classroomId, startDate, endDate);
                throw;
            }
        }

        public async Task<C4LNonContactDay?> GetByIdAsync(long id)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.C4LNonContactDays.FindAsync(id);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting non-contact day {Id}", id);
                throw;
            }
        }

        public async Task<List<C4LNonContactDay>> GetNonContactDaysWithinDateRange(long c4l_classroomId, DateTime startDate, DateTime endDate)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<C4LNonContactDay> ret = await _dbContext.C4LNonContactDays.Where(cd => cd.C4L_ClassroomId == c4l_classroomId
                                                                                        && cd.StartDate > startDate
                                                                                        && cd.EndDate < endDate)
                                                                                .ToListAsync();
                return ret;
            }
        }
    }
}
