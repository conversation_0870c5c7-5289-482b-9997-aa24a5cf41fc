﻿using Compass.Common.Data;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity1
{
    public partial class Entity1EditComponent : IDisposable
    {
        private long? entity1Id;
        private Compass.Common.Models.Entity1? entity1 { get; set; }

        private string? successMessage = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.entity1Id = commonSessionData.CurrentEntity1Id;
                if (this.entity1Id > 0)
                {
                    this.entity1 = await Entity1Repository.GetEntity1Async(this.entity1Id);
                }
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        protected async Task SubmitAsync()
        {
            if (this.entity1 is not null)
            {
                this.successMessage = string.Empty;

                CommonSessionData? commonSessionData = await GetCommonSessionData();
                if (commonSessionData is not null)
                {
                    this.entity1.OrganizationId = commonSessionData.CurrentOrganizationId;

                    this.entity1 = await Entity1Repository.UpdateEntity1Async(this.entity1Id, this.entity1);

                    if (this.entity1 != null)
                    {
                        commonSessionData.SelectedEntityName = this.entity1.Name;
                    }

                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();
                }
                successMessage = "Information saved successfully!";
            }
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
