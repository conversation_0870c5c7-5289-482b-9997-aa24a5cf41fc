﻿namespace Compass.LAP.Resources.Instruments
{
    public class LapInstrument
    {
        private int? id;

        public LapInstrument(int _level)
        {
            id = _level;
        }

        public int? GetId()
        {
            return id;
        }

        public void SetId(int _id)
        {
            id = _id;
        }

        public static string GetName(int _level)
        {
            return AssessmentLevel.GetName(_level);
        }
    }
}
