.page {
    position: relative;
    grid-template-columns: auto;
    grid-template-rows: auto;
    height: 100vh;
    align-content: space-between;

    &[data-logged-in] {
        grid-template-columns: 1fr;
        grid-template-rows: 56px 1fr;
        align-content: normal;
        height: auto;
    }
}

.main-wrapper {
    flex-direction: column;
    justify-content: space-between;
    flex: 1;
    overflow: auto;
}

.page-content-wrapper {
    flex-direction: column;
}

.top-row {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: space-between;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

.top-row ::deep a, .top-row ::deep .btn-link {
    white-space: nowrap;
    margin-left: 0;
    text-decoration: none;
}

.top-row ::deep a:hover, .top-row ::deep .btn-link:hover {
    text-decoration: underline;
}

.top-row ::deep a:first-child {
    overflow: hidden;
    text-overflow: ellipsis;
}

#blazor-error-ui {
    font-weight: 500;
    display: none;
    background: hsl(42 100% 92.5%);
    color: hsl(33 100% 20%);
    position: fixed;
    bottom: 0;
    left: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    width: 100%;
    z-index: 1000;
    text-align: center;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    margin-inline-start: 0.125rem;
}

@media (min-width: 64rem) {
    .page {
        --_sidebar-desktop-width: 285px;
        
        &[data-logged-in] {
            grid-template-columns: var(--_sidebar-desktop-width) 1fr;
            grid-template-rows: auto;

            & .main-wrapper {
                height: 100vh;
            }
        }
    }

    .top-row {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row.auth ::deep a:first-child {
        flex: 1;
        text-align: right;
        width: 0;
    }

    .top-row ::deep a,
    .top-row ::deep .btn-link {
        margin-left: 1.5rem;
    }

    .top-row, article {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
