﻿using Compass.Common.Data;

namespace Compass.LAP.Resources.SmartAssessment.Primary
{
    public class Skill
    {
        public const int EMERGING = -1;
        public const int MASTERED = 1;

        public const int SELDOM = 1;
        public const int SOMETIMES = 2;
        public const int ALWAYS = 3;

        public DateTime? Date { get; set; }
        public ApplicationUser? Observer { get; set; }
        public int? SchoolYear { get; set; }
        public Milestone? Milestone { get; set; }
        public string? Notes { get; set; }
        public string? Selections { get; set; }
        public int? Type { get; set; }
        public Checkpoint? Checkpoint { get; set; }
    }
}
