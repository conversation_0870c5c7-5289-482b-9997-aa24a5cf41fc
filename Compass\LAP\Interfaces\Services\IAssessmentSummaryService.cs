using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Compass.LAP.DTOs;

namespace Compass.LAP.Interfaces.Services
{
    public interface IAssessmentSummaryService
    {
        Task<(List<LapChildRowContainer> Rows, SubscaleContainer Subscales, int CurrentCheckpoint)> GetAssessmentSummaryAsync(
            long classroomId, 
            int checkpoint, 
            int language, 
            HashSet<int> registeredInstruments, 
            List<int> hiddenInstruments);
            
        Task<List<LapChildRowContainer>> GetChildAssessmentsSummaryAsync(
            long childId, 
            HashSet<int> registeredInstruments, 
            SubscaleContainer subscales, 
            long classroomId);
            
        Task<bool> DeleteAssessmentsAsync(List<string> assessmentIds);
        
        Task<List<int>> SaveHiddenInstrumentsAsync(List<int> hiddenInstruments, bool showAll);
    }
}
