﻿using Compass.Common.Data;
using Compass.Common.Pages.Admin.Site;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity3
{
    public partial class Entity3Tabs : IDisposable
    {
        private static readonly int SUMMARY_INDEX = 1;
        private static readonly int MANAGE_INDEX = 2;
        private static readonly int ADD_CHILD_INDEX = 3;
        private static readonly int EDIT_INDEX = 4;
        private static readonly int SUPPORT_INDEX = 5;
        private static readonly int REPORT_INDEX = 6;
        private static readonly int USER_INDEX = 7;

        private int currentTab = SUMMARY_INDEX;
        private Type? currentTabComponent;

        private string currentEntity3Name = string.Empty;
        private string siteHierarchy = string.Empty;

        private readonly Dictionary<int, Type> tabComponents = new()
    {
        { SUMMARY_INDEX, typeof(Entity3SummaryComponent) },
        { MANAGE_INDEX, typeof(Entity3ManageComponent) },
        { ADD_CHILD_INDEX, typeof(SiteAddComponent) },
        { EDIT_INDEX, typeof(Entity3EditComponent) },
        { SUPPORT_INDEX, typeof(Entity3SummaryComponent) },
        { REPORT_INDEX, typeof(Entity3ReportsComponent) },
        { USER_INDEX, typeof(Entity3Users) }
    };

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();

            if (commonSessionData is not null)
            {
                currentEntity3Name = commonSessionData.SelectedEntityName;
                currentTab = SUMMARY_INDEX;
                // Initialize with the first tab's component
                currentTabComponent = tabComponents[currentTab];

                CommonSessionDataObserver.AddStateChangeAsyncListeners(UpdateEntity3Name);

                siteHierarchy = commonSessionData.SiteHierarchy;
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task UpdateEntity3Name()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.currentEntity3Name = commonSessionData.SelectedEntityName;
                StateHasChanged();
            }
        }

        protected void ChangeTab(int tabIndex)
        {
            currentTab = tabIndex;
            currentTabComponent = tabComponents[currentTab];
        }

        public void Dispose()
        {
            CommonSessionDataObserver.RemoveStateChangeAsyncListeners(UpdateEntity3Name);
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
