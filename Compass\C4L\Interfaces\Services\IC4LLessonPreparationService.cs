using Compass.C4L.DTOs;
using Compass.C4L.Models;

namespace Compass.C4L.Interfaces.Services
{
    public interface IC4LLessonPreparationService
    {
        Task<List<C4LLessonPreparation>> GetPreparationsByLessonAsync(string language, int unit, int week, int day);
        Task<C4LLessonPreparation?> GetPreparationAsync(string language, int unit, int week, int day, int lessonTypeSequence, int titleSequence);
        Task<List<LessonPreparationWithTitleDto>> RetrievePreparationsWithTitle(string language, int unit, int week, long? classroomId = null);
    }
}
