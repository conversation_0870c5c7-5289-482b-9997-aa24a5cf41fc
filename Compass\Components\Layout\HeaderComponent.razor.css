.header {
  padding-block: 1rem;
  padding-inline: clamp(1.5rem, 1.211rem + 1.233vw, 2rem);
}

.header-content-wrapper {
  width: min(100%, 1600px);
  margin-inline: auto;
  align-items: center;
  justify-content: space-between;
}

.header-buttons-wrapper {
  gap: 0.75rem;
  align-items: center;
}

.header-user-links-wrapper {
  display: none;
}

.language-switcher-button {
  &:hover {
    background-color: hsl(278 37% 90%);
    color: var(--c4l-primary-purple);
  }
}

.header-user-info-wrapper {
  gap: 0.75rem;
  align-items: center;
  color: var(--c4l-primary-purple);
  display: none;
}

.header-user-avatar {
  width: 2rem;
  aspect-ratio: 1;
  border-radius: 50%;
  background-color: var(--c4l-secondary-teal);
  display: flex;
  justify-content: center;
  align-items: center;
}

@media (min-width: 64rem) {
  .header {
    position: sticky;
    top: 0;
    z-index: 99;
    background-color: var(--white);
    padding-inline: 2rem;
    padding-block: 1.3125rem;
    height: 5.3125rem;
    border-block-end: 1px solid var(--Colors-Border-Base-standard, #B3BBC2);

    &.no-login {
      background-color: var(--c4l-primary-purple);
    }
  }

  .header-user-info-wrapper {
    display: flex;
  }

  .header-user-avatar {
    width: 2.75rem;
  }
}
