using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Compass.Common.Repositories;
using Serilog;

namespace Compass.C4L.Services
{
    public class C4LLessonPreparationCompletedService : IC4LLessonPreparationCompletedService
    {
        private readonly IC4LLessonPreparationCompletedRepository _repository;
        private readonly IC4LClassroomRepository _classroomRepository;
        private readonly IStudentGroupRepository _studentGroupRepository;
        private readonly ISchoolYearRepository _schoolYearRepository;

        public C4LLessonPreparationCompletedService(
            IC4LLessonPreparationCompletedRepository repository,
            IC4LClassroomRepository classroomRepository,
            IStudentGroupRepository studentGroupRepository,
            ISchoolYearRepository schoolYearRepository)
        {
            _repository = repository;
            _classroomRepository = classroomRepository;
            _studentGroupRepository = studentGroupRepository;
            _schoolYearRepository = schoolYearRepository;
        }

        private async Task<int> GetCurrentSchoolYear(long c4l_classroomId )
        {
            // Get the classroom to find the student group
            C4LClassroom? classroom = await _classroomRepository.GetC4LClassroomByIdAsync(c4l_classroomId);
            if (classroom == null)
            {
                throw new InvalidOperationException($"Classroom with ID {c4l_classroomId} not found");
            }

            // Get the student group to find the site
            StudentGroup? studentGroup = await _studentGroupRepository.GetStudentGroupAsync(classroom.StudentGroupId);
            if (studentGroup == null)
            {
                throw new InvalidOperationException($"Student group with ID {classroom.StudentGroupId} not found");
            }

            // Get the current school year for the site
            int? schoolYear = await GetCurrentSchoolYearForSiteAsync(studentGroup.SiteId, studentGroup.OrganizationId);
            if (!schoolYear.HasValue)
            {
                throw new InvalidOperationException($"No current school year found for site {studentGroup.SiteId}");
            }

            return schoolYear.Value;
        }

        public async Task<C4LLessonPreparationCompleted?> GetByPreparationIdAndSchoolYearAsync(long c4l_classroomId, long preparationId, int? schoolYear)
        {
            try
            {
                if ( schoolYear.HasValue )
                {
                    return await _repository.GetByPreparationIdAndSchoolYearAsync(preparationId, schoolYear.Value);
                }
                else
                {
                    int sy = await GetCurrentSchoolYear(c4l_classroomId);
                    return await _repository.GetByPreparationIdAndSchoolYearAsync(preparationId, sy);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LLessonPreparationCompletedService.GetByPreparationIdAndSchoolYearAsync for PreparationId {PreparationId}, SchoolYear {SchoolYear}", preparationId, schoolYear);
                throw;
            }
        }

        public async Task<C4LLessonPreparationCompleted> SaveCompletionStatusAsync(long preparationId, long organizationId, long? c4l_classroomId, bool isCompleted)
        {
            try
            {
                if (!c4l_classroomId.HasValue)
                {
                    throw new ArgumentNullException(nameof(c4l_classroomId), "Classroom ID is required");
                }

                // Get the classroom to find the student group
                C4LClassroom? classroom = await _classroomRepository.GetC4LClassroomByIdAsync(c4l_classroomId.Value);
                if (classroom == null)
                {
                    throw new InvalidOperationException($"Classroom with ID {c4l_classroomId.Value} not found");
                }

                // Get the student group to find the site
                StudentGroup? studentGroup = await _studentGroupRepository.GetStudentGroupAsync(classroom.StudentGroupId);
                if (studentGroup == null)
                {
                    throw new InvalidOperationException($"Student group with ID {classroom.StudentGroupId} not found");
                }

                // Get the current school year for the site
                int? schoolYear = await GetCurrentSchoolYearForSiteAsync(studentGroup.SiteId, organizationId);
                if (!schoolYear.HasValue)
                {
                    throw new InvalidOperationException($"No current school year found for site {studentGroup.SiteId}");
                }

                // Check if a record already exists
                C4LLessonPreparationCompleted? existingRecord = await _repository.GetByPreparationIdAndSchoolYearAsync(preparationId, schoolYear.Value);

                if (existingRecord == null)
                {
                    // Create a new record
                    C4LLessonPreparationCompleted newRecord = new C4LLessonPreparationCompleted
                    {
                        OrganizationId = organizationId,
                        LessonPreparationId = preparationId,
                        Schoolyear = schoolYear.Value,
                        DateCompleted = isCompleted ? DateTime.Now : null
                        // ModId and ModTs will be set in the repository
                    };

                    return await _repository.CreateAsync(newRecord);
                }
                else
                {
                    // Update the existing record
                    existingRecord.OrganizationId = organizationId; // Ensure organization ID is set
                    existingRecord.DateCompleted = isCompleted ? DateTime.Now : null;
                    return await _repository.UpdateAsync(existingRecord);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LLessonPreparationCompletedService.SaveCompletionStatusAsync for PreparationId {PreparationId}, C4L_ClassroomId {ClassroomId}", preparationId, c4l_classroomId);
                throw;
            }
        }

        public async Task<int?> GetCurrentSchoolYearForSiteAsync(long? siteId, long? organizationId)
        {
            try
            {
                return await _schoolYearRepository.GetCurrentSchoolYearForSiteAsync(siteId, organizationId);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error in C4LLessonPreparationCompletedService.GetCurrentSchoolYearForSiteAsync for SiteId {SiteId}, OrganizationId {OrganizationId}", siteId, organizationId);
                throw;
            }
        }
    }
}
