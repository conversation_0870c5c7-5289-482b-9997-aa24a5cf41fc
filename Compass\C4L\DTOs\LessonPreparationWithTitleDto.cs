namespace Compass.C4L.DTOs
{
    public class LessonPreparationWithTitleDto
    {
        public long Id { get; set; }
        public int LessonId { get; set; }
        public string Language { get; set; } = "English";
        public int Unit { get; set; }
        public int Week { get; set; }
        public int Day { get; set; }
        public int LessonTypeSequence { get; set; }
        public int TitleSequence { get; set; }
        public string PreparationTasks { get; set; }
        public string Title { get; set; }
        public string LessonType { get; set; }

        // Properties for completion status
        public long? CompletedId { get; set; }
        public DateTime? DateCompleted { get; set; }
        public bool IsCompleted => DateCompleted.HasValue;
    }
}
