﻿@using Compass.Common.Data
@using Compass.Common.DTOs.Student
@using Compass.Common.Interfaces.Services
@using Compass.Common.Services
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Compass.Common.Resources
@using Microsoft.Extensions.Localization
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserSessionService UserSessionService
@inject NavigationManager NavigationManager
@inject UserAccessor UserAccessor
@inject IStudentGroupService StudentGroupService;
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

@if (showRoster)
{
    <h2>@studentGroupHierarchyName Roster</h2>

    <div class="c4l-search-table-wrapper" style="margin-block-start: 1.75rem;">
        <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

        <div class="c4l-table-scroll-wrapper">
            <div class="c4l-table-wrapper has-links users-wrapper">
                <div class="c4l-table-headings-wrapper">
                    <h6 class="c4l-table-heading">First Name</h6>
                    <h6 class="c4l-table-heading">Last Name</h6>
                    <h6 class="c4l-table-heading">Birthdate</h6>
                    <h6 class="c4l-table-heading">School ID</h6>
                </div>

                @foreach (StudentDisplayDto student in studentResults)
                {
                    <button type="button" title="@($"Edit {student.FirstName} {student.LastName}")" @onclick="() => OnStudentClick(student)">
                        <div class="c4l-table-result-wrapper">
                            <p class="c4l-table-result-item">@student.FirstName</p>
                            <p class="c4l-table-result-item">@student.LastName</p>
                            <p class="c4l-table-result-item">@(student.BirthDate?.ToString("d"))</p>
                            <p class="c4l-table-result-item">@student.SchoolId</p>
                        </div>
                    </button>
                }
            </div>
        </div>
    </div>

    <div class="c4l-pagination-wrapper">
        <div class="c4l-pagination-buttons-wrapper">
            <div class="buttons-wrapper">
                <button class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnPreviousClicked()" 
                        disabled="@(currentPage <= 1)">
                    @Localizer["lbl_Previous"]
                </button>

                <button class="c4l-button c4l-ghost-primary c4l-pagination-button"
                        @onclick="() => OnNextClicked()"
                        disabled="@(currentPage >= maxPages)">
                    @Localizer["lbl_Next"]
                </button>
            </div>

            <div class="add-student-button-wrapper">
                <button class="c4l-button c4l-secondary-button" type="button" title="Add Student" @onclick="OnAddStudentClick">Add Student</button>
            </div>
        </div>

        <div class="page-count-wrapper font-weight-500">
            <span class="current-page-number">@currentPage</span> @Localizer["lbl_of"] @maxPages
        </div>
    </div>    
}
else
{
    if (currentStudentComponent is not null)
    {
        <DynamicComponent Type="currentStudentComponent" Parameters="GetDynamicParameters()" />
    }
}
