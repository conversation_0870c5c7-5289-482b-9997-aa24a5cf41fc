﻿using Compass.C4L.DTOs;
using Compass.C4L.Interfaces.Services;
using Compass.Common.Data;
using Compass.Common.Interfaces.Services;
using Compass.Common.Resources;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Admin.StudentGroup
{
    public partial class StudentGroupSummaryComponent : IDisposable
    {
        [Inject]
        public required NavigationManager NavigationManager { get; set; }
        [Inject]
        public required IC4LClassroomService C4LClassroomService { get; set; }
        [Inject]
        public required ILicensePoolService LicensePoolService { get; set; }

        private bool hasLAPLicense = false;
        private bool hasDECALicense = false;
        private bool hasC4LLicense = false;

        private string siteHierarchy = string.Empty;
        private bool isLoading = true;

        private long? studentGroupId;
        private long? organizationId;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        private async Task CheckLicenseProducts()
        {
            hasLAPLicense = await LicensePoolService.CheckActiveLicense(this.organizationId, CompassResource.LAP);

            hasDECALicense = await LicensePoolService.CheckActiveLicense(this.organizationId, CompassResource.DECA);

            C4LClassroomAccessDto c4lAccessDto = await C4LClassroomService.GetCurrentByStudentGroupIdAsync(this.organizationId, this.studentGroupId);
            hasC4LLicense = c4lAccessDto.HasC4LAccess && c4lAccessDto.C4LClassroom != null;

        }

        protected override async Task OnInitializedAsync()
        {
            this.isLoading = true;
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);
            CommonSessionData? commonSessionData = await GetCommonSessionData();

            if (commonSessionData is not null)
            {
                this.siteHierarchy = commonSessionData.SiteHierarchy;
                this.studentGroupId = commonSessionData.CurrentStudentGroupId;
                this.organizationId = commonSessionData.CurrentOrganizationId;
                this.isLoading = false;
            }

            await CheckLicenseProducts();
        }

        private void OnLAPOptionClick()
        {
            NavigationManager.NavigateTo($"/lap-student-group-summary");
        }

        private void OnDECAOptionClick()
        {
            NavigationManager.NavigateTo($"/deca-student-group-summary");
        }

        private void OnC4LOptionClick()
        {
            NavigationManager.NavigateTo($"/c4l-student-group-summary");
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
