﻿﻿using Compass.Common.Data;
using Compass.DECA.Interfaces.Repositories;
using Compass.Deca.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;

namespace Compass.DECA.Repositories
{
    public class DecaQuestionRepository : IDecaQuestionRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public DecaQuestionRepository(
            IDbContextFactory<ApplicationDbContext> contextFactory,
            AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<DecaQuestion?> GetByIdAsync(long id)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<DecaQuestion>().FindAsync(id);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting question by ID: {ex.Message}");
                throw;
            }
        }

        public async Task<List<DecaQuestion>> GetByRecordFormAsync(string recordForm)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<DecaQuestion>()
                        .Where(q => q.RecordForm == recordForm)
                        .OrderBy(q => q.QuestionNumber)
                        .ToListAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting questions by record form: {ex.Message}");
                throw;
            }
        }

        public async Task<List<DecaQuestion>> GetAllAsync()
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<DecaQuestion>()
                        .OrderBy(q => q.RecordForm)
                        .ThenBy(q => q.QuestionNumber)
                        .ToListAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting all questions: {ex.Message}");
                throw;
            }
        }

        public async Task<DecaQuestion?> GetByRecordFormAndQuestionNumberAsync(string recordForm, int questionNumber)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<DecaQuestion>()
                        .Where(q => q.RecordForm == recordForm && q.QuestionNumber == questionNumber)
                        .FirstOrDefaultAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting question by record form and question number: {ex.Message}");
                throw;
            }
        }

        public async Task<List<string>> GetDistinctRecordFormsAsync()
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<DecaQuestion>()
                        .Where(q => q.RecordForm != null)
                        .Select(q => q.RecordForm!)
                        .Distinct()
                        .OrderBy(rf => rf)
                        .ToListAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting distinct record forms: {ex.Message}");
                throw;
            }
        }

        public async Task<List<DecaQuestion>> GetByProtectiveFactorScaleAsync(string protectiveFactorScale)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<DecaQuestion>()
                        .Where(q => q.ProtectiveFactorScale == protectiveFactorScale)
                        .OrderBy(q => q.RecordForm)
                        .ThenBy(q => q.QuestionNumber)
                        .ToListAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting questions by protective factor scale: {ex.Message}");
                throw;
            }
        }
    }
}
