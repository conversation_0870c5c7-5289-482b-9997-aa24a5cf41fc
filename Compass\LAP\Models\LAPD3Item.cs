using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_lapd3_items")]
    public class LAPD3Item
    {
        [Key]
        [Column("InstID")]
        public long Id { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Required]
        [Column("CustomerInstID")]
        public long OrganizationId { get; set; }

        [Required]
        [Column("ChildInstID")]
        public long StudentId { get; set; }

        [Required]
        [Column("AssessmentInstID")]
        public long AssessmentId { get; set; }

        [Required]
        [Column("SubscaleInstID")]
        public long SubscaleId { get; set; }

        [Column("ObserverInstID")]
        public string? ObserverId { get; set; }

        [Required]
        [Column("SubscaleStaticID")]
        public long SubscaleStaticId { get; set; }

        [Required]
        [Column("ItemStaticID")]
        public long ItemStaticId { get; set; }

        [Column("Value")]
        public int? Value { get; set; }

        [Column("SelectedItems")]
        [StringLength(255)]
        public string? SelectedItems { get; set; }

        [Column("UserComment")]
        [StringLength(1000)]
        public string? UserComment { get; set; }

        [Required]
        [Column("Sequence")]
        public short Sequence { get; set; }
    }
}