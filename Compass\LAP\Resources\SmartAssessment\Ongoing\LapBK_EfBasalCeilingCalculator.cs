using Compass.LAP.Resources.SmartAssessment.Criterion;
using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Resources.SmartAssessment.Ongoing
{
    public class LapBK_EfBasalCeilingCalculator : BasalCeilingCalculator
    {

        public LapBK_EfBasalCeilingCalculator(List<AssessmentItem> assessmentItems)
            : base(0, assessmentItems, 0)
        {
        }

        public override int? Basal => null;

        public override int? Ceiling => null;

        public override int? RawScore => rawscore;

        public override void FindBasal()
        {
            FindRawScore();
        }

        public override void FindCeiling()
        {
            // Empty implementation as in Java
        }

        public override bool IsBasalFound()
        {
            return items.Count == LapBK_Assessment.ITEMS_IN_EF;
        }

        public override bool IsCeilingFound()
        {
            return IsBasalFound();
        }

        public override bool IsRawScoreFound()
        {
            return rawscore != 0;
        }

        public override void FindRawScore()
        {
            rawscore = 0;
            if (!IsBasalFound())
            {
                return;
            }

            foreach (AssessmentItem item in items)
            {
                if (item.Value != null)
                {
                    rawscore += item.Value.Value;
                }
            }
        }
    }
}
