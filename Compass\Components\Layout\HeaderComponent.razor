﻿@using System.Globalization
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization
@inject CultureService CultureService
@inject CurrentCultureObserver CurrentLanguageObserver
@inject IStringLocalizer<CommonResource> Localizer
@inject UserSessionService UserSessionService
@inject UserAccessor UserAccessor
@inject CommonSessionDataObserver CommonSessionDataObserver;
@inject AuthenticationStateProvider AuthenticationStateProvider

@rendermode @(new InteractiveServerRenderMode(prerender: false))

<header class="header top-row @(!isUserLoggedIn ? "no-login" : "")">
    <div class="header-content-wrapper d-flex">
        <div class="header-buttons-wrapper d-flex">
            <button class="c4l-button c4l-ghost-primary language-switcher-button" @onclick="SetSpanishCulture">@switchLanguageText</button>
        </div>

        @if (authResolved && !isUserLoggedIn)
        {
            <a class="c4l-button c4l-secondary-button" href="/login">Login</a>
        }

        <AuthorizeView>
            <Authorized>
                @if (user is not null)
                {
                    <div class="header-user-info-wrapper" data-color="black">
                        @if(!string.IsNullOrEmpty(UserFirstLetterAvatar)) 
                        {
                            <span class="header-user-avatar" data-color="white">@UserFirstLetterAvatar</span>
                        }
                        <p class="header-user-name my-0 font-weight-600">@user.Identity.Name</p>
                    </div>
                }
            </Authorized>
            
            <NotAuthorized>
                <div class="header-user-links-wrapper">
                    <a class="c4l-button c4l-secondary-button header-user-link d-flex" href="/login">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="var(--white)" width="20" height="20" viewBox="0 0 640 512">
                            <path
                                d="M256 48l0 16c0 17.7-14.3 32-32 32l-64 0c-17.7 0-32-14.3-32-32l0-16L64 48c-8.8 0-16 7.2-16 16l0 384c0 8.8 7.2 16 16 16l256 0c8.8 0 16-7.2 16-16l0-384c0-8.8-7.2-16-16-16l-64 0zM0 64C0 28.7 28.7 0 64 0L320 0c35.3 0 64 28.7 64 64l0 384c0 35.3-28.7 64-64 64L64 512c-35.3 0-64-28.7-64-64L0 64zM160 320l64 0c44.2 0 80 35.8 80 80c0 8.8-7.2 16-16 16L96 416c-8.8 0-16-7.2-16-16c0-44.2 35.8-80 80-80zm-32-96a64 64 0 1 1 128 0 64 64 0 1 1 -128 0z" />
                        </svg>
                        Login
                    </a>
                </div>
            </NotAuthorized>
        </AuthorizeView>
    </div>
</header>
