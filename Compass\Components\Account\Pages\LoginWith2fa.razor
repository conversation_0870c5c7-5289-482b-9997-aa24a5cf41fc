﻿@page "/account/login-with-2fa"

@using System.ComponentModel.DataAnnotations
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity

@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager
@inject IdentityRedirectManager RedirectManager
@inject ILogger<LoginWith2fa> Logger

<PageTitle>Two-Factor Authentication | C4L</PageTitle>

<h1 class="page-title">Two-factor authentication</h1>

<StatusMessage Message="@message" AlertType="@alertType" />

<h2 class="text-center my-5">Your login is protected with an authenticator app. Enter your authenticator code below.</h2>

<EditForm Model="Input" FormName="login-with-2fa" OnValidSubmit="OnValidSubmitAsync" method="post" class="c4l-form c4l-2fa-login-form">
    <input type="hidden" name="ReturnUrl" value="@ReturnUrl" />
    <input type="hidden" name="RememberMe" value="@RememberMe" />
    <DataAnnotationsValidator />
    <ValidationSummary class="text-danger" role="alert" />

    <h3 class="c4l-form-heading">Enter two-factor authentication code</h3>

    <div class="form-floating mb-3">
        <InputText @bind-Value="Input.TwoFactorCode" class="form-control" autocomplete="off" />
        <label for="two-factor-code" class="form-label">Authenticator code</label>
        <ValidationMessage For="() => Input.TwoFactorCode" class="text-danger" />
    </div>

    <div class="checkbox">
        <label for="remember-machine" class="form-label form-checkbox-label">
            <InputCheckbox @bind-Value="Input.RememberMachine" class="form-check-input" />
            Remember this machine
        </label>
    </div>
    <div>
        <button type="submit" class="c4l-button c4l-form-button c4l-primary-button">Log in</button>
    </div>
</EditForm>


<p class="text-center my-4">Don't have access to your authenticator device? You can <a href="account/login-with-recovery-code?ReturnUrl=@ReturnUrl">log in with a recovery code</a>.</p>

@code {
    private string? message;
    private string? alertType = "danger";
    private ApplicationUser user = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    [SupplyParameterFromQuery]
    private bool RememberMe { get; set; }

    protected override async Task OnInitializedAsync()
    {
        // Ensure the user has gone through the username & password screen first
        user = await SignInManager.GetTwoFactorAuthenticationUserAsync() ??
            throw new InvalidOperationException("Unable to load two-factor authentication user.");
    }

    private async Task OnValidSubmitAsync()
    {
        string authenticatorCode = Input.TwoFactorCode!.Replace(" ", string.Empty).Replace("-", string.Empty);
        Microsoft.AspNetCore.Identity.SignInResult result = await SignInManager.TwoFactorAuthenticatorSignInAsync(authenticatorCode, RememberMe, Input.RememberMachine);
        string userId = await UserManager.GetUserIdAsync(user);

        if (result.Succeeded)
        {
            Logger.LogInformation("User with ID '{UserId}' logged in with 2fa.", userId);
            RedirectManager.RedirectTo(ReturnUrl);
            alertType = "success";
        }
        else if (result.IsLockedOut)
        {
            Logger.LogWarning("User with ID '{UserId}' account locked out.", userId);
            RedirectManager.RedirectTo("account/lockout");
        }
        else
        {
            Logger.LogWarning("Invalid authenticator code entered for user with ID '{UserId}'.", userId);
            message = "Error: Invalid authenticator code.";
        }
    }

    private sealed class InputModel
    {
        [Required]
        [StringLength(7, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Text)]
        [Display(Name = "Authenticator code")]
        public string? TwoFactorCode { get; set; }

        [Display(Name = "Remember this machine")]
        public bool RememberMachine { get; set; }
    }
}
