﻿@page "/c4l-lessons"

@using Compass.C4L.Interfaces.Services
@using Compass.C4L.Models
@using Compass.C4L.Prompts
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@inject UserAccessor UserAccessor
@inject UserSessionService UserSessionService
@inject IStudentGroupRepository StudentGroupRepository;
@inject IC4LClassroomService C4LClassroomService
@inject IC4LSessionStateService SessionStateService

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="lessons-calendar">
    <div class="calendar-header">
        <div class="week-navigation">
            <button class="btn btn-link" type="button" @onclick="NavigateToPreviousWeek">
                Previous Week
            </button>
            <h2>@CurrentUnitWeeks</h2>
            <button class="btn btn-link" type="button" @onclick="NavigateToNextWeek">
                Next Week
            </button>
        </div>
        <div class="button-group">
            <button class="btn btn-primary prep-button" @onclick="() => NavigateToPreparations(MondayUnit, MondayWeek)">PREP for Week</button>
            <button class="btn btn-primary prep-button" @onclick="NavigateToToday">Today</button>
            <button class="btn btn-primary prep-button" @onclick="ToggleDatePicker">Jump To Date</button>
            @if (showDatePicker)
            {
                <div class="date-picker-container">
                    <input type="date" @bind="selectedJumpDate" @bind:event="onchange" class="form-control date-input" />
                    <button class="btn btn-primary btn-sm mt-2" @onclick="OnDateSelected">Go</button>
                </div>
            }
        </div>
    </div>

    <div class="calendar-grid">
        @for (int day = 0; day < 5; day++)
        {
            DateTime date = CurrentMondayDate.AddDays(day);

            <div class="calendar-column">
                <div class="column-header">
                    <div class="date">@date.ToString("dddd, MMM dd")</div>
                    <div class="week-day">
                        @if (day < lessonHeaderList.Count && lessonHeaderList[day].Description == string.Empty)
                        {
                            LessonHeader combo = lessonHeaderList[day];
                            if (combo.Unit == 0)
                            {
                                <div>&nbsp;</div>
                                <div>&nbsp;</div>
                            }
                            else
                            {
                                <div>Unit @combo.Unit</div>
                                <div>Week @combo.Week: Day @combo.Day</div>
                            }
                        }
                        else if (day < lessonHeaderList.Count && lessonHeaderList[day].Description != string.Empty)
                        {
                            LessonHeader combo = lessonHeaderList[day];
                            <div>@combo.Description</div>
                        }
                        else
                        {
                            <div>&nbsp;</div>
                            <div>&nbsp;</div>
                        }
                    </div>
                </div>
            </div>
        }

        @for (int rowIndex = 0; rowIndex < maxLessons; rowIndex++)
        {
            <div class="lesson-row">
                @for (int day = 0; day < 5; day++)
                {
                    List<C4LLesson> dayLessons = lessonsByDay[day];
                    C4LLesson? lesson = rowIndex < dayLessons.Count ? dayLessons[rowIndex] : null;

                    <div class="lesson-cell">
                        @if (lesson != null)
                        {
                            <div class="lesson-card">
                                <div class="lesson-type">@lesson.LessonType</div>
                                <div class="lesson-title clickable" @onclick="() => NavigateToLessonDetails(lesson)">@lesson.Title</div>
                            </div>
                        }
                        else
                        {
                            <div class="lesson-card empty">
                                <div class="lesson-placeholder"></div>
                            </div>
                        }
                    </div>
                }
            </div>
        }

        @for (int day = 0; day < 5; day++)
        {
            DateTime date = CurrentMondayDate.AddDays(day);
            <button type="button" class="c4l-button c4l-secondary-button c4l-pagination-button" @onclick="() => OnRescheduleClick(date)">Reschedule</button>
        }
    </div>
</div>

<ReschedulePrompt
    IsVisible="@isRescheduleVisible"
    RescheduleResult="OnRescheduleResult"
    OrganizationId="@organizationId"
    C4L_ClassroomId="@c4l_classroomId"
    OriginalDate="@rescheduleOriginalDate" />

<style>
    .lessons-calendar {
        padding: 20px;
    }

    .calendar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .language-selector {
        display: flex;
        gap: 10px;
    }

    .week-navigation {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .non-contact-days-button-wrapper {
        display: flex;
        align-items: center;
    }

    .calendar-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 10px;
    }

    .calendar-column {
        text-align: center;
    }

    .lesson-row {
        display: contents;
    }

    .lesson-cell {
        min-height: 100px;
    }

    .column-header {
        text-align: center;
        margin-bottom: 10px;
    }

    .date {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .week-day {
        color: #666;
        display: flex;
        flex-direction: column;
        gap: 4px;
        align-items: center;
    }

        .week-day div:first-child {
            font-weight: 600;
            color: var(--c4l-primary-purple);
        }

    .lessons-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .lesson-card {
        border: 1px solid #eee;
        padding: 10px;
        background-color: white;
        min-height: 100px; /* Ensure consistent height */
        display: flex;
        flex-direction: column;
    }

        .lesson-card.empty {
            border: none;
            background-color: transparent;
        }

    .lesson-placeholder {
        flex: 1;
    }

    .lesson-type {
        font-size: 0.9em;
        color: #666;
        margin-bottom: 5px;
    }

    .lesson-title {
        font-weight: bold;
        margin-bottom: 10px;
        flex: 1; /* Allow title to grow */
    }

    .clickable {
        cursor: pointer;
        color: var(--c4l-primary-purple);
    }

    .clickable:hover {
        text-decoration: underline;
    }

    .button-group {
        display: flex;
        gap: 10px;
        align-items: flex-start;
        flex-wrap: wrap;
    }

    .prep-button {
        margin-top: auto; /* Push button to bottom */
        white-space: nowrap;
    }

    .date-picker-container {
        margin-top: 10px;
        width: 100%;
    }

    .date-input {
        max-width: 200px;
    }

    .non-contact-day {
        color: #666;
        font-style: italic;
    }
</style>
