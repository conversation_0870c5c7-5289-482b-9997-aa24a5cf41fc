﻿@page "/entity3list"
@using Compass.Common.DTOs.Entity3
@using Compass.Common.DTOs.Generic;
@using Compass.Common.Data
@using Compass.Common.Helpers
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject NavigationManager NavigationManager
@inject IEntity3Service Entity3Service
@inject UserAccessor UserAccessor
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<h1 class="page-title horizontal-line">@entity3Hierarchy @Localizer["lbl_list"]</h1>

<div class="c4l-search-table-wrapper">
    <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

    <LoaderComponent IsLoading="isLoading">
        <div class="c4l-table-scroll-wrapper">
            <div class="c4l-table-wrapper has-links entity3-wrapper">
                <div class="c4l-table-headings-wrapper entity3-headings-wrapper">
                    <h6 class="c4l-table-heading">@Localizer["lbl_Name"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_ContactName"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_ContactEmail"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_ContactPhone"]</h6>
                    @if (@entity1Hierarchy != string.Empty)
                    {
                        <h6 class="c4l-table-heading">@entity1Hierarchy</h6>
                    }

                    @if (@entity2Hierarchy != string.Empty)
                    {
                        <h6 class="c4l-table-heading">@entity2Hierarchy</h6>
                    }
                </div>

                @foreach (Entity3ListDisplayDto entity in entity3Results)
                {
                    <button name="entity 3 select button" type="button" title="Select @entity.Name" @onclick="() => OnEntity3Selected(entity)">
                        <div class="c4l-table-result-wrapper entity3-result-wrapper">
                            <p class="c4l-table-result-item">@entity.Name</p>
                            <p class="c4l-table-result-item">@entity.ContactName</p>
                            <p class="c4l-table-result-item">@entity.ContactEmail</p>
                            <p class="c4l-table-result-item">@entity.ContactPhone</p>
                            @if (@entity.Entity1Name != null)
                            {
                                <p class="c4l-table-result-item">@entity.Entity1Name</p>
                            }

                            @if (@entity.Entity2Name != null)
                            {
                                <p class="c4l-table-result-item">@entity.Entity2Name</p>
                            }
                        </div>
                    </button>
                }
            </div>
        </div>

        <div class="c4l-pagination-wrapper">
            <div class="c4l-pagination-buttons-wrapper">
                <div class="buttons-wrapper">
                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnPreviousClicked()"
                        disabled="@(currentPage <= 1)"
                    >
                        @Localizer["lbl_Previous"]
                    </button>

                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnNextClicked()"
                        disabled="@(currentPage >= maxPages)"
                    >
                        @Localizer["lbl_Next"]
                    </button>
                </div>
            </div>

            <div class="page-count-wrapper font-weight-500">
                <span class="current-page-number">@currentPage</span>  @Localizer["lbl_of"] @maxPages
            </div>
        </div>
    </LoaderComponent>
</div>
