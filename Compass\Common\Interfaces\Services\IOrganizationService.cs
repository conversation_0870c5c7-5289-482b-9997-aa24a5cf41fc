﻿using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Organization;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Services
{
    public interface IOrganizationService
    {
        public Task<Organization?> UpdateOrganizationAsync(long? id, Organization organization);
        public Task<Organization?> GetOrganizationAsync(long? organizationId);
        public Task<Organization> AddOrganizationAsync(Organization organization);
        public Task<KaplanPageable<OrganizationListDisplayDto>> GetOrganizationPages(PageQuery pageQuery);
        public Task<UserOrganizationLink> AssignOrganizationUser(CreateUserLinkAction action);
        public Task<bool> UnAssignOrganizationUser(CreateUserLinkAction action);
        public Task<bool> CheckOrganizationMfaRequirements(long? organizationId);
    }
}
