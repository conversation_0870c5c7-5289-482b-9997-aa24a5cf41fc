using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.EntityFrameworkCore;
using Serilog;

namespace Compass.C4L.Repositories
{
    public class C4LLearningObjectiveRepository : IC4LLearningObjectiveRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

        public C4LLearningObjectiveRepository(IDbContextFactory<ApplicationDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<IEnumerable<C4LLearningObjective>> GetLearningObjectivesAsync(string language)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.C4LLearningObjectives
                        .Where(lo => lo.Language == language)
                        .OrderBy(lo => lo.Domain)
                        .ThenBy(lo => lo.ContentArea)
                        .ToListAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error getting learning objectives for language {Language}", language);
                throw;
            }
        }
    }
}
