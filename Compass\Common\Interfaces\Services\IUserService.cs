﻿using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.User;
using Compass.Common.Helpers;

namespace Compass.Common.Interfaces.Services
{
    public interface IUserService
    {
        public enum AssignLevels
        {
            OrgLevel = 0,
            Entity1Level = 1,
            Entity2Level = 2,
            Entity3Level = 3,
            SiteLevel = 4,
            StudentGroupLevel = 5
        }

        public Task<KaplanPageable<UserListDisplayDto>> GetUserDisplayPages(UserListAction action);
        public Task<KaplanPageable<UserListDisplayDto>> GetOrganizationUserDisplayPages(UserListAction action);
        public Task<KaplanPageable<UserListDisplayDto>> GetEntity1UserDisplayPages(UserListAction action);
        public Task<KaplanPageable<UserListDisplayDto>> GetEntity2UserDisplayPages(UserListAction action);
        public Task<KaplanPageable<UserListDisplayDto>> GetEntity3UserDisplayPages(UserListAction action);
        public Task<KaplanPageable<UserListDisplayDto>> GetSiteUserDisplayPages(UserListAction action);
        public Task<KaplanPageable<UserListDisplayDto>> GetStudentGroupUserDisplayPages(UserListAction action);
        public Task<KaplanPageable<UserListDisplayDto>> GetUserAssignList(UserAssignListAction action);
        public Task<List<UserAssignmentDto>> GetUserAssignments(string userId);
    }
}
