﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Common.Models
{
    [Table("cmn_user_organization_links")]
    public class UserOrganizationLink
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("mod_id")]
        public string ModId { get; set; } = string.Empty;

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Column("link_status")]
        public string LinkStatus { get; set; } = string.Empty;

        [Column("user_role")]
        public string UserRole { get; set; } = string.Empty;

        [Column("aspnetuser_id")]
        public string UserId { get; set; } = string.Empty;

        [Column("cmn_user_organization_access_id")]
        public long OrganizationAccessId { get; set; }

    }
}
