namespace Compass.Components.Account.Shared
{
  public partial class ManageLayout : IDisposable
  {

    protected override async Task OnInitializedAsync()
    {
      CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);
    }

    private void UpdateLocalizedValues()
    {
      var culture = CurrentLanguageObserver.GetCurrentCulture();
      CultureService.SetCulture(culture);

      InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
      CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
    }
  }
}
