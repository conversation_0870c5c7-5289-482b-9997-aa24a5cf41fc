using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_lap_screen_cutoff_scores")]
    public class LAPScreenCutoffScore
    {
        [Key]
        [Column("InstID")]
        public long Id { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Required]
        [Column("CustomerInstID")]
        public long OrganizationId { get; set; }

        [Column("ThreeYear")]
        public int? ThreeYear { get; set; }

        [Column("FourYear")]
        public int? FourYear { get; set; }

        [Column("FiveYear")]
        public int? FiveYear { get; set; }
    }
}