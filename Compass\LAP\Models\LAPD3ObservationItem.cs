using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_lapd3_observation_items")]
    public class LAPD3ObservationItem
    {
        [Key]
        [Column("InstID")]
        public long Id { get; set; }

        [Required]
        [Column("CustomerInstID")]
        public long OrganizationId { get; set; }

        [Required]
        [Column("ObservationInstID")]
        public long ObservationId { get; set; }

        [Required]
        [Column("ChildInstID")]
        public long StudentId { get; set; }

        [Required]
        [Column("SubscaleStaticID")]
        public long SubscaleStaticId { get; set; }

        [Required]
        [Column("ItemStaticID")]
        public long ItemStaticId { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Required]
        [Column("SourceSubscaleInstID")]
        public long SourceSubscaleInstId { get; set; }

        [Column("SourceItemInstID")]
        public long? SourceItemInstId { get; set; }

        [Column("CrossScore")]
        public int? CrossScore { get; set; }
    }
}