﻿@page "/Account/AcceptInvite"
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.JSInterop
@using System.Security.Claims
@layout EmptyLayout

@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityRedirectManager RedirectManager
@inject NavigationManager NavigationManager
@inject ILogger<AcceptInvite> Logger
@inject IJSRuntime JSRuntime

<PageTitle>Accept Invitation | C4L</PageTitle>

<div class="invite-container">
    <div class="invite-card">
        <div class="invite-header">
            <img src="/c4l-favicon.png" alt="C4L Logo" class="invite-logo" />
            <h1>Accept Invitation</h1>
        </div>

        <!-- Hidden form with antiforgery token for logout -->
        <form id="logoutForm" action="/Account/Logout" method="post" style="display:none;">
            <AntiforgeryToken />
            <input type="hidden" name="returnUrl" value="login" />
        </form>

        <EditForm Model="Input" FormName="formUser" OnValidSubmit="ConfirmUserAsync">
            <DataAnnotationsValidator></DataAnnotationsValidator>
            <ValidationSummary></ValidationSummary>

            <div class="form-group mb-3">
                <label for="username">Username</label>
                <InputText id="username" @bind-Value="Input.UserName" class="form-control" required />
            </div>

            <div class="form-group mb-3">
                <label for="password">Password</label>
                <InputText id="password" type="password" @bind-Value="Input.Password" class="form-control" autocomplete="new-password" aria-required="true" />
                <ValidationMessage For="() => Input.Password" class="text-danger" />
            </div>

            <div class="form-group mb-3">
                <label for="confirmPassword">Confirm Password</label>
                <InputText id="confirmPassword" type="password" @bind-Value="Input.ConfirmPassword" class="form-control" autocomplete="new-password" aria-required="true" />
                <ValidationMessage For="() => Input.ConfirmPassword" class="text-danger" />
            </div>

            <div class="form-group mb-3">
                <label for="firstName">First Name</label>
                <InputText id="firstName" @bind-Value="Input.FirstName" class="form-control" required />
            </div>

            <div class="form-group mb-3">
                <label for="lastName">Last Name</label>
                <InputText id="lastName" @bind-Value="Input.LastName" class="form-control" required />
            </div>

            <div class="mt-4">
                <button class="btn btn-primary w-100" type="submit">Complete Registration</button>
            </div>

            @if (!string.IsNullOrEmpty(statusMessage))
            {
                <div class="alert alert-success mt-3" role="alert">
                    @statusMessage
                </div>
            }
        </EditForm>
    </div>
</div>