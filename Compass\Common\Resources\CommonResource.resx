﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <data name="login-text" xml:space="preserve">
    <value>Log in</value>
  </data>
  <data name="login-page-heading" xml:space="preserve">
    <value>Log in with a personal account</value>
  </data>
  <data name="login-page-form-heading" xml:space="preserve">
    <value>Use a personal account to log in</value>
  </data>
  <data name="login-page-remember-me" xml:space="preserve">
    <value>Remember me?</value>
  </data>
  <data name="login-page-forgot-password" xml:space="preserve">
    <value>Forgot Password?</value>
  </data>
  <data name="login-page-resend-email" xml:space="preserve">
    <value>Resend confirmation email</value>
  </data>
  <data name="lbl_Address2" xml:space="preserve">
    <value>Address 2</value>
  </data>
  <data name="btn_Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="btn-update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="lbl_Address1" xml:space="preserve">
    <value>Address 1</value>
  </data>
  <data name="lbl_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="msg_InfoSaved" xml:space="preserve">
    <value>Information saved successfully!</value>
  </data>
  <data name="msg_InfoUpdated" xml:space="preserve">
    <value>Information updated successfully!</value>
  </data>
  <data name="lbl_City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="lbl_State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="lbl_ZipCode" xml:space="preserve">
    <value>Zip Code</value>
  </data>
  <data name="lbl_ContactFirstName" xml:space="preserve">
    <value>Contact First Name</value>
  </data>
  <data name="lbl_ContactLastName" xml:space="preserve">
    <value>Contact Last Name</value>
  </data>
  <data name="lbl_ContactEmail" xml:space="preserve">
    <value>Contact Email</value>
  </data>
  <data name="lbl_ContactPhone" xml:space="preserve">
    <value>Contact Phone</value>
  </data>
  <data name="lbl_ContactFax" xml:space="preserve">
    <value>Contact Fax</value>
  </data>
  <data name="lbl_Fax" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="lbl_About" xml:space="preserve">
    <value>About</value>
  </data>
  <data name="btn_save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="btn_close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="err_HierarchyOrder" xml:space="preserve">
    <value>Hierarchy Order is incorrect</value>
  </data>
  <data name="err_Entity1HasActiveEntity2s" xml:space="preserve">
    <value>The Entity 1 you are trying to delete still has active Entity 2s</value>
  </data>
  <data name="err_Entity1HasActiveSites" xml:space="preserve">
    <value>The Entity 1 you are trying to delete still has active Sites</value>
  </data>
  <data name="err_Entity2HasActiveEntity3s" xml:space="preserve">
    <value>The Entity 2 you are trying to delete still has active Entity 3s</value>
  </data>
  <data name="err_Entity2HasActiveSites" xml:space="preserve">
    <value>The Entity 2 you are trying to delete still has active Sites</value>
  </data>
  <data name="err_Entity3HasActiveSites" xml:space="preserve">
    <value>The Entity 3 you are trying to delete still has active Sites</value>
  </data>
  <data name="err_SiteHasActiveClassrooms" xml:space="preserve">
    <value>The Site you are trying to delete still has active Student Groups</value>
  </data>
  <data name="err_UsersAssigned" xml:space="preserve">
    <value>Can not delete without un-assigning all users first</value>
  </data>
  <data name="lbl_Search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="lbl_AssignUser" xml:space="preserve">
    <value>Assign User</value>
  </data>
  <data name="lbl_InviteUser" xml:space="preserve">
    <value>Invite User</value>
  </data>
  <data name="lbl_Username" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="password-label" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="lbl_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="lbl_OrganizationUserList" xml:space="preserve">
    <value>Organization Users List</value>
  </data>
  <data name="org-addedit-details" xml:space="preserve">
    <value>Organization Details</value>
  </data>
  <data name="lbl_SearchAllFields" xml:space="preserve">
    <value>Search all fields</value>
  </data>
  <data name="lbl_FirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="lbl_LastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="lbl_Previous" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="lbl_Next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="lbl_Remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="lbl_CurrentPage" xml:space="preserve">
    <value>Current Page</value>
  </data>
  <data name="lbl_of" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="lbl_Summary" xml:space="preserve">
    <value>Summary</value>
  </data>
  <data name="lbl_Manage" xml:space="preserve">
    <value>Manage</value>
  </data>
  <data name="lbl_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="lbl_Support_Resources" xml:space="preserve">
    <value>Support &amp; Resources</value>
  </data>
  <data name="lbl_Reports" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="lbl_Users" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="lbl_ContactInformation" xml:space="preserve">
    <value>Contact Information</value>
  </data>
  <data name="lbl_ProductName" xml:space="preserve">
    <value>Product Name</value>
  </data>
  <data name="lbl_Begin" xml:space="preserve">
    <value>Begin</value>
  </data>
  <data name="lbl_End" xml:space="preserve">
    <value>End</value>
  </data>
  <data name="lbl_AccountType" xml:space="preserve">
    <value>Account Type</value>
  </data>
  <data name="lbl_ActiveLicenses" xml:space="preserve">
    <value>Active License</value>
  </data>
  <data name="lbl_ArchiveLicenses" xml:space="preserve">
    <value>Archive Licenses</value>
  </data>
  <data name="lbl_ActiveInUse" xml:space="preserve">
    <value>Active In Use</value>
  </data>
  <data name="lbl_ArchiveInUse" xml:space="preserve">
    <value>Archive In Use</value>
  </data>
  <data name="lbl_Entity" xml:space="preserve">
    <value>Entity</value>
  </data>
  <data name="lbl_OrganizationList" xml:space="preserve">
    <value>Organization List</value>
  </data>
  <data name="lbl_AddOrganization" xml:space="preserve">
    <value>Add Organization</value>
  </data>
  <data name="lbl_EditOrganization" xml:space="preserve">
    <value>Edit Organization</value>
  </data>
  <data name="lbl_OrganizationName" xml:space="preserve">
    <value>Organization Name</value>
  </data>
  <data name="lbl_MFARequired" xml:space="preserve">
    <value>MFA Required</value>
  </data>
  <data name="lbl_Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="lbl_AddRegion" xml:space="preserve">
    <value>Add </value>
  </data>
  <data name="lbl_Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="lbl_Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="lbl_Phone" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="lbl_list" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="lbl_DistrictName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lbl_DistrictList" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="lbl_ContactName" xml:space="preserve">
    <value>Contact Name</value>
  </data>
  <data name="lbl_UserList" xml:space="preserve">
    <value>User List</value>
  </data>
  <data name="lbl_adduser" xml:space="preserve">
    <value>Add User</value>
  </data>
  <data name="lbl_RemoveUser" xml:space="preserve">
    <value>Remove User</value>
  </data>
  <data name="lbl_home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="lbl_RegionName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lbl_RegionList" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="lbl_AddDistrict" xml:space="preserve">
    <value>Add </value>
  </data>
  <data name="lbl_ZoneName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lbl_AddZone" xml:space="preserve">
    <value>Add Zone</value>
  </data>
  <data name="lbl_QuickViews" xml:space="preserve">
    <value>Quick Views</value>
  </data>
  <data name="lbl_AddSchool" xml:space="preserve">
    <value>Add School</value>
  </data>
  <data name="lbl_Region" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="lgl_District" xml:space="preserve">
    <value>District</value>
  </data>
  <data name="lbl_Zone" xml:space="preserve">
    <value>Zone</value>
  </data>
  <data name="lbl_School" xml:space="preserve">
    <value>School</value>
  </data>
  <data name="classroom-information-text" xml:space="preserve">
    <value>Classroom Information</value>
  </data>
  <data name="lbl_Classrooms" xml:space="preserve">
    <value>Classrooms</value>
  </data>
  <data name="lbl_Numberof" xml:space="preserve">
    <value>Total classrooms</value>
  </data>
  <data name="lbl_Children" xml:space="preserve">
    <value>Children</value>
  </data>
  <data name="lbl_Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="lbl_Archived" xml:space="preserve">
    <value>Archived</value>
  </data>
  <data name="lbl_Pending" xml:space="preserve">
    <value>Pending</value>
  </data>
  <data name="lbl_AddClassroom" xml:space="preserve">
    <value>Add Classroom</value>
  </data>
  <data name="lbl_Checkpoints" xml:space="preserve">
    <value>Checkpoints</value>
  </data>
  <data name="lbl_SchoolYears" xml:space="preserve">
    <value>School Years</value>
  </data>
  <data name="lbl_accessibility" xml:space="preserve">
    <value>Accessibility</value>
  </data>
  <data name="lbl_teachers" xml:space="preserve">
    <value>Teacher(s)</value>
  </data>
  <data name="classroom_standard" xml:space="preserve">
    <value>Standard</value>
  </data>
  <data name="classroom_specialist" xml:space="preserve">
    <value>Specialist</value>
  </data>
  <data name="no-users-text" xml:space="preserve">
    <value>There are no users for this entity. Add a user in order to see them in the users list.</value>
  </data>
  <data name="no-data-text" xml:space="preserve">
    <value>There is no data to show.</value>
  </data>
  <data name="manage-account-settings" xml:space="preserve">
    <value>Manage your account settings</value>
  </data>
</root>