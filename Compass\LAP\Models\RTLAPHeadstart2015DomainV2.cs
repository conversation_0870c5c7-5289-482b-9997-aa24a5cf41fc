using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_rt_lap_headstart_2015_domain_v2")]
    public class RTLAPHeadstart2015DomainV2
    {
        [Key]
        [Column("InstID")]
        public long InstId { get; set; }

        [Column("RuleId")]
        public int? RuleId { get; set; }

        [Required]
        [Column("ChildInstID")]
        public long ChildInstId { get; set; }

        [Required]
        [Column("SchoolYearInstID")]
        public long SchoolYearInstId { get; set; }

        [Required]
        [Column("CheckPt")]
        public int CheckPt { get; set; }

        [Required]
        [Column("Instrument")]
        public int Instrument { get; set; }

        [Required]
        [Column("Schoolyear")]
        public int Schoolyear { get; set; }

        [Required]
        [Column("AssessmentInstID")]
        public long AssessmentInstId { get; set; }

        [Required]
        [Column("HSDomainSequence")]
        public int HSDomainSequence { get; set; }

        [Required]
        [Column("HSDomainName")]
        [StringLength(50)]
        public string HSDomainName { get; set; } = string.Empty;

        [Column("ChronologicalAge")]
        public int? ChronologicalAge { get; set; }

        [Column("Age1Percentile", TypeName = "decimal(5,2)")]
        public decimal? Age1Percentile { get; set; }

        [Column("Age2Percentile", TypeName = "decimal(5,2)")]
        public decimal? Age2Percentile { get; set; }

        [Column("Age3Percentile", TypeName = "decimal(5,2)")]
        public decimal? Age3Percentile { get; set; }

        [Column("Age1Potential")]
        public int? Age1Potential { get; set; }

        [Column("Age2Potential")]
        public int? Age2Potential { get; set; }

        [Column("Age3Potential")]
        public int? Age3Potential { get; set; }

        [Column("Age1Mastered")]
        public int? Age1Mastered { get; set; }

        [Column("Age2Mastered")]
        public int? Age2Mastered { get; set; }

        [Column("Age3Mastered")]
        public int? Age3Mastered { get; set; }

        [Column("Percentage", TypeName = "decimal(5,2)")]
        public decimal? Percentage { get; set; }

        [Column("Potential")]
        public int? Potential { get; set; }

        [Column("Mastered")]
        public int? Mastered { get; set; }

        [Column("Age4Percentile", TypeName = "decimal(5,2)")]
        public decimal? Age4Percentile { get; set; }

        [Column("Age4Potential")]
        public int? Age4Potential { get; set; }

        [Column("Age4Mastered")]
        public int? Age4Mastered { get; set; }

        [Column("Age5Percentile", TypeName = "decimal(5,2)")]
        public decimal? Age5Percentile { get; set; }

        [Column("Age5Potential")]
        public int? Age5Potential { get; set; }

        [Column("Age5Mastered")]
        public int? Age5Mastered { get; set; }

        [Column("Age6Percentile", TypeName = "decimal(5,2)")]
        public decimal? Age6Percentile { get; set; }

        [Column("Age6Potential")]
        public int? Age6Potential { get; set; }

        [Column("Age6Mastered")]
        public int? Age6Mastered { get; set; }
    }
}