﻿using Compass.Common.DTOs.Entity2;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Compass.Common.Models;
using Compass.Common.Resources;

namespace Compass.Common.Services
{
    public class Entity2Service : IEntity2Service
    {
        private readonly IEntity2Repository _entity2Repository;
        private readonly IEntity3Repository _entity3Repository;
        private readonly ISiteRepository _siteRepository;
        private readonly IOrganizationHierarchyRepository _organizationHierarchyRepository;
        private readonly IUserEntity2AccessRepository _userEntity2AccessRepository;
        private readonly IUserEntity2LinkRepository _userEntity2LinkRepository;
        private readonly IUserRepository _userRepository;

        public Entity2Service(IEntity2Repository entity2Repository,
                                IEntity3Repository entity3Repository,
                                ISiteRepository siteRepository,
                                IOrganizationHierarchyRepository organizationHierarchyRepository,
                                IUserEntity2AccessRepository userEntity2AccessRepository,
                                IUserEntity2LinkRepository userEntity2LinkRepository,
                                IUserRepository userRepository)
        {
            _entity2Repository = entity2Repository;
            _entity3Repository = entity3Repository;
            _siteRepository = siteRepository;
            _organizationHierarchyRepository = organizationHierarchyRepository;
            _userEntity2AccessRepository = userEntity2AccessRepository;
            _userEntity2LinkRepository = userEntity2LinkRepository;
            _userRepository = userRepository;
        }

        public async Task<Entity2> CreateEntity2Async(Entity2 entity2)
        {
            Entity2 createdEntity2 = await _entity2Repository.CreateEntity2Async(entity2);

            UserEntity2Access entity2UserAccess = new UserEntity2Access();
            entity2UserAccess.OrganizationId = entity2.OrganizationId;
            entity2UserAccess.Entity2Id = entity2.Id;
            entity2UserAccess.CanAdd = "Y";
            entity2UserAccess.CanUpdate = "Y";
            entity2UserAccess.CanDelete = "Y";
            entity2UserAccess.CanView = "Y";
            entity2UserAccess.CanAssign = "Y";

            await _userEntity2AccessRepository.AddUserEntity2AccessAsync(entity2UserAccess);

            Entity3 entity3 = new Entity3();
            entity3.OrganizationId = entity2.OrganizationId;
            entity3.Entity1Id = entity2.Entity1Id;
            entity3.Entity2Id = entity2.Id;
            entity3.Name = "Default";
            entity3.IsDeleted = "D";
            await _entity3Repository.CreateEntity3Async(entity3);

            return createdEntity2;
        }

        public async Task<KaplanPageable<Entity2ListDisplayDto>> GetEntity2Page(Entity2ListAction action)
        {
            List<Entity2ListDisplayDto> entity2List = await _entity2Repository.GetEntity2List(action);

            long? organizationId = action.organizationId;
            long? entity1Id = action.entity1Id;

            PageQuery pageQuery = action.pageQuery;
            int pageSize = pageQuery.PageSize;

            int entity2Count = await _entity2Repository.GetEntity2Count(organizationId, entity1Id, pageQuery.QueryText);

            int maxPages = (int)Math.Ceiling((double)entity2Count / pageSize);

            KaplanPageable<Entity2ListDisplayDto> pageable = new KaplanPageable<Entity2ListDisplayDto>();
            pageable.PageContent = entity2List;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<int> GetEntity2Count(long orgId)
        {
            int count = await _entity2Repository.GetEntity2Count(orgId, null, "");
            return count;
        }

        private async Task<bool> ValidateHasUsers(long? organizationId, long? entity2Id)
        {
            int userCount = await _userRepository.GetEntity2UserCount(organizationId, entity2Id, string.Empty);

            if (userCount > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task<DeleteReturnDto> DeleteEntity2(long? organizationId, long? entity1Id, long? entity2Id)
        {
            DeleteReturnDto ret = new();
            ret.Success = true;

            OrganizationHierarchy? hierarchy = await _organizationHierarchyRepository.GetOrganizationHierarchyAsync(organizationId);

            if (hierarchy != null)
            {
                if (hierarchy.HierarchyEntity3EntityName != string.Empty)
                {
                    // validate entity3
                    int entity3Count = await _entity3Repository.GetEntity3Count(organizationId, entity1Id, entity2Id, string.Empty);

                    if (entity3Count > 0)
                    {
                        ret.Success = false;
                        ret.Message = nameof(CommonResource.err_Entity2HasActiveEntity3s);
                    }
                }
                else if (hierarchy.HierarchySiteEntityName != string.Empty)
                {
                    // validate site
                    int siteCount = await _siteRepository.GetSiteCount(organizationId, entity1Id, entity2Id, null, string.Empty);

                    if (siteCount > 0)
                    {
                        ret.Success = false;
                        ret.Message = nameof(CommonResource.err_Entity2HasActiveSites);
                    }
                }

            }
            else
            {
                throw new Exception("Organization Hierarchy not set up");
            }

            // validate users
            bool hasUsers = await ValidateHasUsers(organizationId, entity2Id);
            if (hasUsers)
            {
                ret.Success = false;
                ret.Message = nameof(CommonResource.err_UsersAssigned);
            }

            //If valid then delete
            if (ret.Success)
            {
                bool success = await _entity2Repository.DeleteEntity2(entity2Id);
                ret.Success = success;
            }

            return ret;
        }

        public async Task<UserEntity2Link> AssignEntity2User(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            long? entity2Id = action.EntityId;

            UserEntity2Access? userEntity1Access = await _userEntity2AccessRepository.GetUserEntity2AccessAsync(organizationId, entity2Id);

            if (userEntity1Access is null)
            {
                throw new Exception("No Entity 2 Access Found");
            }

            UserEntity2Link createdLink = new UserEntity2Link();
            createdLink.OrganizationId = organizationId;
            createdLink.Entity2UserAccessId = userEntity1Access.Id;
            createdLink.UserId = action.UserId;
            createdLink.LinkStatus = CompassResource.LinkStatus_Active;
            createdLink.UserRole = "TEST"; // TODO need to figure out user roles

            UserEntity2Link newLink = await _userEntity2LinkRepository.AddUserEntity2LinkAsync(createdLink);

            return newLink;
        }

        public async Task<bool> UnAssignEntity2User(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            long? entity2Id = action.EntityId;
            UserEntity2Access? userEntity2Access = await _userEntity2AccessRepository.GetUserEntity2AccessAsync(organizationId, entity2Id);

            if (userEntity2Access is null)
            {
                throw new Exception("No Entity 2 Access Found");
            }

            string? userId = action.UserId;
            long? accessId = userEntity2Access.Id;

            UserEntity2Link? removeLink = await _userEntity2LinkRepository.GetUserEntity2LinkAsync(organizationId, userId, accessId);

            if (removeLink is null)
            {
                throw new Exception("No Link Found");
            }

            long? linkId = removeLink.Id;
            bool result = await _userEntity2LinkRepository.RemoveUserEntity2LinkAsync(linkId);

            return result;
        }

        public async Task<List<Entity2>> GetEntities2Async(long? organizationId, long? entity1Id)
        {
            if (organizationId == null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (entity1Id == null)
            {
                throw new ArgumentNullException(nameof(entity1Id));
            }

            List<Entity2> ret = await _entity2Repository.GetEntities2Async(organizationId, entity1Id);
            return ret;
        }
    }
}
