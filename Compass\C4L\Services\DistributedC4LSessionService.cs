using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Data;
using Compass.Common.Models;
using Compass.Common.Services;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

namespace Compass.C4L.Services
{
    /// <summary>
    /// Distributed session state service for C4L components using Redis cache
    /// </summary>
    public class DistributedC4LSessionService : IC4LSessionStateService
    {
        private readonly IDistributedCache _cache;
        private readonly UserAccessor _userAccessor;
        private readonly TimeSpan _defaultExpiration = TimeSpan.FromMinutes(30);
        
        private const string NAVIGATION_KEY_PREFIX = "c4l:nav:";
        private const string LESSON_KEY_PREFIX = "c4l:lesson:";

        public DistributedC4LSessionService(IDistributedCache cache, UserAccessor userAccessor)
        {
            _cache = cache;
            _userAccessor = userAccessor;
        }

        #region Navigation Context Properties

        public int CurrentUnit
        {
            get => GetNavigationContextAsync().Result.CurrentUnit;
            set => UpdateNavigationPropertyAsync(ctx => ctx.CurrentUnit = value).Wait();
        }

        public int CurrentWeek
        {
            get => GetNavigationContextAsync().Result.CurrentWeek;
            set => UpdateNavigationPropertyAsync(ctx => ctx.CurrentWeek = value).Wait();
        }

        public string CurrentCalendarUnitWeeks
        {
            get => GetNavigationContextAsync().Result.CurrentCalendarUnitWeeks;
            set => UpdateNavigationPropertyAsync(ctx => ctx.CurrentCalendarUnitWeeks = value).Wait();
        }

        public DateTime CurrentMondayDate
        {
            get => GetNavigationContextAsync().Result.CurrentMondayDate;
            set => UpdateNavigationPropertyAsync(ctx => ctx.CurrentMondayDate = value).Wait();
        }

        public DateTime ClassroomStartDate
        {
            get => GetNavigationContextAsync().Result.ClassroomStartDate;
            set => UpdateNavigationPropertyAsync(ctx => ctx.ClassroomStartDate = value).Wait();
        }

        public long? C4L_ClassroomId
        {
            get => GetNavigationContextAsync().Result.C4L_ClassroomId;
            set => UpdateNavigationPropertyAsync(ctx => ctx.C4L_ClassroomId = value).Wait();
        }

        public long? NonContactDayId
        {
            get => GetNavigationContextAsync().Result.NonContactDayId;
            set => UpdateNavigationPropertyAsync(ctx => ctx.NonContactDayId = value).Wait();
        }

        #endregion

        #region Lesson Context Properties

        public string SelectedLessonTitle
        {
            get => GetLessonContextAsync().Result.SelectedLessonTitle;
            set => UpdateLessonPropertyAsync(ctx => ctx.SelectedLessonTitle = value).Wait();
        }

        public string SelectedLessonType
        {
            get => GetLessonContextAsync().Result.SelectedLessonType;
            set => UpdateLessonPropertyAsync(ctx => ctx.SelectedLessonType = value).Wait();
        }

        public int SelectedLessonUnit
        {
            get => GetLessonContextAsync().Result.SelectedLessonUnit;
            set => UpdateLessonPropertyAsync(ctx => ctx.SelectedLessonUnit = value).Wait();
        }

        public int SelectedLessonWeek
        {
            get => GetLessonContextAsync().Result.SelectedLessonWeek;
            set => UpdateLessonPropertyAsync(ctx => ctx.SelectedLessonWeek = value).Wait();
        }

        public int SelectedLessonDay
        {
            get => GetLessonContextAsync().Result.SelectedLessonDay;
            set => UpdateLessonPropertyAsync(ctx => ctx.SelectedLessonDay = value).Wait();
        }

        public int SelectedLessonTypeSequence
        {
            get => GetLessonContextAsync().Result.SelectedLessonTypeSequence;
            set => UpdateLessonPropertyAsync(ctx => ctx.SelectedLessonTypeSequence = value).Wait();
        }

        public int SelectedLessonTitleSequence
        {
            get => GetLessonContextAsync().Result.SelectedLessonTitleSequence;
            set => UpdateLessonPropertyAsync(ctx => ctx.SelectedLessonTitleSequence = value).Wait();
        }

        public string SelectedLessonLanguage
        {
            get => GetLessonContextAsync().Result.SelectedLessonLanguage;
            set => UpdateLessonPropertyAsync(ctx => ctx.SelectedLessonLanguage = value).Wait();
        }

        #endregion

        #region Batch Operations (Recommended for better performance)

        /// <summary>
        /// Set navigation context in a single operation
        /// </summary>
        public async Task SetNavigationContextAsync(C4LNavigationContext context)
        {
            string? userId = await GetCurrentUserIdAsync();
            if (userId == null) return;

            string key = NAVIGATION_KEY_PREFIX + userId;
            string serializedData = JsonSerializer.Serialize(context);
            DistributedCacheEntryOptions options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _defaultExpiration
            };

            await _cache.SetStringAsync(key, serializedData, options);
        }

        /// <summary>
        /// Get navigation context in a single operation
        /// </summary>
        public async Task<C4LNavigationContext> GetNavigationContextAsync()
        {
            string? userId = await GetCurrentUserIdAsync();
            if (userId == null) return new C4LNavigationContext();

            string key = NAVIGATION_KEY_PREFIX + userId;
            string? serializedData = await _cache.GetStringAsync(key);

            return serializedData == null 
                ? new C4LNavigationContext() 
                : JsonSerializer.Deserialize<C4LNavigationContext>(serializedData) ?? new C4LNavigationContext();
        }

        /// <summary>
        /// Set lesson context in a single operation
        /// </summary>
        public async Task SetLessonContextAsync(C4LLessonContext context)
        {
            string? userId = await GetCurrentUserIdAsync();
            if (userId == null) return;

            string key = LESSON_KEY_PREFIX + userId;
            string serializedData = JsonSerializer.Serialize(context);
            DistributedCacheEntryOptions options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _defaultExpiration
            };

            await _cache.SetStringAsync(key, serializedData, options);
        }

        /// <summary>
        /// Get lesson context in a single operation
        /// </summary>
        public async Task<C4LLessonContext> GetLessonContextAsync()
        {
            string? userId = await GetCurrentUserIdAsync();
            if (userId == null) return new C4LLessonContext();

            string key = LESSON_KEY_PREFIX + userId;
            string? serializedData = await _cache.GetStringAsync(key);

            return serializedData == null 
                ? new C4LLessonContext() 
                : JsonSerializer.Deserialize<C4LLessonContext>(serializedData) ?? new C4LLessonContext();
        }

        #endregion

        #region Private Helper Methods

        private async Task<string?> GetCurrentUserIdAsync()
        {
            (ApplicationUser?, string?) result = await _userAccessor.GetUserAndIdAsync();
            return result.Item2;
        }

        private async Task UpdateNavigationPropertyAsync(Action<C4LNavigationContext> updateAction)
        {
            C4LNavigationContext context = await GetNavigationContextAsync();
            updateAction(context);
            await SetNavigationContextAsync(context);
        }

        private async Task UpdateLessonPropertyAsync(Action<C4LLessonContext> updateAction)
        {
            C4LLessonContext context = await GetLessonContextAsync();
            updateAction(context);
            await SetLessonContextAsync(context);
        }

        #endregion
    }
}
