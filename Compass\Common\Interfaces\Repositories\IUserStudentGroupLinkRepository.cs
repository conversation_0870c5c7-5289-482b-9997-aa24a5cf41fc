﻿using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IUserStudentGroupLinkRepository
    {
        public Task<UserStudentGroupLink> AddUserStudentGroupLinkAsync(UserStudentGroupLink userStudentGroupLink);
        public Task<UserStudentGroupLink?> GetUserStudentGroupLinkAsync(long? organizationId, string? userId, long? accessId);
        public Task<bool> RemoveUserStudentGroupLinkAsync(long? linkId);
    }
}
