namespace Compass.C4L.Services
{
    /// <summary>
    /// Static class to store shared data between C4L components
    /// </summary>
    public static class C4LSessionData
    {
        /// <summary>
        /// The current curriculum unit
        /// </summary>
        public static int CurrentUnit { get; set; } = 1;

        /// <summary>
        /// The current curriculum week
        /// </summary>
        public static int CurrentWeek { get; set; } = 1;

        /// <summary>
        /// The current calendar week number (since classroom start date)
        /// </summary>
        public static int CurrentCalendarWeekNumber { get; set; } = 1;

        /// <summary>
        /// The current Monday date for the displayed week
        /// </summary>
        public static DateTime CurrentMondayDate { get; set; } = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek + (int)DayOfWeek.Monday);

        /// <summary>
        /// The classroom start date
        /// </summary>
        public static DateTime ClassroomStartDate { get; set; } = DateTime.Today;

        /// <summary>
        /// The classroom ID
        /// </summary>
        public static long? C4L_ClassroomId { get; set; }
    }
}
