﻿using Compass.Common.Data;
using Compass.Common.DTOs.OrganizationHierarchies;
using Compass.Common.Models;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;

namespace Compass.Common.Pages.Admin.Organization.Manage
{
    public partial class OrganizationHierarchiesComponent
    {
        [Parameter]
        public EventCallback OnReturn { get; set; }

        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        private long? organizationId;

        private string validationMessage = string.Empty;
        private string errorMessage = string.Empty;
        private string successMessage = string.Empty;

        private bool showValidationDialogBox = false;
        private bool showError = false;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            showValidationDialogBox = false;
            showError = false;

            CommonSessionData? commonSessionData = await GetCommonSessionData();

            if (commonSessionData is not null)
            {
                organizationId = commonSessionData.CurrentOrganizationId;

                if (organizationId != null)
                {
                    OrganizationHierarchy? organizationHierarchy = await OrganizationHierarchyRepository.GetOrganizationHierarchyAsync(organizationId);
                    if (organizationHierarchy is null)
                    {
                        organizationHierarchy = new OrganizationHierarchy();
                        organizationHierarchy.OrganizationId = (long)organizationId;
                    }
                    else
                    {
                        if (organizationHierarchy.HierarchyEntity1EntityName != string.Empty)
                        {
                            Input.IsEntity1Enabled = true;
                        }

                        if (organizationHierarchy.HierarchyEntity2EntityName != string.Empty)
                        {
                            Input.IsEntity2Enabled = true;
                        }

                        if (organizationHierarchy.HierarchyEntity3EntityName != string.Empty)
                        {
                            Input.IsEntity3Enabled = true;
                        }
                    }

                    SetInitialValues(organizationHierarchy);
                    StateHasChanged();
                }
            }
        }

        private void SetInitialValues(OrganizationHierarchy? organizationHierarchy)
        {
            if (organizationHierarchy != null)
            {
                Input.HierarchyEntity1EntityName = organizationHierarchy.HierarchyEntity1EntityName;
                Input.HierarchyEntity2EntityName = organizationHierarchy.HierarchyEntity2EntityName;
                Input.HierarchyEntity3EntityName = organizationHierarchy.HierarchyEntity3EntityName;
                Input.HierarchySiteEntityName = organizationHierarchy.HierarchySiteEntityName;
                Input.HierarchyStudentGroupEntityName = organizationHierarchy.HierarchyStudentGroupEntityName;
                Input.HierarchyStudentEntityName = organizationHierarchy.HierarchyStudentEntityName;
            }
        }

        private void ShowHierarchyValidationMessage(ValidateRemovedEntitiesDto validationDto)
        {
            bool entity1Removed = validationDto.Entity1Removed;
            bool entity2Removed = validationDto.Entity2Removed;
            bool entity3Removed = validationDto.Entity3Removed;

            validationMessage = "";

            if (entity1Removed)
            {
                validationMessage += "You have removed the entity 1 hierarchy while your organization has multiple values.\n";
            }
            if (entity2Removed)
            {
                validationMessage += "You have removed the entity 2 hierarchy while your organization has multiple values.\n";
            }
            if (entity3Removed)
            {
                validationMessage += "You have removed the entity 3 hierarchy while your organization has multiple values.\n";
            }

            validationMessage += "Are you sure you want to remove this option?";

            showValidationDialogBox = true;
        }

        protected async Task HandleHierarchyValidation(bool result)
        {
            if (result)
            {
                OrganizationHierarchy? organizationHierarchy = await OrganizationHierarchyRepository.GetOrganizationHierarchyAsync(organizationId);
                if (organizationHierarchy == null)
                {
                    organizationHierarchy = new OrganizationHierarchy();
                }
                organizationHierarchy = SetNewValues(organizationHierarchy);
                organizationHierarchy = SetEntityNames(organizationHierarchy);
                await SaveHierarchy(organizationHierarchy);
            }

            showValidationDialogBox = false;
        }

        private async Task SaveHierarchy(OrganizationHierarchy? organizationHierarchy)
        {
            if (organizationHierarchy != null)
            {
                organizationHierarchy.ModId = _currentUserId;
                SaveHierarchyDto? savedHierarchyDto = await OrganizationHierarchyService.SaveHierarchy(organizationHierarchy);

                if (savedHierarchyDto != null)
                {
                    if (savedHierarchyDto.SavedOrganizationHierarchy == null)
                    {
                        errorMessage = savedHierarchyDto.Message;
                        showError = true;
                    }
                    else
                    {
                        successMessage = "Information saved successfully!";

                        CommonSessionData? commonSessionData = await GetCommonSessionData();
                        if (commonSessionData is not null)
                        {
                            OrganizationHierarchy savedOrganizationHierarchy = savedHierarchyDto.SavedOrganizationHierarchy;
                            commonSessionData.Entity1Hierarchy = savedOrganizationHierarchy.HierarchyEntity1EntityName;
                            commonSessionData.Entity2Hierarchy = savedOrganizationHierarchy.HierarchyEntity2EntityName;
                            commonSessionData.Entity3Hierarchy = savedOrganizationHierarchy.HierarchyEntity3EntityName;
                            commonSessionData.SiteHierarchy = savedOrganizationHierarchy.HierarchySiteEntityName;
                            commonSessionData.StudentGroupHierarchy = savedOrganizationHierarchy.HierarchyStudentGroupEntityName;

                            await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                            await CommonSessionDataObserver.BroadcastStateChangeAsync();
                            await OnReturn.InvokeAsync();
                        }
                    }
                }
            }
        }

        private OrganizationHierarchy SetEntityNames(OrganizationHierarchy organizationHierarchy)
        {
            if (!Input.IsEntity1Enabled)
            {
                organizationHierarchy.HierarchyEntity1EntityName = string.Empty;
                organizationHierarchy.HierarchyEntity2EntityName = string.Empty;
                organizationHierarchy.HierarchyEntity3EntityName = string.Empty;
            }
            else
            {
                if (!Input.IsEntity2Enabled)
                {
                    organizationHierarchy.HierarchyEntity2EntityName = string.Empty;
                    organizationHierarchy.HierarchyEntity3EntityName = string.Empty;
                }
                else
                {
                    if (!Input.IsEntity3Enabled)
                    {
                        organizationHierarchy.HierarchyEntity3EntityName = string.Empty;
                    }
                }
            }

            return organizationHierarchy;
        }

        private OrganizationHierarchy SetNewValues(OrganizationHierarchy organizationHierarchy)
        {
            organizationHierarchy.HierarchyEntity1EntityName = Input.HierarchyEntity1EntityName ?? string.Empty;
            organizationHierarchy.HierarchyEntity2EntityName = Input.HierarchyEntity2EntityName ?? string.Empty;
            organizationHierarchy.HierarchyEntity3EntityName = Input.HierarchyEntity3EntityName ?? string.Empty;
            organizationHierarchy.HierarchySiteEntityName = Input.HierarchySiteEntityName ?? string.Empty;
            organizationHierarchy.HierarchyStudentGroupEntityName = Input.HierarchyStudentGroupEntityName ?? string.Empty;
            organizationHierarchy.HierarchyStudentEntityName = Input.HierarchyStudentEntityName ?? string.Empty;

            return organizationHierarchy;
        }

        protected async Task OnHierarchySubmit()
        {
            if (organizationId != null)
            {
                OrganizationHierarchy? organizationHierarchy = await OrganizationHierarchyRepository.GetOrganizationHierarchyAsync(organizationId);
                if (organizationHierarchy == null)
                {
                    organizationHierarchy = new OrganizationHierarchy();
                }
                organizationHierarchy = SetNewValues(organizationHierarchy);
                organizationHierarchy = SetEntityNames(organizationHierarchy);
                organizationHierarchy.OrganizationId = (long)organizationId;

                ValidateRemovedEntitiesDto validationDto = await OrganizationHierarchyService.ValidateRemovedEntities(organizationHierarchy);
                if (validationDto.Entity1Removed || validationDto.Entity2Removed || validationDto.Entity3Removed)
                {
                    ShowHierarchyValidationMessage(validationDto);
                }
                else
                {
                    await SaveHierarchy(organizationHierarchy);
                }
            }
        }

        protected void HideErrorMessage()
        {
            showError = false;
        }

        public sealed class InputModel
        {
            [Display(Name = "Entity 1 Entity Name")]
            [CustomValidation(typeof(InputModel), nameof(ValidateEntity1Name))]
            public string? HierarchyEntity1EntityName { get; set; }

            [Display(Name = "Entity 2 Entity Name")]
            [CustomValidation(typeof(InputModel), nameof(ValidateEntity2Name))]
            public string? HierarchyEntity2EntityName { get; set; }

            [Display(Name = "Entity 3 Entity Name")]
            [CustomValidation(typeof(InputModel), nameof(ValidateEntity3Name))]
            public string? HierarchyEntity3EntityName { get; set; }

            [Required]
            [Display(Name = "Site Entity Name")]
            public string? HierarchySiteEntityName { get; set; }

            [Required]
            [Display(Name = "Student Group Entity Name")]
            public string? HierarchyStudentGroupEntityName { get; set; }

            [Required]
            [Display(Name = "Student Entity Name")]
            public string? HierarchyStudentEntityName { get; set; }

            public bool IsEntity1Enabled { get; set; }
            public bool IsEntity2Enabled { get; set; }
            public bool IsEntity3Enabled { get; set; }

            public static ValidationResult? ValidateEntity1Name(string? entity1Name, ValidationContext context)
            {
                InputModel? instance = context.ObjectInstance as InputModel;
                if (instance is null)
                {
                    return ValidationResult.Success;
                }

                if (instance.IsEntity1Enabled && string.IsNullOrWhiteSpace(entity1Name))
                {
                    return new ValidationResult("Entity 1 Entity Name is required");
                }

                return ValidationResult.Success;
            }

            public static ValidationResult? ValidateEntity2Name(string? entity2Name, ValidationContext context)
            {
                InputModel? instance = context.ObjectInstance as InputModel;
                if (instance is null)
                {
                    return ValidationResult.Success;
                }

                if (instance.IsEntity2Enabled && string.IsNullOrWhiteSpace(entity2Name))
                {
                    return new ValidationResult("Entity 2 Entity Name is required");
                }

                return ValidationResult.Success;
            }

            public static ValidationResult? ValidateEntity3Name(string? entity3Name, ValidationContext context)
            {
                InputModel? instance = context.ObjectInstance as InputModel;
                if (instance is null)
                {
                    return ValidationResult.Success;
                }

                if (instance.IsEntity3Enabled && string.IsNullOrWhiteSpace(entity3Name))
                {
                    return new ValidationResult("Entity 3 Entity Name is required");
                }

                return ValidationResult.Success;
            }
        }
    }
}
