using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Deca.Models
{
    [Table("edeca_questions")]
    public class DecaQuestion
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        [Column("record_form")]
        [MaxLength(1)]
        public string? RecordForm { get; set; }

        [Column("question_number")]
        [Required]
        public int QuestionNumber { get; set; }

        [Column("text_english")]
        [MaxLength(255)]
        public string? TextEnglish { get; set; }

        [Column("text_spanish")]
        [MaxLength(255)]
        public string? TextSpanish { get; set; }

        [Column("protective_factor_scale")]
        [MaxLength(3)]
        public string? ProtectiveFactorScale { get; set; }
    }
}
