namespace Compass.LAP.DTOs
{
    public class LapAssessmentContainerDto
    {
        public long? AssessmentInstId { get; set; }
        public long? OrganizationId { get; set; }
        public long? StudentId { get; set; }
        public DateTime? AssessmentDate { get; set; }
        public int Instrument { get; set; }
        public int Language { get; set; }
        public int Checkpoint { get; set; }

        public List<LapSubscaleContainerDto> LapSubscaleContainerList { get; set; } = new List<LapSubscaleContainerDto>();
    }
}
