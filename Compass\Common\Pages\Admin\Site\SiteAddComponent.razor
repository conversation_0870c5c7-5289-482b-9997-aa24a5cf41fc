﻿@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject IEntity1Service Entity1Service;
@inject IEntity2Service Entity2Service;
@inject IEntity3Service Entity3Service;
@inject ISiteService SiteService;
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService
@inject UserAccessor UserAccessor

@implements IDisposable

<EditForm Model="Input" FormName="formSite" OnValidSubmit="SubmitAsync" class="c4l-form site-add-form">
    <h3 class="c4l-form-heading">@Localizer["lbl_Add"] @siteHierarchy</h3>

    <DataAnnotationsValidator></DataAnnotationsValidator>
    <ValidationSummary></ValidationSummary>

    <div>
        <label class="col-form-label" for="site-name">
            @Localizer["lbl_Name"]
            <InputText @bind-Value="Input.SiteName"
                        class="form-control mt-2"
                id="site-name" 
                aria-required="true"
            />
        </label>
        <ValidationMessage For="() => Input.SiteName" class="mt-2 text-danger" />
    </div>

    <div>
        <label class="col-form-label" for="site-address1">
            @Localizer["lbl_Address1"]
            <InputText @bind-Value="Input.Address1"
                        class="form-control mt-2"
                id="site-address1" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="site-address2">
            @Localizer["lbl_Address2"]
            <InputText @bind-Value="Input.Address2"
                        class="form-control mt-2"
                id="site-address2" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="site-city">
            @Localizer["lbl_City"]
            <InputText @bind-Value="Input.City"
                        class="form-control mt-2"
                id="site-city" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="site-state">
            @Localizer["lbl_State"]
            <InputSelect @bind-Value="Input.State"
                            id="site-state" 
            >
                <option disabled value="">-- @Localizer["lbl_State"] --</option>
                @foreach (var stateCode in CompassResource.UsStates)
                {
                    <option value="@stateCode">@stateCode</option>
                }
            </InputSelect>
        </label>
    </div>

    <div>
        <label class="col-form-label" for="site-zipcode">
            @Localizer["lbl_ZipCode"]
            <InputText @bind-Value="Input.ZipCode"
                        class="form-control mt-2"
                id="site-zipcode" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="site-contact-firstname">
            @Localizer["lbl_ContactFirstName"]
            <InputText @bind-Value="Input.ContactFirstName"
                        class="form-control mt-2"
                id="site-contact-firstname" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="site-contact-lastname">
            @Localizer["lbl_ContactLastName"]
            <InputText @bind-Value="Input.ContactLastName"
                        class="form-control mt-2"
                id="site-contact-lastname" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="site-contact-email">
            @Localizer["lbl_ContactEmail"]
            <InputText @bind-Value="Input.ContactEmail"
                        class="form-control mt-2"
                id="site-contact-email" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="site-contact-phone">
            @Localizer["lbl_ContactPhone"]
            <InputText @bind-Value="Input.ContactPhone"
                        class="form-control mt-2"
                id="site-contact-phone" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="site-contact-fax">
            @Localizer["lbl_ContactFax"]
            <InputText @bind-Value="Input.ContactFax"
                        class="form-control mt-2"
                id="site-contact-fax" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="site-fax">
            @Localizer["lbl_Fax"]
            <InputText @bind-Value="Input.Fax"
                        class="form-control mt-2"
                id="site-fax" 
            />
        </label>
    </div>

    <span>Initial School Year</span>
    <div class="form-floating mb-3">
        <InputNumber class="form-control" @bind-Value="Input.SchoolYear" autocomplete="school-year" required placeholder="School Year" />
        <label class="form-label" for="school-year">School Year</label>
        <ValidationMessage For="() => Input.SchoolYear" class="text-danger" />
    </div>

    <div class="form-floating mb-3">
        <InputText class="form-control" @bind-Value="Input.Description" autocomplete="description" required placeholder="Description" />
        <label for="description">Description</label>
        <ValidationMessage For="() => Input.Description" class="text-danger" />
    </div>

    <div class="form-submit-buttons-wrapper">
        <button class="c4l-button c4l-secondary-button c4l-form-button" type="submit">@Localizer["btn_save"]</button>
    </div>

    @if (!string.IsNullOrEmpty(successMessage))
    {
        <div class="alert alert-success mt-4" role="alert">
            <p class="text-center font-weight-600 mb-0">@successMessage</p>
        </div>
    }
</EditForm>
