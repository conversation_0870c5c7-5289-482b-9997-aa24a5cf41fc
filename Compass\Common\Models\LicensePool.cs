using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Common.Models
{
    [Table("cmn_license_pools")]
    public class LicensePool
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime? ModTs { get; set; }

        [Column("status")]
        public string? Status { get; set; } = string.Empty;

        [Column("name")]
        public string? Name { get; set; } = string.Empty;

        [Column("product")]
        public string? Product { get; set; } = string.Empty;

        [Column("accounting_type")]
        public string? AccountingType { get; set; } = string.Empty;

        [Column("purchased_licenses")]
        public int? PurchasedLicenses { get; set; }

        [Column("purchased_archived_licenses")]
        public int? PurchasedArchivedLicenses { get; set; }

        [Column("used_licenses")]
        public int? UsedLicenses { get; set; }

        [Column("used_archived_licenses")]
        public int? UsedArchivedLicenses { get; set; }

        [Column("begin_ts")]
        public DateTime? BeginTs { get; set; }

        [Column("end_ts")]
        public DateTime? EndTs { get; set; }

        [Column("notes")]
        public string? Notes { get; set; }
    }
}
