﻿using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Organization;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Compass.Common.Models;
using Compass.Common.Resources;

namespace Compass.Common.Services
{
    public class OrganizationService : IOrganizationService
    {
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IUserOrganizationAccessRepository _userOrganizationAccessRepository;
        private readonly IUserOrganizationLinkRepository _userOrganizationLinkRepository;

        public OrganizationService(IOrganizationRepository organizationRepository,
                                    IUserOrganizationAccessRepository userOrganizationAccessRepository,
                                    IUserOrganizationLinkRepository userOrganizationLinkRepository)
        {
            _organizationRepository = organizationRepository;
            _userOrganizationAccessRepository = userOrganizationAccessRepository;
            _userOrganizationLinkRepository = userOrganizationLinkRepository;
        }

        public async Task<Organization?> UpdateOrganizationAsync(long? id, Organization organization)
        {
            Organization? updatedOrganization = await _organizationRepository.UpdateOrganizationAsync(id, organization);

            return updatedOrganization;
        }

        public async Task<Organization?> GetOrganizationAsync(long? organizationId)
        {
            Organization? organization = await _organizationRepository.GetOrganizationAsync(organizationId);

            return organization;
        }

        public async Task<Organization> AddOrganizationAsync(Organization organization)
        {
            Organization createdOrganization = await _organizationRepository.AddOrganizationAsync(organization);

            //Create access
            UserOrganizationAccess organizationUserAccess = new UserOrganizationAccess();
            organizationUserAccess.OrganizationId = organization.Id;
            organizationUserAccess.CanAdd = "Y";
            organizationUserAccess.CanUpdate = "Y";
            organizationUserAccess.CanDelete = "Y";
            organizationUserAccess.CanView = "Y";
            organizationUserAccess.CanAssign = "Y";

            await _userOrganizationAccessRepository.AddUserOrganizationAccessAsync(organizationUserAccess);

            return createdOrganization;
        }

        public async Task<KaplanPageable<OrganizationListDisplayDto>> GetOrganizationPages(PageQuery pageQuery)
        {
            List<OrganizationListDisplayDto> OrganizationList = await _organizationRepository.GetOrganizations(pageQuery);

            int organzationCount = await _organizationRepository.GetOrganizationCount(pageQuery.QueryText);
            int pageSize = pageQuery.PageSize;

            int maxPages = (int)Math.Ceiling((double)organzationCount / pageSize);

            KaplanPageable<OrganizationListDisplayDto> pageable = new KaplanPageable<OrganizationListDisplayDto>();
            pageable.PageContent = OrganizationList;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<UserOrganizationLink> AssignOrganizationUser(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            UserOrganizationAccess? userOrganizationAccess = await _userOrganizationAccessRepository.GetUserOrganizationAccessAsync(organizationId);

            if (userOrganizationAccess is null)
            {
                throw new Exception("No Organzation Access Found");
            }

            UserOrganizationLink createdLink = new UserOrganizationLink();
            createdLink.OrganizationId = organizationId;
            createdLink.OrganizationAccessId = userOrganizationAccess.Id;
            createdLink.UserId = action.UserId;
            createdLink.LinkStatus = CompassResource.LinkStatus_Active;
            createdLink.UserRole = "TEST"; // TODO need to figure out user roles

            UserOrganizationLink newLink = await _userOrganizationLinkRepository.AddUserOrganizationLinkAsync(createdLink);

            return newLink;
        }

        public async Task<bool> UnAssignOrganizationUser(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            UserOrganizationAccess? userOrganizationAccess = await _userOrganizationAccessRepository.GetUserOrganizationAccessAsync(organizationId);

            if (userOrganizationAccess is null)
            {
                throw new Exception("No Organzation Access Found");
            }

            string? userId = action.UserId;
            long? accessId = userOrganizationAccess.Id;

            UserOrganizationLink? removeLink = await _userOrganizationLinkRepository.GetUserOrganizationLinkAsync(organizationId, userId, accessId);

            if (removeLink is null)
            {
                throw new Exception("No Link Found");
            }

            long? linkId = removeLink.Id;
            bool result = await _userOrganizationLinkRepository.RemoveUserOrganizationLinkAsync(linkId);

            return result;
        }

        public async Task<bool> CheckOrganizationMfaRequirements(long? organizationId)
        {
            if (organizationId is null)
            {
                throw new Exception("No Organzation Id passed");
            }

            Organization? organization = await GetOrganizationAsync(organizationId);
            if (organization is null)
            {
                throw new Exception("No Organzation found");
            }

            string? mfaRequiredValue = organization.MfaRequired;

            bool mfaRequired;
            if (mfaRequiredValue == "Y")
            {
                mfaRequired = true;
            }
            else
            {
                mfaRequired = false;
            }

            return mfaRequired;
        }
    }
}
