﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity3
{
    public partial class Entity3SummaryComponent : IDisposable
    {
        private string contactName = string.Empty;
        private string email = string.Empty;
        private string phone = string.Empty;

        private List<Compass.Common.Models.LicensePool> licensePoolPage = new();

        private int maxPages;
        private int currentPage;

        private long? organizationId;
        private bool isLoading = true;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private string entityName = string.Empty;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                organizationId = commonSessionData.CurrentOrganizationId;
                long? entity3Id = commonSessionData.CurrentEntity3Id;
                Compass.Common.Models.Entity3? currentEntity3 = await Entity3Repository.GetEntity3Async(entity3Id);

                if (currentEntity3 != null)
                {
                    string contactFirstName = currentEntity3.ContactFirstName ?? string.Empty;
                    string contactLastName = currentEntity3.ContactLastName ?? string.Empty;
                    contactName = contactFirstName + " " + contactLastName;
                    entityName = commonSessionData.SelectedEntityName;

                    email = currentEntity3.ContactEmail ?? string.Empty;
                    phone = currentEntity3.ContactPhone ?? string.Empty;

                    currentPage = 1;
                    maxPages = 0;

                    await GetLicensePoolPage();
                }
            }
        }

        private async Task GetLicensePoolPage()
        {
            isLoading = true;
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;

            KaplanPageable<Compass.Common.Models.LicensePool> currentPage = await LicensePoolService.GetLicensePoolSummaryPages(pageQuery, organizationId);

            licensePoolPage = currentPage.PageContent;
            maxPages = currentPage.MaxPages;
            isLoading = false;

            StateHasChanged();
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetLicensePoolPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetLicensePoolPage();
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
