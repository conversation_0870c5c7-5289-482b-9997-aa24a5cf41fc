﻿using Compass.Common.Data;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;

namespace Compass.Common.Pages.Admin.Organization
{
    public partial class OrganizationEditComponent : IDisposable
    {
        private string culture = "en-US";
        private string switchLanguageText = "Español";
        private long? organizationId;

        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        private bool showSuccessMessage = false;

        private string successMessage = "Information saved successfully!";

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.organizationId = commonSessionData.CurrentOrganizationId;
                if (this.organizationId is not null)
                {
                    Compass.Common.Models.Organization? organization = await OrganizationService.GetOrganizationAsync(this.organizationId);
                    SetInitialValues(organization);
                    StateHasChanged();
                }
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private void SetInitialValues(Compass.Common.Models.Organization? organization)
        {
            if (organization != null)
            {
                Input.Name ??= organization.Name;
                Input.ContactEmail ??= organization.ContactEmail;
                Input.ContactFirstName ??= organization.ContactFirstName;
                Input.ContactLastName ??= organization.ContactLastName;
                Input.ContactPhoneNumber ??= organization.ContactPhone;
                Input.Address1 ??= organization.Address1;
                Input.Address2 ??= organization.Address2;
                Input.City ??= organization.City;
                Input.State ??= organization.State;
                Input.ZipCode ??= organization.ZipCode;

                string? mfaRequired = organization.MfaRequired;
                if (mfaRequired == "Y")
                {
                    Input.MfaRequired = true;
                }
                else
                {
                    Input.MfaRequired = false;
                }
            }
        }

        private Compass.Common.Models.Organization SetNewValues(Compass.Common.Models.Organization organization)
        {
            organization.Name = Input.Name ?? string.Empty;
            organization.ContactEmail = Input.ContactEmail;
            organization.ContactFirstName = Input.ContactFirstName;
            organization.ContactLastName = Input.ContactLastName;
            organization.ContactPhone = Input.ContactPhoneNumber;
            organization.Address1 = Input.Address1;
            organization.Address2 = Input.Address2;
            organization.City = Input.City;
            organization.State = Input.State;
            organization.ZipCode = Input.ZipCode;

            bool mfaRequired = Input.MfaRequired;
            if (mfaRequired)
            {
                organization.MfaRequired = "Y";
            }
            else
            {
                organization.MfaRequired = "N";
            }

            return organization;
        }

        protected async Task OnValidateSubmit()
        {
            try
            {
                CommonSessionData? commonSessionData = await GetCommonSessionData();
                if (commonSessionData is not null)
                {
                    Compass.Common.Models.Organization? organization = await OrganizationService.GetOrganizationAsync(this.organizationId);
                    if (organization != null)
                    {
                        organization = SetNewValues(organization);
                        if (this.organizationId is not null && organization is not null)
                        {
                            Compass.Common.Models.Organization? savedOrganization = await OrganizationService.UpdateOrganizationAsync(this.organizationId, organization);

                            if (savedOrganization != null)
                            {
                                commonSessionData.CurrentOrganizationName = savedOrganization.Name;

                                await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                                await CommonSessionDataObserver.BroadcastStateChangeAsync();
                                showSuccessMessage = true;
                                StateHasChanged();
                            }
                        }
                    }
                }
            }
            catch (Exception)
            {
                showSuccessMessage = false;
            }
        }

        public sealed class InputModel
        {
            [Required]
            [Display(Name = "Name")]
            public string? Name { get; set; }

            [Display(Name = "Contact Email")]
            public string? ContactEmail { get; set; }

            [Display(Name = "Contact First Name")]
            public string? ContactFirstName { get; set; }

            [Display(Name = "Contact Last Name")]
            public string? ContactLastName { get; set; }

            [Display(Name = "Contact Phone number")]
            [CustomValidation(typeof(InputModel), nameof(ValidatePhone))]
            public string? ContactPhoneNumber { get; set; }

            [Display(Name = "Address 1")]
            public string? Address1 { get; set; }

            [Display(Name = "Address 2")]
            public string? Address2 { get; set; }

            [Display(Name = "City")]
            public string? City { get; set; }

            [Display(Name = "State")]
            public string? State { get; set; }

            [Display(Name = "Zip Code")]
            public string? ZipCode { get; set; }

            [Display(Name = "MFA Required")]
            public bool MfaRequired { get; set; }

            public static ValidationResult? ValidatePhone(string? phone, ValidationContext context)
            {
                if (string.IsNullOrEmpty(phone))
                {
                    return ValidationResult.Success; // Optional, so no error if empty.
                }

                string phonePattern = @"^\+?[1-9]\d{1,14}$";
                if (System.Text.RegularExpressions.Regex.IsMatch(phone, phonePattern))
                {
                    return ValidationResult.Success;
                }

                return new ValidationResult("The phone number is not valid.");
            }
        }
        protected void SetSpanishCulture()
        {
            if (culture == "es-ES")
            {
                culture = "en-US";
                switchLanguageText = "Español";
            }
            else
            {
                culture = "es-ES";
                switchLanguageText = "English";
            }

            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged); // Re-render the component with the new culture
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
