﻿using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Prompts.Generic
{
    public partial class DialogBox
    {
        [Parameter]
        public string Title { get; set; } = "Confirm";

        [Parameter]
        public string Message { get; set; } = "Are you sure?";

        [Parameter]
        public string EntityName { get; set; } = string.Empty;

        [Parameter]
        public EventCallback<bool> DialogResult { get; set; }

        [Parameter]
        public bool IsVisible { get; set; }

        private async Task OnYes()
        {
            await DialogResult.InvokeAsync(true);
        }

        private async Task OnNo()
        {
            await DialogResult.InvokeAsync(false);
        }
    }
}
