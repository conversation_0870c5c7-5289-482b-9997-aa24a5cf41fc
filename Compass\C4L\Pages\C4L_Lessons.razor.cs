﻿using Compass.C4L.DTOs;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Data;
using Compass.Common.Pages.Admin.StudentGroup;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.C4L.Pages
{
    public partial class C4L_Lessons
    {
        [Inject]
        public required ILessonService LessonService { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; } = default!;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private long? organizationId;
        private long? c4l_classroomId;

        private string studentGroupTypeLabel = string.Empty;

        private DateTime CurrentMondayDate { get; set; }
        private DateTime ClassroomStartDate { get; set; }
        private string? CurrentUnitWeeks { get; set; }

        private int MondayUnit { get; set; }
        private int MondayWeek { get; set; }

        private List<C4LLesson> weekLessonList { get; set; } = new List<C4LLesson>();

        private List<LessonHeader> lessonHeaderList = new List<LessonHeader>();
        private List<List<C4LLesson>> lessonsByDay = new List<List<C4LLesson>>();
        int maxLessons = 0;

        private bool isRescheduleVisible = false;
        private DateTime rescheduleOriginalDate;

        private bool showDatePicker = false;
        private DateTime selectedJumpDate = DateTime.Today;

        private string SelectedLanguage { get; set; } = "English";


        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            (ApplicationUser? user, string? userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;

            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            // Check if we're returning from the preparations component
            C4LNavigationContext navigationContext = await SessionStateService.GetNavigationContextAsync();
            if (navigationContext.C4L_ClassroomId.HasValue)
            {
                // Use the data from the session state service
                c4l_classroomId = navigationContext.C4L_ClassroomId;
                ClassroomStartDate = navigationContext.ClassroomStartDate;
                CurrentMondayDate = navigationContext.CurrentMondayDate;

                // Initialize with the current week's Monday
                await LoadWeekLessonsAsync();
            }
            else
            {
                // Get the data from the student group
                CommonSessionData? commonSessionData = await GetCommonSessionData();
                if (commonSessionData is not null)
                {
                    organizationId = commonSessionData.CurrentOrganizationId;
                    long? studentGroupId = commonSessionData.CurrentStudentGroupId;
                    if (studentGroupId.HasValue)
                    {
                        C4LClassroom? c4lc = await C4LClassroomService.GetByStudentGroupIdAsync(studentGroupId.Value);
                        if (c4lc is not null)
                        {
                            c4l_classroomId = c4lc.Id;
                            ClassroomStartDate = c4lc.StartDate;
                            CurrentMondayDate = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek + (int)DayOfWeek.Monday);

                            // Initialize with the current week's Monday
                            await LoadWeekLessonsAsync();
                        }
                    }
                }
            }
        }

        private void SetUpLessonDays()
        {
            Dictionary<string, int> dayMapping = new Dictionary<string, int>();
            for (int i = 0; i < lessonHeaderList.Count; i++)
            {
                LessonHeader combo = lessonHeaderList[i];
                if (combo.Description == string.Empty)
                {
                    string key = $"{combo.Unit}-{combo.Week}-{combo.Day}";
                    dayMapping[key] = i + 1; // 1-based (Monday=1, Tuesday=2, etc.)
                }
            }

            // Group lessons by day (1-5 for Monday-Friday)
            lessonsByDay = new List<List<C4LLesson>>();

            // Initialize empty lists for each day
            for (int i = 0; i < 5; i++)
            {
                lessonsByDay.Add(new List<C4LLesson>());
            }

            // Assign lessons to days based on the mapping
            foreach (C4LLesson lesson in weekLessonList)
            {
                string key = $"{lesson.Unit}-{lesson.Week}-{lesson.Day}";
                if (dayMapping.TryGetValue(key, out int calendarDay))
                {
                    // Add the lesson to the appropriate day's list
                    lessonsByDay[calendarDay - 1].Add(lesson);
                }
            }

            // Sort each day's lessons by LessonTypeSequence and TitleSequence
            for (int i = 0; i < 5; i++)
            {
                if ( lessonsByDay.Count() <= i) continue;

                lessonsByDay[i] = lessonsByDay[i]
                    .OrderBy(l => l.LessonTypeSequence)
                    .ThenBy(l => l.TitleSequence)
                    .ToList();
            }

            // Find the maximum number of lessons in any day
            maxLessons = lessonsByDay.Max(dayLessons => dayLessons.Count);
        }

        private async Task LoadWeekLessonsAsync()
        {
            try
            {
                // Get lessons from the service
                LessonPlanWeekDto dto = await LessonService.GetWeekLessonsAsync(ClassroomStartDate, CurrentMondayDate, SelectedLanguage, this.c4l_classroomId.Value);
                weekLessonList = dto.Lessons;
                List<NonContactDayIntValueDto> nonContactDays = dto.NonContactDays;

                lessonHeaderList = new List<LessonHeader>();
                int dayCount = 1;//Starting at 1 for monday

                // Add no lesson days for days before start date
                if (CurrentMondayDate < ClassroomStartDate)
                {
                    DateTime day = CurrentMondayDate;
                    while (day < ClassroomStartDate)
                    {
                        LessonHeader ncHeader = new LessonHeader();
                        ncHeader.Description = string.Empty;
                        lessonHeaderList.Add(ncHeader);
                        dayCount++;
                        day = day.AddDays(1);
                    }
                }

                MondayUnit = 0;
                MondayWeek = 0;
                SortedSet<string> UnitWeeks = new SortedSet<string>();
                foreach (C4LLesson lesson in weekLessonList)
                {
                    foreach (NonContactDayIntValueDto ncDayValue in nonContactDays)
                    {
                        int day = ncDayValue.Day;
                        if (day < dayCount)
                        {
                            break;
                        }
                        else if (day == dayCount)
                        {
                            LessonHeader ncHeader = new LessonHeader();
                            ncHeader.Description = ncDayValue.Description;
                            lessonHeaderList.Add(ncHeader);
                            dayCount++;
                        }
                    }

                    LessonHeader lessonHeader = new LessonHeader();
                    lessonHeader.Unit = lesson.Unit;
                    lessonHeader.Week = lesson.Week;
                    lessonHeader.Day = lesson.Day;

                    string unitWeek = $"Unit {lesson.Unit}- Week {lesson.Week}";
                    if (!UnitWeeks.Contains(unitWeek))
                    {
                        UnitWeeks.Add(unitWeek);
                    }

                    if (MondayUnit == 0 && MondayWeek == 0)
                    {
                        MondayUnit = lesson.Unit;
                        MondayWeek = lesson.Week;
                    }

                    if (lessonHeaderList.Count() < 5)
                    { 
                        if (!lessonHeaderList.Contains(lessonHeader))
                        {
                            lessonHeaderList.Add(lessonHeader);
                        }
                        dayCount++;
                    }
                }

                if (weekLessonList.Any())
                {
                    if ( UnitWeeks.Any())
                    {
                        CurrentUnitWeeks = UnitWeeks.Min;
                        if (UnitWeeks.Count > 1)
                        {
                            CurrentUnitWeeks += " thru " + UnitWeeks.Max;
                        }
                    }
                }

                SetUpLessonDays();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading week lessons: {ex.Message}");
            }
            StateHasChanged();
        }

        private async Task NavigateToPreviousWeek()
        {
            // Calculate the number of days from classroom start to current date
            int daysFromStart = (CurrentMondayDate - ClassroomStartDate).Days;

            // Subtract 7 days from the current Monday date
            CurrentMondayDate = CurrentMondayDate.AddDays(-7);

            await LoadWeekLessonsAsync();
        }

        private async Task NavigateToNextWeek()
        {
            // Add 7 days to the current start date
            CurrentMondayDate = CurrentMondayDate.AddDays(7);

            await LoadWeekLessonsAsync();
        }

        private async Task NavigateToToday()
        {
            // Calculate the Monday of the current week
            DateTime today = DateTime.Today;
            int daysFromMonday = (int)today.DayOfWeek - (int)DayOfWeek.Monday;
            if (daysFromMonday < 0) daysFromMonday += 7; // Handle Sunday (DayOfWeek.Sunday = 0)

            CurrentMondayDate = today.AddDays(-daysFromMonday);

            // Hide date picker if it's open
            showDatePicker = false;

            await LoadWeekLessonsAsync();
        }

        private void ToggleDatePicker()
        {
            showDatePicker = !showDatePicker;
            if (showDatePicker)
            {
                selectedJumpDate = CurrentMondayDate;
            }
            StateHasChanged();
        }

        private async Task OnDateSelected()
        {
            // Calculate the Monday of the selected week
            int daysFromMonday = (int)selectedJumpDate.DayOfWeek - (int)DayOfWeek.Monday;
            if (daysFromMonday < 0) daysFromMonday += 7; // Handle Sunday

            CurrentMondayDate = selectedJumpDate.AddDays(-daysFromMonday);

            // Hide the date picker
            showDatePicker = false;

            await LoadWeekLessonsAsync();
        }

        //TODO Handle Language
        private async Task OnLanguageChanged(string language)
        {
            SelectedLanguage = language;
            await LoadWeekLessonsAsync();
        }

        private void OnRescheduleClick(DateTime date)
        {
            this.rescheduleOriginalDate = date;
            this.isRescheduleVisible = true;
        }

        private async Task OnRescheduleResult(bool success)
        {
            this.isRescheduleVisible = false;

            if (success)
            {
                await LoadWeekLessonsAsync();
            }
        }


        private async Task NavigateToPreparations(int unit, int week)
        {
            // Store the current unit, week, calendar week number, and Monday date in the distributed service
            // so the preparations component can access it
            C4LNavigationContext navigationContext = new Compass.C4L.Models.C4LNavigationContext
            {
                CurrentUnit = unit,
                CurrentWeek = week,
                CurrentCalendarUnitWeeks = CurrentUnitWeeks,
                CurrentMondayDate = CurrentMondayDate,
                ClassroomStartDate = ClassroomStartDate,
                C4L_ClassroomId = c4l_classroomId
            };

            await SessionStateService.SetNavigationContextAsync(navigationContext);

            NavigationManager.NavigateTo($"/c4l-lessons-week-preparations");
        }

        private async Task NavigateToLessonDetails(C4LLesson lesson)
        {
            // Store the lesson details in the distributed session service
            C4LLessonContext lessonContext = new Compass.C4L.Models.C4LLessonContext
            {
                SelectedLessonTitle = lesson.Title,
                SelectedLessonType = lesson.LessonType,
                SelectedLessonUnit = lesson.Unit,
                SelectedLessonWeek = lesson.Week,
                SelectedLessonDay = lesson.Day,
                SelectedLessonTypeSequence = lesson.LessonTypeSequence,
                SelectedLessonTitleSequence = lesson.TitleSequence,
                SelectedLessonLanguage = SelectedLanguage
            };

            C4LNavigationContext navigationContext = new Compass.C4L.Models.C4LNavigationContext
            {
                CurrentMondayDate = CurrentMondayDate,
                ClassroomStartDate = ClassroomStartDate,
                C4L_ClassroomId = c4l_classroomId
            };

            // Set both contexts
            await SessionStateService.SetLessonContextAsync(lessonContext);
            await SessionStateService.SetNavigationContextAsync(navigationContext);

            NavigationManager.NavigateTo($"/c4l-lesson-details");
        }

        private sealed class LessonHeader
        {
            public int Unit { get; set; }
            public int Week { get; set; }
            public int Day { get; set; }
            public string Description { get; set; } = string.Empty;

            public override bool Equals(object obj)
            {
                if (obj is LessonHeader other)
                {
                    return Unit == other.Unit && Week == other.Week && Day == other.Day;
                }
                return false;
            }

            public override int GetHashCode()
            {
                return HashCode.Combine(Unit, Week, Day);
            }
        }
    }
}
