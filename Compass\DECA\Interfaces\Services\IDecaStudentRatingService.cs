﻿﻿using Compass.DECA.DTOs;
using Compass.Deca.Models;

namespace Compass.DECA.Interfaces.Services
{
    public interface IDecaStudentRatingService
    {
        /// <summary>
        /// Gets a list of student ratings for all students in a student group
        /// </summary>
        /// <param name="studentGroupId">The ID of the student group</param>
        /// <returns>A list of StudentGroupRatingDto objects</returns>
        Task<List<StudentGroupRatingDto>> GetCurrentStudentRatingsForStudentGroupAsync(long studentGroupId);

        /// <summary>
        /// Gets a list of student ratings for a student
        /// </summary>
        /// <param name="studentId">The ID of the student</param>
        /// <returns>A list of StudentGroupRatingDto objects</returns>
        Task<List<StudentGroupRatingDto>> GetStudentRatingsForStudentAsync(long studentId);

        /// <summary>
        /// Gets a student rating by ID
        /// </summary>
        /// <param name="id">The ID of the rating</param>
        /// <returns>The DecaStudentRating object if found, null otherwise</returns>
        Task<DecaStudentRating?> GetByIdAsync(long id);
        
        /// <summary>
        /// Creates a new student rating
        /// </summary>
        /// <param name="rating">The rating to create</param>
        /// <returns>The created rating</returns>
        Task<DecaStudentRating> CreateAsync(DecaStudentRating rating);
        
        /// <summary>
        /// Updates an existing student rating
        /// </summary>
        /// <param name="id">The ID of the rating to update</param>
        /// <param name="rating">The updated rating data</param>
        /// <returns>The updated rating if successful, null otherwise</returns>
        Task<DecaStudentRating?> UpdateAsync(long id, DecaStudentRating rating);
        
        /// <summary>
        /// Deletes a student rating
        /// </summary>
        /// <param name="id">The ID of the rating to delete</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> DeleteAsync(long id);
    }
}
