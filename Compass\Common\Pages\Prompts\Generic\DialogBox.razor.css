.dialog-box {
  width: min(100% - 2rem, 500px);
  padding: 1.25rem;
}

.dialog-box-heading {
  margin-block-end: 1rem;
}

.dialog-actions {
  flex-direction: column;
  gap: 1rem;
  
  & .c4l-button {
    width: 100%;
  }
}

@media (min-width: 48rem) {
  .dialog-actions {
    flex-direction: row;
    justify-content: center;

    & .c4l-button {
      width: fit-content;
    }
  }
}

@media (min-width: 64rem) {
  .dialog-box {
    transform: translateX(137.5px);
  }
}
