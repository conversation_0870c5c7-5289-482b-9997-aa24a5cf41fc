using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;

namespace Compass.C4L.Services
{
    public class C4LNonContactDayService : IC4LNonContactDayService
    {
        private readonly IC4LNonContactDayRepository _repository;

        public C4LNonContactDayService(IC4LNonContactDayRepository repository)
        {
            _repository = repository;
        }

        public async Task<IEnumerable<C4LNonContactDay>> GetNonContactDaysAsync(long c4l_classroomId)
        {
            return await _repository.GetByClassroomIdAsync(c4l_classroomId);
        }

        public async Task<C4LNonContactDay> CreateNonContactDayAsync(C4LNonContactDay nonContactDay)
        {
            if (nonContactDay.EndDate < nonContactDay.StartDate)
            {
                throw new ArgumentException("End date must be after start date");
            }

            bool hasOverlap = await _repository.HasOverlappingDates(
                nonContactDay.C4L_ClassroomId,
                nonContactDay.StartDate,
                nonContactDay.EndDate
            );

            if (hasOverlap)
            {
                throw new InvalidOperationException("Date range overlaps with existing non-contact days");
            }

            return await _repository.CreateAsync(nonContactDay);
        }

        public async Task<C4LNonContactDay> UpdateNonContactDayAsync(C4LNonContactDay nonContactDay)
        {
            if (nonContactDay.EndDate < nonContactDay.StartDate)
            {
                throw new ArgumentException("End date must be after start date");
            }

            bool hasOverlap = await _repository.HasOverlappingDates(
                nonContactDay.C4L_ClassroomId,
                nonContactDay.StartDate,
                nonContactDay.EndDate,
                nonContactDay.Id
            );

            if (hasOverlap)
            {
                throw new InvalidOperationException("Date range overlaps with existing non-contact days");
            }

            return await _repository.UpdateAsync(nonContactDay);
        }

        public async Task DeleteNonContactDayAsync(long id)
        {
            await _repository.DeleteAsync(id);
        }

        public async Task<C4LNonContactDay> GetNonContactDayAsync(long nonContactDayId)
        {
            C4LNonContactDay? nonContactDay = await _repository.GetByIdAsync(nonContactDayId);
            if (nonContactDay == null)
            {
                throw new KeyNotFoundException($"Non-contact day with ID {nonContactDayId} not found");
            }
            return nonContactDay;
        }

        // Implementation for the first ValidateDateRange overload
        public async Task<bool> ValidateDateRange(long classroomId, DateTime startDate, DateTime endDate, int? excludeId = null)
        {
            if (endDate < startDate)
            {
                return false;
            }

            bool hasOverlap = await _repository.HasOverlappingDates(
                classroomId,
                startDate,
                endDate,
                excludeId.HasValue ? (long?)excludeId.Value : null
            );

            return !hasOverlap;
        }

        // Implementation for the second ValidateDateRange overload
        public async Task<bool> ValidateDateRange(long classroomId, DateTime startDate, DateTime endDate, long? excludeId)
        {
            if (endDate < startDate)
            {
                return false;
            }

            bool hasOverlap = await _repository.HasOverlappingDates(
                classroomId,
                startDate,
                endDate,
                excludeId
            );

            return !hasOverlap;
        }

        public async Task<List<C4LNonContactDay>> GetNonContactDaysWithinDateRange(long classroomId, DateTime startDate, DateTime endDate)
        {
            List<C4LNonContactDay> ret = await _repository.GetNonContactDaysWithinDateRange(classroomId, startDate, endDate);
            return ret;
        }
    }
}
