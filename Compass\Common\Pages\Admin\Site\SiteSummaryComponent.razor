﻿@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Models
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject ISiteService SiteService;
@inject ILicensePoolService LicensePoolService;
@inject UserAccessor UserAccessor
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<LoaderComponent IsLoading="isLoading">
    <ContactSummary ContactName="@contactName" Email="@email" Phone="@phone" />
</LoaderComponent>

<section class="summary-section-wrapper mt-4">
    <h4 class="summary-title" data-color="white">@Localizer["classroom-information-text"]: <span>@currentSiteName</span></h4>

    <div class="summary-wrapper d-grid">
        <div class="summary-section">
            <h4 class="mb-3" data-color="c4l-secondary-teal">
                @Localizer["lbl_Classrooms"]
                <span class="summary-title-underline"></span>
            </h4>

            <div class="summary-detail">
                <span>@Localizer["lbl_Numberof"]:</span>
                <span class="font-weight-700" data-color="black">@studentGroupCount</span>
            </div>
        </div>

        <div class="summary-section">
            <h4 class="mb-3" data-color="c4l-secondary-teal">
                @Localizer["lbl_Children"]
                <span class="summary-title-underline"></span>
            </h4>

            <div class="summary-detail">
                <span class="summary-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 512 512" fill="var(--c4l-secondary-teal)">
                        <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"/>
                    </svg>
                    @Localizer["lbl_Active"]:
                </span>
                <span class="font-weight-700" data-color="black">@activeStudentCount</span>
            </div>

            <div class="summary-detail">
                <span class="summary-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 512 512" fill="var(--c4l-dark-gray)">
                        <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336l24 0 0-64-24 0c-13.3 0-24-10.7-24-24s10.7-24 24-24l48 0c13.3 0 24 10.7 24 24l0 88 8 0c13.3 0 24 10.7 24 24s-10.7 24-24 24l-80 0c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"/>
                    </svg>
                    @Localizer["lbl_Archived"]:
                </span>
                <span class="font-weight-700" data-color="black">@archiveStudentCount</span>
            </div>

            <div class="summary-detail">
                <span class="summary-item">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 512 512" fill="var(--c4l-tertiary-yellow)">
                        <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-384c13.3 0 24 10.7 24 24l0 112c0 13.3-10.7 24-24 24s-24-10.7-24-24l0-112c0-13.3 10.7-24 24-24zM224 352a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"/>
                    </svg>
                    @Localizer["lbl_Pending"]:
                </span>
                <span class="font-weight-700" data-color="black">@pendingStudentCount</span>
            </div>
        </div>
    </div>
</section>

<div class="c4l-table-scroll-wrapper">
    <div class="c4l-table-wrapper has-license-info org-summary-table-wrapper">
        <div class="c4l-table-title-wrapper">
            <h3 class="c4l-table-title">@($"License Summary For {currentSiteName}")</h3>
        </div>

        <div class="c4l-table-headings-wrapper org-summary-headings-wrapper">
            <h6 class="c4l-table-heading">@Localizer["lbl_ProductName"]</h6>
            <h6 class="c4l-table-heading">Status</h6>
            <h6 class="c4l-table-heading">@Localizer["lbl_AccountType"]</h6>
            <h6 class="c4l-table-heading">@Localizer["lbl_Begin"]</h6>
            <h6 class="c4l-table-heading">@Localizer["lbl_End"]</h6>
            <h6 class="c4l-table-heading">Purchased Licenses</h6>
            <TooltipText Text="Purchased Archive Licenses" Placement="TooltipPlacement.Bottom">
                <h6 class="c4l-table-heading">Purchased Archived Licenses</h6>
            </TooltipText>
            <h6 class="c4l-table-heading">Used Licenses</h6>
            <TooltipText Text="Used Archive Licenses" Placement="TooltipPlacement.Bottom">
                <h6 class="c4l-table-heading">Used Archived Licenses</h6>
            </TooltipText>
        </div>

        @foreach (LicensePool licensePool in licensePoolPage)
        {
            <div class="c4l-table-result-wrapper">
                <p class="c4l-table-result-item">@licensePool.Product</p>
                <p class="c4l-table-result-item">@licensePool.Status</p>
                <p class="c4l-table-result-item">@licensePool.AccountingType</p>
                <p class="c4l-table-result-item">@(licensePool.BeginTs?.ToString("d"))</p>
                <p class="c4l-table-result-item">@(licensePool.EndTs?.ToString("d"))</p>
                <p class="c4l-table-result-item">@licensePool.PurchasedLicenses</p>
                <p class="c4l-table-result-item">@licensePool.PurchasedArchivedLicenses</p>
                <p class="c4l-table-result-item">@licensePool.UsedLicenses</p>
                <p class="c4l-table-result-item">@licensePool.UsedArchivedLicenses</p>
            </div>
        }
    </div>
</div>

<div class="c4l-pagination-wrapper">
    <div class="c4l-pagination-buttons-wrapper">
        <div class="buttons-wrapper">
            <button class="c4l-button c4l-ghost-primary c4l-pagination-button"
                    @onclick="() => OnPreviousClicked()"
                    disabled="@(currentPage <= 1)">
                @Localizer["lbl_Previous"]
            </button>

            <button class="c4l-button c4l-ghost-primary c4l-pagination-button"
                    @onclick="() => OnNextClicked()"
                    disabled="@(currentPage >= maxPages)">
                @Localizer["lbl_Next"]
            </button>
        </div>
    </div>

    <div class="page-count-wrapper font-weight-500">
        <span class="current-page-number">@currentPage</span> @Localizer["lbl_of"] @maxPages
    </div>
</div>
