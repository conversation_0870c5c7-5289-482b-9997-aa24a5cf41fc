﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class UserSiteAccessRepository : IUserSiteAccessRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public UserSiteAccessRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<UserSiteAccess?> GetUserSiteAccessAsync(long? organizationId, long? siteId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (siteId is null)
            {
                throw new ArgumentNullException(nameof(siteId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.UserSiteAccesses.FirstOrDefaultAsync(o => o.OrganizationId == organizationId && o.SiteId == siteId);
            }
        }

        public async Task<UserSiteAccess?> AddUserSiteAccessAsync(UserSiteAccess? userSiteAccess)
        {
            if (userSiteAccess is null)
            {
                throw new ArgumentNullException(nameof(userSiteAccess));
            }

            // Get the authentication state 
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            userSiteAccess.ModId = userId;
            userSiteAccess.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.UserSiteAccesses.AddAsync(userSiteAccess);
                await _dbContext.SaveChangesAsync();
            }

            return userSiteAccess;
        }
    }
}
