using Compass.Common.Resources;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Controls.Generic
{
    public enum TooltipPlacement
    {
        Top,
        Bottom
    }

    public partial class TooltipText
    {
        [Parameter]
        public RenderFragment ChildContent { get; set; }

        [Parameter]
        public string Text { get; set; }

        [Parameter]
        public TooltipPlacement Placement { get; set; } = TooltipPlacement.Bottom;

        private bool isTooltipVisible = false;

        private string VisibilityClass => isTooltipVisible ? "visible" : "";

        private string ArrowClass => Placement switch
        {
            TooltipPlacement.Bottom => "up-arrow",
            TooltipPlacement.Top => "down-arrow",
            _ => "up-arrow"
        };

        private void ShowTooltip()
        {
            if (!string.IsNullOrWhiteSpace(Text))
            {
                isTooltipVisible = true;
            }
        }

        private void HideTooltip()
        {
            isTooltipVisible = false;
        }
    }
}
