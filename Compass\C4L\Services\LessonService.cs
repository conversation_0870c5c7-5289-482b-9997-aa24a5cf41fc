using Compass.C4L.DTOs;
using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;

namespace Compass.C4L.Services
{
    public class LessonService : ILessonService
    {
        private readonly IC4LNonContactDayService _nonContactDayService;
        private readonly ILessonRepository _lessonRepository;

        public LessonService(
            IC4LNonContactDayService nonContactDayService,
            ILessonRepository lessonRepository)
        {
            _nonContactDayService = nonContactDayService;
            _lessonRepository = lessonRepository;
        }

        public async Task<LessonPlanWeekDto> GetWeekLessonsAsync(DateTime classroomStartDate, DateTime mondayDateOfCurrentDate, string language, long classroomId)
        {
            // Get all non-contact days between classroom start and current date
            List<C4LNonContactDay> prevNonContactDays = await _nonContactDayService.GetNonContactDaysWithinDateRange(classroomId, classroomStartDate, mondayDateOfCurrentDate);
            int nonContactDayCount = prevNonContactDays.Count();

            int totalDays = 0;
            DateTime tempDate = classroomStartDate;
            DateTime maxStartDate = classroomStartDate > mondayDateOfCurrentDate ? classroomStartDate : mondayDateOfCurrentDate;
            while (tempDate < maxStartDate)
            {
                if (tempDate.DayOfWeek != DayOfWeek.Saturday
                        && tempDate.DayOfWeek != DayOfWeek.Sunday)
                {
                    totalDays++;
                }
                tempDate = tempDate.AddDays(1);
            }

            // Subtract non-contact days
            totalDays -= nonContactDayCount;
            totalDays = totalDays > 0 ? totalDays : 0;


            DateTime currentFriday = mondayDateOfCurrentDate.AddDays(4);
            List<C4LNonContactDay> currentWeekNonContactDates = await _nonContactDayService.GetNonContactDaysWithinDateRange(classroomId, mondayDateOfCurrentDate, currentFriday);
            int availableDays = 5 - currentWeekNonContactDates.Count();

            List<C4LLesson> lessons = await _lessonRepository.GetLessonPage(language, totalDays, availableDays);

            LessonPlanWeekDto ret = new LessonPlanWeekDto();
            ret.Lessons = lessons;

            List<NonContactDayIntValueDto> ncDayValues = GetNonContactDayValues(mondayDateOfCurrentDate, currentWeekNonContactDates);
            ret.NonContactDays = ncDayValues;

            return ret;
        }

        private List<NonContactDayIntValueDto> GetNonContactDayValues(DateTime monday, List<C4LNonContactDay> currentWeekNonContactDates)
        {
            List<NonContactDayIntValueDto> ret = new List<NonContactDayIntValueDto>();
            foreach (C4LNonContactDay nonContactDay in currentWeekNonContactDates)
            {
                DateTime currentDate = nonContactDay.StartDate.Date;
                while (currentDate <= nonContactDay.EndDate.Date)
                {
                    // Only include dates that are weekdays (Monday through Friday)
                    if (currentDate.DayOfWeek != DayOfWeek.Saturday &&
                        currentDate.DayOfWeek != DayOfWeek.Sunday)
                    {
                        NonContactDayIntValueDto ncDayValue = new NonContactDayIntValueDto();
                        ncDayValue.Day = (int)(currentDate.Date - monday.Date).TotalDays + 1;
                        if (nonContactDay.Description != null)
                        {
                            ncDayValue.Description = nonContactDay.Description;
                        }
                        else
                        {
                            ncDayValue.Description = string.Empty;
                        }

                        ret.Add(ncDayValue);
                    }
                    currentDate = currentDate.AddDays(1);

                }
            }

            return ret;
        }
    }
}
