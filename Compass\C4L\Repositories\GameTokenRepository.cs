﻿using Compass.C4L.Helpers;
using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.C4L.Repositories
{
    public class GameTokenRepository : IGameTokenRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public GameTokenRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<C4LGameToken> CreateOneTimeToken(GameParameters parameters)
        {
            string token = Convert.ToBase64String(System.Security.Cryptography.RandomNumberGenerator.GetBytes(32))
                .Replace('+', '-')
                .Replace('/', '_')
                .Replace("=", "");

            C4LGameToken gameToken = new C4LGameToken();
            gameToken.StudentId = parameters.StudentId;
            gameToken.C4LClassroomId = parameters.C4LClassroomId;
            gameToken.OrganizationId = parameters.OrganizationId;
            gameToken.Token = token;
            gameToken.Unit = parameters.Unit;
            gameToken.Week = parameters.Week;
            gameToken.StudentName = parameters.StudentName;
            gameToken.ExpireTs = parameters.ExpiresAt;
            gameToken.Used = "N";

            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

            gameToken.ModId = userId ?? string.Empty;
            gameToken.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.C4LGameTokens.AddAsync(gameToken);
                await _dbContext.SaveChangesAsync();
            }

            return gameToken;
        }
    }
}
