@page "/c4l-non-contact-day-list"

@using Compass.C4L.Interfaces.Services
@using Compass.C4L.Models
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserAccessor UserAccessor
@inject UserSessionService UserSessionService
@inject IStudentGroupRepository StudentGroupRepository;

<div class="component-content-wrapper non-contact-day-list-wrapper">
    <h3 class="page-title">Non-Contact Days</h3>

    <div class="non-contact-days-list">
        <table class="table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var nonContactDay in nonContactDays)
                {
                    <tr>
                        <td>@nonContactDay.Description</td>
                        <td>@nonContactDay.StartDate.ToShortDateString()</td>
                        <td>@nonContactDay.EndDate.ToShortDateString()</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

<style>
    .non-contact-day-list-wrapper {
        padding: 20px;
    }

    .page-title {
        margin-bottom: 2rem;
    }

    .non-contact-days-list {
        margin-top: 1rem;
    }

    .table {
        width: 100%;
        margin-bottom: 1rem;
        background-color: transparent;
    }

    .table th,
    .table td {
        padding: 0.75rem;
        vertical-align: top;
        border-top: 1px solid #dee2e6;
    }

    .table thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
    }
</style>