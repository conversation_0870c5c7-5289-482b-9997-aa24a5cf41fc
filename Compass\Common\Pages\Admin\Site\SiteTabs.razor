﻿@page "/site"
@using Compass.Common.Data
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Pages.Admin.StudentGroup
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject UserAccessor UserAccessor
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<h1 class="page-title">@currentSiteName</h1>

<ul class="nav-tabs-wrapper">
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == SUMMARY_INDEX ? "active" : "")" @onclick="() => ChangeTab(SUMMARY_INDEX)">@Localizer["lbl_Summary"]</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == MANAGE_INDEX ? "active" : "")" @onclick="() => ChangeTab(MANAGE_INDEX)">@Localizer["lbl_Manage"]</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == ADD_CHILD_INDEX ? "active" : "")" @onclick="() => ChangeTab(ADD_CHILD_INDEX)">@Localizer["lbl_Add"] @studentGroupHierarchy</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == EDIT_INDEX ? "active" : "")" @onclick="() => ChangeTab(EDIT_INDEX)">@Localizer["lbl_Edit"]</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == REPORT_INDEX ? "active" : "")" @onclick="() => ChangeTab(REPORT_INDEX)">@Localizer["lbl_Reports"]</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == CHECKPOINT_INDEX ? "active" : "")" @onclick="() => ChangeTab(CHECKPOINT_INDEX)">@Localizer["lbl_Checkpoints"]</button>
    </li>
    <li class="nav-item">
        <button class="c4l-tab c4l-primary-tab @(currentTab == USER_INDEX ? "active" : "")" @onclick="() => ChangeTab(USER_INDEX)">@Localizer["lbl_Users"]</button>
    </li>
</ul>

@if (currentTabComponent is not null)
{
    <div class="component-content-wrapper site-component-wrapper">
        <DynamicComponent Type="currentTabComponent" />
    </div>
}
