﻿namespace Compass.Common.DTOs.LicensePool
{
    public class LicensePoolListDisplayDto
    {
        public long Id { get; set; }
        public string? Status { get; set; }
        public string? Name { get; set; }
        public string? Product { get; set; }
        public int? PurchasedLicenses { get; set; }
        public int? PurchasedArchivedLicenses { get; set; }
        public int? UsedLicenses { get; set; }
        public int? UsedArchivedLicenses { get; set; }
        public DateTime? BeginTs { get; set; }
        public DateTime? EndTs { get; set; }
        public string? Notes { get; set; }
    }
}
