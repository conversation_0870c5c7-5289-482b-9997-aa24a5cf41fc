using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_rt_lapd3_items")]
    public class RTLapd3Item
    {
        [Key]
        [Column("InstID")]
        public long InstId { get; set; }

        [Required]
        [Column("ChildInstID")]
        public long ChildInstId { get; set; }

        [Required]
        [Column("SchoolyearInstID")]
        public long SchoolYearInstId { get; set; }

        [Required]
        [Column("CheckPt")]
        public int CheckPt { get; set; }

        [Required]
        [Column("CheckPtName")]
        [StringLength(30)]
        public string CheckPtName { get; set; } = string.Empty;

        [Column("ChronologicalAge")]
        public int? ChronologicalAge { get; set; }

        [Column("SchoolYear")]
        public int? SchoolYear { get; set; }

        [Required]
        [Column("DomainName")]
        [StringLength(30)]
        public string DomainName { get; set; } = string.Empty;

        [Required]
        [Column("DomainSequence")]
        public int DomainSequence { get; set; }

        [Required]
        [Column("SubscaleStaticID")]
        public long SubscaleStaticId { get; set; }

        [Required]
        [Column("SubscaleName")]
        [StringLength(30)]
        public string SubscaleName { get; set; } = string.Empty;

        [Required]
        [Column("SubscaleID")]
        [StringLength(3)]
        public string SubscaleId { get; set; } = string.Empty;

        [Required]
        [Column("SubscaleSequence")]
        public int SubscaleSequence { get; set; }

        [Required]
        [Column("Language")]
        public int Language { get; set; }

        [Column("RawScore")]
        public short? RawScore { get; set; }

        [Column("Basal")]
        public short? Basal { get; set; }

        [Column("Ceiling")]
        public short? Ceiling { get; set; }

        [Column("ItemCount")]
        public int? ItemCount { get; set; }

        [Required]
        [Column("AssessmentInstID")]
        public long AssessmentInstId { get; set; }

        [Required]
        [Column("AssessmentDate")]
        public DateTime AssessmentDate { get; set; }

        [Required]
        [Column("SubscaleInstID")]
        public long SubscaleInstId { get; set; }

        [Required]
        [Column("SubscaleDate")]
        public DateTime SubscaleDate { get; set; }

        [Column("Percentile")]
        public short? Percentile { get; set; }

        [Column("ZScore", TypeName = "decimal(9,2)")]
        public decimal? ZScore { get; set; }

        [Column("TScore")]
        public short? TScore { get; set; }

        [Column("NCE")]
        public short? NCE { get; set; }

        [Column("AE")]
        [StringLength(7)]
        public string? AE { get; set; }

        [Column("Item1")]
        public short? Item1 { get; set; }

        [Column("Item2")]
        public short? Item2 { get; set; }

        [Column("Item3")]
        public short? Item3 { get; set; }

        [Column("Item4")]
        public short? Item4 { get; set; }

        [Column("Item5")]
        public short? Item5 { get; set; }

        [Column("Item6")]
        public short? Item6 { get; set; }

        [Column("Item7")]
        public short? Item7 { get; set; }

        [Column("Item8")]
        public short? Item8 { get; set; }

        [Column("Item9")]
        public short? Item9 { get; set; }

        [Column("Item10")]
        public short? Item10 { get; set; }

        [Column("Item11")]
        public short? Item11 { get; set; }

        [Column("Item12")]
        public short? Item12 { get; set; }

        [Column("Item13")]
        public short? Item13 { get; set; }

        [Column("Item14")]
        public short? Item14 { get; set; }

        [Column("Item15")]
        public short? Item15 { get; set; }

        [Column("Item16")]
        public short? Item16 { get; set; }

        [Column("Item17")]
        public short? Item17 { get; set; }

        [Column("Item18")]
        public short? Item18 { get; set; }

        [Column("Item19")]
        public short? Item19 { get; set; }

        [Column("Item20")]
        public short? Item20 { get; set; }

        [Column("Item21")]
        public short? Item21 { get; set; }

        [Column("Item22")]
        public short? Item22 { get; set; }

        [Column("Item23")]
        public short? Item23 { get; set; }

        [Column("Item24")]
        public short? Item24 { get; set; }

        [Column("Item25")]
        public short? Item25 { get; set; }

        [Column("Item26")]
        public short? Item26 { get; set; }

        [Column("Item27")]
        public short? Item27 { get; set; }

        [Column("Item28")]
        public short? Item28 { get; set; }

        [Column("Item29")]
        public short? Item29 { get; set; }

        [Column("Item30")]
        public short? Item30 { get; set; }

        [Column("Item31")]
        public short? Item31 { get; set; }

        [Column("Item32")]
        public short? Item32 { get; set; }

        [Column("Item33")]
        public short? Item33 { get; set; }

        [Column("Item34")]
        public short? Item34 { get; set; }

        [Column("Item35")]
        public short? Item35 { get; set; }

        [Column("Item36")]
        public short? Item36 { get; set; }

        [Column("Item37")]
        public short? Item37 { get; set; }

        [Column("Item38")]
        public short? Item38 { get; set; }

        [Column("Item39")]
        public short? Item39 { get; set; }

        [Column("Item40")]
        public short? Item40 { get; set; }

        [Column("Item41")]
        public short? Item41 { get; set; }

        [Column("Item42")]
        public short? Item42 { get; set; }

        [Column("Item43")]
        public short? Item43 { get; set; }

        [Column("Item44")]
        public short? Item44 { get; set; }

        [Column("Item45")]
        public short? Item45 { get; set; }

        [Column("Item46")]
        public short? Item46 { get; set; }

        [Column("Item47")]
        public short? Item47 { get; set; }

        [Column("Item48")]
        public short? Item48 { get; set; }

        [Column("Item49")]
        public short? Item49 { get; set; }

        [Column("Item50")]
        public short? Item50 { get; set; }

        [Column("Item51")]
        public short? Item51 { get; set; }

        [Column("Item52")]
        public short? Item52 { get; set; }

        [Column("Item53")]
        public short? Item53 { get; set; }

        [Column("Item54")]
        public short? Item54 { get; set; }

        [Column("Item55")]
        public short? Item55 { get; set; }

        [Column("Item56")]
        public short? Item56 { get; set; }

        [Column("Item57")]
        public short? Item57 { get; set; }

        [Column("Item58")]
        public short? Item58 { get; set; }

        [Column("Item59")]
        public short? Item59 { get; set; }

        [Column("Item60")]
        public short? Item60 { get; set; }

        [Column("Item61")]
        public short? Item61 { get; set; }

        [Column("Item62")]
        public short? Item62 { get; set; }

        [Column("Item63")]
        public short? Item63 { get; set; }

        [Column("Item64")]
        public short? Item64 { get; set; }

        [Column("Item65")]
        public short? Item65 { get; set; }

        [Column("Item66")]
        public short? Item66 { get; set; }

        [Column("Item67")]
        public short? Item67 { get; set; }

        [Column("Item68")]
        public short? Item68 { get; set; }

        [Column("Item69")]
        public short? Item69 { get; set; }

        [Column("Item70")]
        public short? Item70 { get; set; }

        [Column("Item71")]
        public short? Item71 { get; set; }

        [Column("Item72")]
        public short? Item72 { get; set; }

        [Column("Item73")]
        public short? Item73 { get; set; }

        [Column("Item74")]
        public short? Item74 { get; set; }

        [Column("Item75")]
        public short? Item75 { get; set; }

        [Column("Item76")]
        public short? Item76 { get; set; }

        [Column("Item77")]
        public short? Item77 { get; set; }

        [Column("Item78")]
        public short? Item78 { get; set; }

        [Column("Item79")]
        public short? Item79 { get; set; }

        [Column("Item80")]
        public short? Item80 { get; set; }

        [Column("Item81")]
        public short? Item81 { get; set; }

        [Column("Item82")]
        public short? Item82 { get; set; }

        [Column("Item83")]
        public short? Item83 { get; set; }

        [Column("Item84")]
        public short? Item84 { get; set; }

        [Column("Item85")]
        public short? Item85 { get; set; }

        [Column("Item86")]
        public short? Item86 { get; set; }

        [Column("Item87")]
        public short? Item87 { get; set; }

        [Column("Item88")]
        public short? Item88 { get; set; }

        [Column("Item89")]
        public short? Item89 { get; set; }

        [Column("Item90")]
        public short? Item90 { get; set; }

        [Column("Item91")]
        public short? Item91 { get; set; }

        [Column("Item92")]
        public short? Item92 { get; set; }

        [Column("Item93")]
        public short? Item93 { get; set; }

        [Column("Item94")]
        public short? Item94 { get; set; }

        [Column("Item95")]
        public short? Item95 { get; set; }

        [Column("Item96")]
        public short? Item96 { get; set; }

        [Column("Item97")]
        public short? Item97 { get; set; }

        [Column("Item98")]
        public short? Item98 { get; set; }

        [Column("Item99")]
        public short? Item99 { get; set; }

        [Column("Item100")]
        public short? Item100 { get; set; }

        [Column("Item101")]
        public short? Item101 { get; set; }

        [Column("Item102")]
        public short? Item102 { get; set; }

        [Column("Item103")]
        public short? Item103 { get; set; }

        [Column("Item104")]
        public short? Item104 { get; set; }

        [Column("Item105")]
        public short? Item105 { get; set; }

        [Column("Item106")]
        public short? Item106 { get; set; }

        [Column("Item107")]
        public short? Item107 { get; set; }

        [Column("Item108")]
        public short? Item108 { get; set; }

        [Column("Item109")]
        public short? Item109 { get; set; }

        [Column("Item110")]
        public short? Item110 { get; set; }

        [Column("Item111")]
        public short? Item111 { get; set; }

        [Column("Item112")]
        public short? Item112 { get; set; }

        [Column("Item113")]
        public short? Item113 { get; set; }

        [Column("Item114")]
        public short? Item114 { get; set; }

        [Column("Item115")]
        public short? Item115 { get; set; }

        [Column("Item116")]
        public short? Item116 { get; set; }

        [Column("Item117")]
        public short? Item117 { get; set; }

        [Column("Item118")]
        public short? Item118 { get; set; }

        [Column("Item119")]
        public short? Item119 { get; set; }

        [Column("Item120")]
        public short? Item120 { get; set; }

        [Column("Item121")]
        public short? Item121 { get; set; }

        [Column("Item122")]
        public short? Item122 { get; set; }

        [Column("Item123")]
        public short? Item123 { get; set; }

        [Column("Item124")]
        public short? Item124 { get; set; }

        [Column("Item125")]
        public short? Item125 { get; set; }

        [Column("Item126")]
        public short? Item126 { get; set; }

        [Column("Item127")]
        public short? Item127 { get; set; }

        [Column("Item128")]
        public short? Item128 { get; set; }

        [Column("Item129")]
        public short? Item129 { get; set; }

        [Column("Item130")]
        public short? Item130 { get; set; }

        [Column("Item131")]
        public short? Item131 { get; set; }

        [Column("Item132")]
        public short? Item132 { get; set; }

        [Column("Item133")]
        public short? Item133 { get; set; }

        [Column("Item134")]
        public short? Item134 { get; set; }

        [Column("Item135")]
        public short? Item135 { get; set; }

        [Column("Item136")]
        public short? Item136 { get; set; }

        [Column("Item137")]
        public short? Item137 { get; set; }

        [Column("Item138")]
        public short? Item138 { get; set; }

        [Column("Item139")]
        public short? Item139 { get; set; }

        [Column("Item140")]
        public short? Item140 { get; set; }

        [Column("Item141")]
        public short? Item141 { get; set; }

        [Column("Item142")]
        public short? Item142 { get; set; }

        [Column("Item143")]
        public short? Item143 { get; set; }

        [Column("Item144")]
        public short? Item144 { get; set; }

        [Column("Item145")]
        public short? Item145 { get; set; }

        [Column("Item146")]
        public short? Item146 { get; set; }

        [Column("Item147")]
        public short? Item147 { get; set; }

        [Column("Item148")]
        public short? Item148 { get; set; }

        [Column("Item149")]
        public short? Item149 { get; set; }

        [Column("Item150")]
        public short? Item150 { get; set; }

        [Column("Item151")]
        public short? Item151 { get; set; }

        [Column("Item152")]
        public short? Item152 { get; set; }

        [Column("Item153")]
        public short? Item153 { get; set; }

        [Column("Item154")]
        public short? Item154 { get; set; }

        [Column("Item155")]
        public short? Item155 { get; set; }

        [Column("Item156")]
        public short? Item156 { get; set; }

        [Column("Item157")]
        public short? Item157 { get; set; }

        [Column("Item158")]
        public short? Item158 { get; set; }

        [Column("Item159")]
        public short? Item159 { get; set; }

        [Column("Item160")]
        public short? Item160 { get; set; }

        [Column("Item161")]
        public short? Item161 { get; set; }

        [Column("Item162")]
        public short? Item162 { get; set; }

        [Column("Item163")]
        public short? Item163 { get; set; }

        [Column("Item164")]
        public short? Item164 { get; set; }

        [Column("Item165")]
        public short? Item165 { get; set; }

        [Column("ItemsMastered")]
        public int? ItemsMastered { get; set; }

        [Column("ExpressivePotential")]
        public int? ExpressivePotential { get; set; }

        [Column("ExpressiveMastered")]
        public int? ExpressiveMastered { get; set; }
    }
}

