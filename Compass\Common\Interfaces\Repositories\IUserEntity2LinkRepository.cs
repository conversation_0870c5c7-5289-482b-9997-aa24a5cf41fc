﻿using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IUserEntity2LinkRepository
    {
        public Task<UserEntity2Link> AddUserEntity2LinkAsync(UserEntity2Link userEntity2Link);
        public Task<UserEntity2Link?> GetUserEntity2LinkAsync(long? organizationId, string? userId, long? accessId);
        public Task<bool> RemoveUserEntity2LinkAsync(long? linkId);
    }
}
