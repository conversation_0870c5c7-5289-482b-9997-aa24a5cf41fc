using Compass.C4L.DTOs;
using Compass.C4L.Interfaces.Services;
using Microsoft.Extensions.Configuration;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace Compass.C4L.Services
{
    /// <summary>
    /// Service for interacting with the C4L CMS API
    /// </summary>
    public class C4LCmsApiService : IC4LCmsApiService
    {
        private readonly IConfiguration _configuration;
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl = "http://prod-ae81978a-2b1fac67-i13e6ocx.app.devpanel.com/api/v1";
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// Constructor for the C4L CMS API service
        /// </summary>
        /// <param name="configuration">The application configuration</param>
        /// <param name="httpClientFactory">The HTTP client factory</param>
        public C4LCmsApiService(IConfiguration configuration, IHttpClientFactory httpClientFactory)
        {
            _configuration = configuration;
            _httpClient = httpClientFactory.CreateClient("C4LCmsApi");
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        /// <summary>
        /// Gets an authentication token from the C4L CMS API
        /// </summary>
        /// <returns>The authentication token</returns>
        private async Task<string> GetAuthTokenAsync()
        {
            try
            {
                // Get credentials from configuration
                string username = _configuration["C4LCmsApi:Username"] ?? "api";
                string password = _configuration["C4LCmsApi:Password"] ?? "c4lapi";

                // Create the request content
                object tokenRequest = new
                {
                    username,
                    password
                };

                StringContent content = new StringContent(
                    JsonSerializer.Serialize(tokenRequest),
                    Encoding.UTF8,
                    "application/json");

                // Make the request
                HttpResponseMessage response = await _httpClient.PostAsync($"{_baseUrl}/token", content);
                response.EnsureSuccessStatusCode();

                // Parse the response
                string responseContent = await response.Content.ReadAsStringAsync();
                TokenResponse? tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent, _jsonOptions);

                return tokenResponse?.Token ?? throw new InvalidOperationException("Failed to retrieve token from API");
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error getting authentication token: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets lesson content from the C4L CMS API
        /// </summary>
        /// <param name="language">The language of the lesson content</param>
        /// <param name="unit">The unit number</param>
        /// <param name="week">The week number</param>
        /// <param name="day">The day number</param>
        /// <param name="lessonTypeSequence">The lesson type sequence</param>
        /// <param name="titleSequence">The title sequence</param>
        /// <returns>A DTO containing the lesson content</returns>
        public async Task<C4LLessonContentDto> GetC4LLessonContent(string language, int unit, int week, int day, int lessonTypeSequence, int titleSequence)
        {
            try
            {
                // Get the authentication token
                string token = await GetAuthTokenAsync();

                // Set the authorization header
                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

                // Build the query string
                Dictionary<string, string> queryParams = new Dictionary<string, string>
                {
                    { "language", language },
                    { "unit", unit.ToString() },
                    { "week", week.ToString() },
                    { "day", day.ToString() },
                    { "lesson_type_sequence", lessonTypeSequence.ToString() },
                    { "title_sequence", titleSequence.ToString() }
                };

                string queryString = string.Join("&", queryParams.Select(p => $"{p.Key}={p.Value}"));
                string url = $"{_baseUrl}/lesson-content?{queryString}";

                // Make the request
                HttpResponseMessage response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();

                // Parse the response
                string responseContent = await response.Content.ReadAsStringAsync();
                C4LLessonContentDto? lessonContent = JsonSerializer.Deserialize<C4LLessonContentDto>(responseContent, _jsonOptions);

                return lessonContent ?? new C4LLessonContentDto();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Error getting lesson: Pameters: {language}, {unit}, {week}, {day}, {lessonTypeSequence}, {titleSequence}. content: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Private class for deserializing the token response
        /// </summary>
        private class TokenResponse
        {
            public string Token { get; set; } = string.Empty;
        }
    }
}
