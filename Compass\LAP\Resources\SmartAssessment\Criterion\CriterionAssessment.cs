using Compass.LAP.Resources.Instruments;
using Compass.LAP.Resources.SmartAssessment.Primary;
using LanguageExt;

namespace Compass.LAP.Resources.SmartAssessment.Criterion
{
    public abstract class CriterionAssessment : LapAssessment
    {
        // Constants
        public const int ALL_SUBSCALES_COMPLETE = -1;
        public const int MAX_ITEMS_IN_CEILING = 5;
        public const int MIN_ITEMS_IN_CEILING = 3;
        public const int MAX_DAYS_BETWEEN_SUBSCALES = 15;

        // Private fields
        private bool ongoingAssessment = false;
        private DateTime? latestDate;
        private Checkpoint? previousCheckpoint;

        // Protected fields
        protected Checkpoint? checkpoint;
        protected ICollection<Subscale> subscales = new List<Subscale>();
        protected int? timePeriod;

        // Properties
        public DateTime? LatestDate
        {
            get { return latestDate; }
            set { latestDate = value; }
        }

        public int? TimePeriod
        {
            get { return timePeriod; }
            set { timePeriod = value; }
        }

        public Checkpoint? AssessmentCheckpoint
        {
            get { return checkpoint; }
            set { checkpoint = value; }
        }

        public ICollection<Subscale> Subscales
        {
            get
            {
                if (subscales == null)
                {
                    subscales = new List<Subscale>();
                }
                return subscales;
            }
            set { subscales = value; }
        }

        public bool OngoingAssessment
        {
            get { return ongoingAssessment; }
            set { ongoingAssessment = value; }
        }

        public Checkpoint? PreviousCheckpoint
        {
            get { return previousCheckpoint; }
            set { previousCheckpoint = value; }
        }

        public Subscale? GetSubscale(string? subscaleID)
        {
            foreach (Subscale s in Subscales)
            {
                if (s.SubscaleID != null && s.SubscaleID.Equals(subscaleID))
                {
                    return s;
                }
            }
            return null;
        }

        protected override AssessmentItem AddSkill(Milestone masteredItem)
        {
            AssessmentItem item = base.AddSkill(masteredItem);
            if (item.Checkpoint == null)
            {
                item.Checkpoint = AssessmentCheckpoint;
            }
            Subscale? s = GetSubscale(masteredItem.SubscaleID);
            if (s != null)
            {
                item.SubscaleInstID = s.AssessmentInstID; // Using AssessmentInstID as equivalent to InstID
            }
            return item;
        }

        protected override void UpdateItemBeforeAutoScore(
            AssessmentItem item,
            Skill skill,
            List<Milestone> milestones,
            int? lastItemSequence)
        {
            UpdateSubscale(skill.Date, item, lastItemSequence);
        }

        public virtual void UpdateSubscale(DateTime? selectedDate, AssessmentItem item, int? lastItemSequence)
        {
            // Get the current subscale, create new if empty
            Subscale? s = GetSubscale(item.SubscaleID);
            if (s == null || s.AssessmentInstID == null)
            {
                if (s == null)
                {
                    s = Subscale.Create(item);
                    Subscales.Add(s);
                }

                // Set subscale properties (equivalent to Java version)
                s.SchoolYear = item.SchoolYear; // Assuming Assessment has SchoolYear property
                s.Domain = item.Domain;
                s.SubscaleID = item.SubscaleID;
                s.SubscaleName = item.Subscale;
                s.DomainSequence = item.DomainSequence;
                s.Sequence = item.SubscaleSequence;
                s.DateOfAssessment = selectedDate;
                s.Instrument = item.Instrument;
                s.CheckpointValue = item.Checkpoint;
                // s.Child = item.Child; // Excluded per memory instructions

                if (s.ChildInstID == null)
                {
                    s.ChildInstID = item.ChildInstID;
                }
            }
            else
            {
                // s.EntityStatus = EntityStatus.MODIFIED; // Would need EntityStatus implementation
            }

            BasalCeilingCalculator cc = CreateBasalCeilingCalculator(item, lastItemSequence);
            int? assessmentLevel = item.Instrument;
            int? subscaleSequence = item.SubscaleSequence;

            if (!Subscale.IsDualLanguage(assessmentLevel, subscaleSequence))
            {
                cc.FindBasal();
                cc.FindCeiling();

                if (cc.IsBasalFound())
                {
                    s.Basal = cc.Basal;
                }
                else
                {
                    s.Basal = null;
                }

                if (cc.IsCeilingFound())
                {
                    s.Ceiling = cc.Ceiling;
                    s.RawScore = cc.RawScore;
                    // ClearAboveCeiling();
                }
                else
                {
                    s.Ceiling = null;
                    s.RawScore = null;
                }
            }
            else
            {
                cc.FindRawScore();
                s.RawScore = cc.RawScore;
            }
        }

        protected virtual BasalCeilingCalculator CreateBasalCeilingCalculator(AssessmentItem item, int? lastItemSequence)
        {
            int basalItemNumber = GetNumberOfItemsForBasal();
            List<AssessmentItem> itemCol = GetAssessmentItemsBySubscale(item.SubscaleID).ToList();

            return new BasalCeilingCalculator(basalItemNumber, itemCol, lastItemSequence);
        }

        protected virtual void ClearAboveCeiling()
        {
            List<AssessmentItem> updatedItems = new List<AssessmentItem>();

            foreach (AssessmentItem item in AssessmentItems)
            {
                Subscale? s = GetSubscale(item.SubscaleID);
                if (s != null && s.Ceiling != null && item.Sequence <= s.Ceiling)
                {
                    updatedItems.Add(item);
                }
            }
            SetAssessmentItems(updatedItems, true);
        }

        public override Milestone GetNextItem(Milestone previousItem, int? previousScore, ICollection<Milestone> milestones)
        {
            Subscale? sub = GetSubscale(previousItem.SubscaleID);
            int? assessmentLevel = sub?.Instrument;
            int? subscaleSequence = sub?.Sequence;
            bool isDLL = Subscale.IsDualLanguage(assessmentLevel, subscaleSequence);
            List<Milestone> milestonesList = milestones.ToList();
            int idx = milestonesList.IndexOf(previousItem);

            if (previousScore == Skill.MASTERED || isDLL)
            {
                idx++;
            }
            else
            {
                if (sub?.Basal != null)
                {
                    idx++;
                }
                else
                {
                    idx += GetNoBasalItemRetreat() ?? 0;
                }
                idx = idx < 0 ? 0 : idx;
            }

            // The only time this will be triggered is if the THEN block of the statement above
            // So we have done idx++
            if (idx == milestones.Count)
            {
                // If the idx is == to the item count, we are past the available items.
                // Move back x.
                if ((sub == null || sub.Basal == null) && !isDLL)
                {
                    idx += GetNoBasalItemRetreat() ?? 0;
                }
                else
                {
                    // Hard return. Because if we go down to the next set or it will try to retreat
                    return milestonesList[idx - 1];
                }
            }

            Milestone ret = milestonesList[idx];

            // Logic to check if we need to skip the "next" answered item
            // if current item has been answered and it's a +, move to next.
            AssessmentItem item = new AssessmentItem();
            item.ItemID = ret.ItemID;
            if (AssessmentItems.Contains(item))
            {
                List<AssessmentItem> items = AssessmentItems.ToList();
                item = items[items.IndexOf(item)];
                // always skip if mastered
                if (item.Value == Skill.MASTERED)
                {
                    ret = GetNextItem(ret, item.Value, milestones);
                }
                // if item is emerging but don't have a basal or ceiling, move to next.
                else if (item.Scored && (sub == null || sub.Ceiling == null) && item.Value == Skill.EMERGING)
                {
                    ret = GetNextItem(ret, item.Value, milestones);
                }
            }

            return ret;
        }

        public bool HasBasalCeiling(Milestone milestone)
        {
            bool ret = false;
            Subscale? s = GetSubscale(milestone.SubscaleID);
            if (s != null && s.Basal != null && s.Ceiling != null)
            {
                ret = true;
            }
            return ret;
        }

        public bool IsItemInBasal(Milestone milestone)
        {
            int? assessmentLevel = milestone.Instrument;
            int? subscaleSequence = milestone.SubscaleSequence;
            if (Subscale.IsDualLanguage(assessmentLevel, subscaleSequence))
            {
                return false;
            }

            bool ret = false;
            Subscale? s = GetSubscale(milestone.SubscaleID);
            if (s?.Basal != null)
            {
                int basal = s.Basal.Value;
                if (milestone.Sequence >= basal &&
                    milestone.Sequence <= basal + GetNumberOfItemsForBasal() - 1)
                {
                    ret = true;
                }
            }
            return ret;
        }

        public bool IsItemInCeiling(Milestone milestone)
        {
            int? assessmentLevel = milestone.Instrument;
            int? subscaleSequence = milestone.SubscaleSequence;
            if (Subscale.IsDualLanguage(assessmentLevel, subscaleSequence))
            {
                return false;
            }

            bool ret = false;
            Subscale? s = GetSubscale(milestone.SubscaleID);
            if (s != null && s.Ceiling != null)
            {
                int ceiling = s.Ceiling.Value;
                if (milestone.Sequence == ceiling)
                {
                    ret = true;
                }
            }
            return ret;
        }

        public bool IsItemPotentiallyInCeiling(Milestone milestone)
        {
            bool ret = false;
            Subscale? s = GetSubscale(milestone.SubscaleID);
            if (s?.Ceiling != null)
            {
                int ceiling = s.Ceiling.Value;
                if (milestone.Sequence <= ceiling &&
                    milestone.Sequence >= ceiling - (MAX_ITEMS_IN_CEILING - 1))
                {
                    ret = true;
                }
            }
            return ret;
        }

        public bool IsComplete(ICollection<CompletedAssessment> completed)
        {
            bool ret = false;
            int count = 0;
            foreach (CompletedAssessment ca in completed)
            {
                if (ca.Checkpoint?.GetValue() == AssessmentCheckpoint?.GetValue())
                {
                    count++;
                }
            }
            if (count == GetTotalSubscales())
            {
                ret = true;
            }
            return ret;
        }

        public bool IsBeyondTimeLimit(ICollection<CompletedAssessment> completed)
        {
            bool ret = false;

            DateTime? dt = null;
            foreach (CompletedAssessment ca in completed)
            {
                if (ca.Checkpoint?.GetValue() == AssessmentCheckpoint?.GetValue())
                {
                    dt = ca.DateOfAssessment;
                    break;
                }
            }

            if (dt != null)
            {
                if (DateOfAssessment != null)
                {
                    int days = Math.Abs((dt.Value - (DateTime)DateOfAssessment).Days);
                    if (days > MAX_DAYS_BETWEEN_SUBSCALES)
                    {
                        ret = true;
                    }
                }
            }
            return ret;
        }

        public bool HasCompletedCheckpoint(ICollection<CompletedAssessment> completed)
        {
            bool ret = false;
            // Collect all the completed subscales by checkpoint
            Dictionary<Checkpoint, List<CompletedAssessment>?> summary = new Dictionary<Checkpoint, List<CompletedAssessment>?>();

            foreach (CompletedAssessment ca in completed)
            {
                Checkpoint? cp = ca.Checkpoint;

                if (cp != null)
                {
                    List<CompletedAssessment>? complete = summary[(Checkpoint)cp];
                    if (complete == null)
                    {
                        complete = new List<CompletedAssessment>(10);
                    }
                    complete.Add(ca);
                    summary[(Checkpoint)cp] = complete;
                }
            }

            foreach (List<CompletedAssessment>? total in summary.Values)
            {
                if (total != null)
                {
                    if (total.Count == GetTotalSubscales())
                    {
                        ret = true;
                        break;
                    }
                }
            }
            return ret;
        }

        public override Milestone GetStartingPoint(ICollection<Milestone> milestones)
        {
            List<Milestone> milestonesList = milestones.ToList();
            int startMonths = 0;
            int preStartMonths = 0;
            int i = milestonesList.Count - 1;
            Milestone? milestone = null;

            // Only DLL milestones OR elap SHOULD have a months of 0
            if (milestonesList[0].Months != 0 || milestonesList[0].Instrument == AssessmentLevel.E_LAP)
            {
                // If our age is over the required months for the first item
                if (ChronologicalAge > milestonesList[0].Months)
                {
                    // Step backwards through the list of milestones
                    // I'm assuming this finds the first one that our age is under (or that seems to be what it is doing)
                    while (true)
                    {
                        // If this milestone has a required months less then our age
                        if (milestonesList[i].Months <= ChronologicalAge)
                        {
                            // Set our start months to the items months
                            startMonths = milestonesList[i].Months ?? 0;
                            // Account for children who are not quite 1 month old.
                            if (startMonths == 0 && i == 0)
                            {
                                i = -1;
                                break;
                            }
                        }
                        // If months have changed again, we need to get the last item.
                        // Or, if startMonths has been found but we are at the first item, we need to get
                        // out of the loop and select the first item.
                        if (startMonths != preStartMonths && preStartMonths != 0 ||
                            startMonths != 0 && i == 0)
                        {
                            if (preStartMonths > ChronologicalAge)
                            {
                                i = i == 0 ? -1 : i;
                            }
                            break;
                        }
                        preStartMonths = startMonths;
                        i--;
                    }
                    milestone = milestonesList[i + 1];
                }
                else
                {
                    milestone = milestonesList[0];
                }
            }
            else
            {
                milestone = milestonesList[0];
            }
            return milestone;
        }

        public override Milestone? FindFirstItem(ICollection<Milestone> milestones)
        {
            Milestone startingpoint = GetStartingPoint(milestones);
            Milestone milestone = startingpoint;
            List<Milestone> milestonesList = milestones.ToList();

            int? i;

            if (milestone == null || milestone.Sequence == 0)
            {
                i = -1;
            }
            else
            {
                i = milestone.Sequence - 1;
            }

            // Have initial starting point. However, if items aren't null, we need
            // to find the first unanswered item or first emerging skill. That becomes the
            // starting point.

            if (milestone != null)
            {
                List<AssessmentItem> items = GetAssessmentItemsBySubscale(milestone.SubscaleID).ToList();

                if (items.Count > 0)
                {
                    bool hasItems = false;
                    foreach (AssessmentItem itm in items)
                    {
                        if (itm.ObservationDate == null)
                        {
                            i = itm.Sequence ?? 0;
                            hasItems = true;
                            break;
                        }
                    }

                    if (hasItems)
                    {
                        Subscale? s = GetSubscale(milestone.SubscaleID);
                        if (s != null && s.Basal != null)
                        {
                            AssessmentItem itm = new AssessmentItem();
                            itm.ItemID = s.SubscaleID + s.Basal.ToString();

                            int index = items.IndexOf(itm);

                            // This if statement was added because of a bug where the
                            // subscales basal somehow got set to an item that was not
                            // scored. This has caused many issues. This if statement will
                            // set the basal to null allowing the user to fix the issue in app
                            if (index > -1)
                            {
                                AssessmentItem item = items[index];
                                i = item.Sequence ?? 0;
                            }
                            else
                            {
                                s.Basal = null;
                            }
                        }

                        for (; i < milestonesList.Count + 1; i++)
                        {
                            if (i - 1 < milestonesList.Count)
                            {
                                Milestone m = milestonesList[(int)i - 1];
                                AssessmentItem ai = new AssessmentItem();
                                ai.ItemID = m.ItemID;

                                AssessmentItem? tempItem = GetItemBasedOnId(m.ItemID, items);

                                if (tempItem == null || tempItem.Value == Skill.EMERGING && tempItem.ObservationDate == null)
                                {
                                    milestone = m;
                                    break;
                                }
                            }
                        }
                    }

                    bool hasEmerging = HasEmergingItems(items);

                    if (milestone.Equals(startingpoint) && subscales != null && subscales.Any() &&
                        items.Any() && !hasEmerging)
                    {
                        Subscale? subscale = GetSubscale(milestone.SubscaleID);

                        if (subscale != null && subscale.AssessmentInstID == null)
                        {
                            if (subscale.Basal != null)
                            {
                                milestone = FindCrossScoreStartingPoint(subscale, milestone, items, milestones);
                            }
                        }
                    }
                }
            }

            return milestone;
        }

        private Milestone FindCrossScoreStartingPoint(Subscale subscale, Milestone milestone, List<AssessmentItem> items, ICollection<Milestone> milestones)
        {
            AssessmentItem? startingItem = null;

            foreach (AssessmentItem itm in items)
            {
                if (itm.Sequence == subscale.Basal)
                {
                    startingItem = itm;
                    break;
                }
            }

            Milestone? ret = null;

            if (startingItem != null)
            {
                AssessmentItem item = startingItem;
                AssessmentItem? prevItem = null;

                while (!item.Equals(prevItem))
                {
                    prevItem = item;
                    foreach (AssessmentItem tempItem in items)
                    {
                        if (tempItem.Sequence == item.Sequence + 1)
                        {
                            item = tempItem;
                            break;
                        }
                    }
                }

                foreach (Milestone tempMilestone in milestones)
                {
                    if (tempMilestone.Sequence == item.Sequence + 1)
                    {
                        ret = tempMilestone;
                    }
                }
            }

            return ret ?? milestone;
        }

        private bool HasEmergingItems(List<AssessmentItem> items)
        {
            foreach (AssessmentItem item in items)
            {
                if (item.Value == Skill.EMERGING)
                {
                    return true;
                }
            }
            return false;
        }

        protected virtual AssessmentItem? GetItemBasedOnId(string itemID, List<AssessmentItem> items)
        {
            foreach (AssessmentItem ai in items)
            {
                if (ai.ItemID != null && ai.ItemID.Equals(itemID))
                {
                    return ai;
                }
            }
            return null;
        }

        public bool IsBetweenBasalAndCeiling(Milestone milestone)
        {
            int? assessmentLevel = milestone.Instrument;
            int? subscaleSequence = milestone.SubscaleSequence;
            if (Subscale.IsDualLanguage(assessmentLevel, subscaleSequence))
            {
                return false;
            }

            bool ret = false;
            Subscale? s = GetSubscale(milestone.SubscaleID);
            if (s?.Ceiling != null && s.Basal != null)
            {
                int ceiling = s.Ceiling.Value;
                int basal = s.Basal.Value;
                if (milestone.Sequence <= ceiling && milestone.Sequence >= basal)
                {
                    ret = true;
                }
            }
            return ret;
        }

        // Abstract methods that subclasses must implement
        public abstract int GetNumberOfItemsForBasal();
        public abstract int? GetTotalSubscales();
        public abstract int? GetNoBasalItemRetreat();

        protected override void CopyAdditionalData(LapAssessment result)
        {
            AssessmentCheckpoint = ((CriterionAssessment)result).AssessmentCheckpoint;
        }
    }
}
