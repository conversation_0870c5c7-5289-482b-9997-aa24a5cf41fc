﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Common.Models
{
    [Table("cmn_user_entity_2_links")]
    public class UserEntity2Link
    {
        [Key]
        public long Id { get; set; }
        [Column("organization_id")]
        public long? OrganizationId { get; set; }
        [Column("mod_id")]
        public string? ModId { get; set; }
        [Column("mod_ts")]
        public DateTime ModTs { get; set; }
        [Column("link_status")]
        public string? LinkStatus { get; set; }
        [Column("user_role")]
        public string? UserRole { get; set; }
        [Column("aspnetuser_id")]
        public string? UserId { get; set; }
        [Column("cmn_user_entity_2_access_id")]
        public long? Entity2UserAccessId { get; set; }
    }
}
