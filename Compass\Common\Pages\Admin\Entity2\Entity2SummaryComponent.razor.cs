﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity2
{
    public partial class Entity2SummaryComponent : IDisposable
    {
        private string contactName = string.Empty;
        private string email = string.Empty;
        private string phone = string.Empty;

        private List<Compass.Common.Models.LicensePool> licensePoolPage = new();

        private int maxPages;
        private int currentPage;

        private long? organizationId;
        private bool isLoading = true;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private string entityName = string.Empty;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                organizationId = commonSessionData.CurrentOrganizationId;
                long? entity2Id = commonSessionData.CurrentEntity2Id;
                Compass.Common.Models.Entity2? currentEntity2 = await Entity2Repository.GetEntity2Async(entity2Id);

                if (currentEntity2 != null)
                {
                    string contactFirstName = currentEntity2.ContactFirstName ?? string.Empty;
                    string contactLastName = currentEntity2.ContactLastName ?? string.Empty;
                    contactName = contactFirstName + " " + contactLastName;
                    entityName = commonSessionData.SelectedEntityName;

                    email = currentEntity2.ContactEmail ?? string.Empty;
                    phone = currentEntity2.ContactPhone ?? string.Empty;

                    currentPage = 1;
                    maxPages = 0;

                    await GetLicensePoolPage();
                }
            }
        }

        private async Task GetLicensePoolPage()
        {
            isLoading = true;
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;

            KaplanPageable<Compass.Common.Models.LicensePool> currentPage = await LicensePoolService.GetLicensePoolSummaryPages(pageQuery, organizationId);

            licensePoolPage = currentPage.PageContent;
            maxPages = currentPage.MaxPages;
            isLoading = false;

            StateHasChanged();
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetLicensePoolPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetLicensePoolPage();
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
