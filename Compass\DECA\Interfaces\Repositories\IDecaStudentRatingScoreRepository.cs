﻿﻿using Compass.Deca.Models;

namespace Compass.DECA.Interfaces.Repositories
{
    public interface IDecaStudentRatingScoreRepository
    {
        /// <summary>
        /// Gets a student rating score by ID
        /// </summary>
        /// <param name="id">The ID of the rating score</param>
        /// <returns>The DecaStudentRatingScore object if found, null otherwise</returns>
        Task<DecaStudentRatingScore?> GetByIdAsync(long id);
        
        /// <summary>
        /// Gets all rating scores for a specific student rating
        /// </summary>
        /// <param name="edecaStudentRatingId">The ID of the student rating</param>
        /// <returns>A list of DecaStudentRatingScore objects</returns>
        Task<List<DecaStudentRatingScore>> GetByStudentRatingIdAsync(long edecaStudentRatingId);
        
        /// <summary>
        /// Gets all rating scores for a specific student
        /// </summary>
        /// <param name="studentId">The ID of the student</param>
        /// <returns>A list of DecaStudentRatingScore objects</returns>
        Task<List<DecaStudentRatingScore>> GetByStudentIdAsync(long studentId);
        
        /// <summary>
        /// Gets rating scores by question number for a specific student rating
        /// </summary>
        /// <param name="edecaStudentRatingId">The ID of the student rating</param>
        /// <param name="questionNumber">The question number</param>
        /// <returns>The DecaStudentRatingScore object if found, null otherwise</returns>
        Task<DecaStudentRatingScore?> GetByStudentRatingIdAndQuestionNumberAsync(long edecaStudentRatingId, int questionNumber);
        
        /// <summary>
        /// Creates a new student rating score
        /// </summary>
        /// <param name="ratingScore">The rating score to create</param>
        /// <returns>The created rating score</returns>
        Task<DecaStudentRatingScore> CreateAsync(DecaStudentRatingScore ratingScore);
        
        /// <summary>
        /// Updates an existing student rating score
        /// </summary>
        /// <param name="id">The ID of the rating score to update</param>
        /// <param name="ratingScore">The updated rating score data</param>
        /// <returns>The updated rating score if successful, null otherwise</returns>
        Task<DecaStudentRatingScore?> UpdateAsync(long id, DecaStudentRatingScore ratingScore);
        
        /// <summary>
        /// Deletes a student rating score
        /// </summary>
        /// <param name="id">The ID of the rating score to delete</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> DeleteAsync(long id);
        
        /// <summary>
        /// Deletes all rating scores for a specific student rating
        /// </summary>
        /// <param name="edecaStudentRatingId">The ID of the student rating</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> DeleteByStudentRatingIdAsync(long edecaStudentRatingId);
    }
}
