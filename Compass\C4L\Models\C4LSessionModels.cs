namespace Compass.C4L.Models
{
    /// <summary>
    /// Data transfer object for C4L navigation context
    /// </summary>
    public class C4LNavigationContext
    {
        /// <summary>
        /// The current curriculum unit
        /// </summary>
        public int CurrentUnit { get; set; } = 1;

        /// <summary>
        /// The current curriculum week
        /// </summary>
        public int CurrentWeek { get; set; } = 1;

        /// <summary>
        /// The current calendar week number (since classroom start date)
        /// </summary>
        public string CurrentCalendarUnitWeeks { get; set; } = string.Empty;

        /// <summary>
        /// The current Monday date for the displayed week
        /// </summary>
        public DateTime CurrentMondayDate { get; set; } = DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek + (int)DayOfWeek.Monday);

        /// <summary>
        /// The classroom start date
        /// </summary>
        public DateTime ClassroomStartDate { get; set; } = DateTime.Today;

        /// <summary>
        /// The classroom ID
        /// </summary>
        public long? C4L_ClassroomId { get; set; }

        /// <summary>
        /// The Non-Contact Day ID
        /// </summary>
        public long? NonContactDayId { get; set; } = null;
    }

    /// <summary>
    /// Data transfer object for C4L lesson details
    /// </summary>
    public class C4LLessonContext
    {
        /// <summary>
        /// The selected lesson title
        /// </summary>
        public string SelectedLessonTitle { get; set; } = string.Empty;

        /// <summary>
        /// The selected lesson type
        /// </summary>
        public string SelectedLessonType { get; set; } = string.Empty;

        /// <summary>
        /// The selected lesson unit
        /// </summary>
        public int SelectedLessonUnit { get; set; } = 1;

        /// <summary>
        /// The selected lesson week
        /// </summary>
        public int SelectedLessonWeek { get; set; } = 1;

        /// <summary>
        /// The selected lesson day
        /// </summary>
        public int SelectedLessonDay { get; set; } = 1;

        /// <summary>
        /// The selected lesson type sequence
        /// </summary>
        public int SelectedLessonTypeSequence { get; set; } = 0;

        /// <summary>
        /// The selected lesson title sequence
        /// </summary>
        public int SelectedLessonTitleSequence { get; set; } = 0;

        /// <summary>
        /// The selected lesson language
        /// </summary>
        public string SelectedLessonLanguage { get; set; } = "English";
    }

    /// <summary>
    /// Combined C4L session data
    /// </summary>
    public class C4LSessionData
    {
        /// <summary>
        /// Navigation context data
        /// </summary>
        public C4LNavigationContext NavigationContext { get; set; } = new();

        /// <summary>
        /// Lesson context data
        /// </summary>
        public C4LLessonContext LessonContext { get; set; } = new();
    }
}
