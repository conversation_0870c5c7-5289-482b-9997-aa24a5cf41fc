﻿using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Student;
using Compass.Common.DTOs.StudentGroup;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Compass.Common.Models;
using Compass.Common.Resources;

namespace Compass.Common.Services
{
    public class StudentService : IStudentService
    {
        private readonly IStudentRepository _studentRepository;
        private readonly IStudentGroupRepository _studentGroupRepository;
        private readonly IStudentGroupRosterRepository _studentGroupRosterRepository;
        private readonly ISchoolYearRepository _schoolYearRepository;
        public StudentService(IStudentRepository studentRepository,
                                IStudentGroupRepository studentGroupRepository,
                                IStudentGroupRosterRepository studentGroupRosterRepository,
                                ISchoolYearRepository schoolYearRepository)
        {
            _studentRepository = studentRepository;
            _studentGroupRepository = studentGroupRepository;
            _studentGroupRosterRepository = studentGroupRosterRepository;
            _schoolYearRepository = schoolYearRepository;
        }

        private async Task<Student> CreateStudentAsync(Student student)
        {
            Student createdStudent = await _studentRepository.CreateStudentAsync(student);

            return createdStudent;
        }

        private async Task<Student?> UpdateStudentAsync(long? id, Student student)
        {
            Student? updatedStudent = await _studentRepository.UpdateStudentAsync(id, student);

            return updatedStudent;
        }

        private bool RaceLinkMatches(StudentRaceLink newLink, StudentRaceLink existingLink)
        {
            return newLink.StudentId == existingLink.StudentId &&
                    newLink.StudentRaceId == existingLink.StudentRaceId &&
                    newLink.OrganizationId == existingLink.OrganizationId;
        }

        private async Task SaveStudentRaceLinks(List<StudentRaceLink> raceLinkList, long studentId)
        {
            List<StudentRaceLink> existingRaceLinks = await _studentRepository.GetStudentRaceLinkList(studentId);
            List<StudentRaceLink> removeRaceLinks = new List<StudentRaceLink>();

            //Set Student Ids for links
            foreach (StudentRaceLink link in raceLinkList)
            {
                link.StudentId = studentId;
            }

            //Check for links to remove
            foreach (StudentRaceLink existingLink in existingRaceLinks)
            {
                bool matchFound = raceLinkList.Any(newLink => RaceLinkMatches(newLink, existingLink));

                //If the new link list does not have this existing link then it must be removed
                if (!matchFound)
                {
                    removeRaceLinks.Add(existingLink);
                }
            }

            //Hanlde inserts
            foreach (StudentRaceLink newLink in raceLinkList)
            {
                StudentRaceLink? existingLink = existingRaceLinks.FirstOrDefault(rl => RaceLinkMatches(newLink, rl));

                //Link does not exist thus must be added
                if (existingLink == null)
                {
                    await _studentRepository.CreateStudentRaceLink(newLink);
                }
            }

            //Handle Deletes
            if (removeRaceLinks.Count > 0)
            {
                await _studentRepository.DeleteStudentRaceLinks(removeRaceLinks);
            }
        }

        private async Task CreateRoster(StudentGroupRoster? roster, long? studentId, long? organizationId, StudentGroup group, SchoolYear currentSchoolYear)
        {
            long schoolYearId = currentSchoolYear.Id;
            int? schoolYear = currentSchoolYear.SchoolYearValue;

            if (roster == null)
            {
                roster = new();
            }
            roster.OrganizationId = organizationId;
            roster.SiteId = group.SiteId;
            roster.SchoolYearId = schoolYearId;
            roster.SchoolYear = schoolYear;
            roster.LinkStatus = CompassResource.LinkStatus_Active;

            roster.StudentGroupId = group.Id;
            roster.StudentId = studentId;

            long rosterId = roster.Id;
            if (rosterId > 0)
            {
                await _studentGroupRosterRepository.UpdateStudentGroupRosterAsync(rosterId, roster);
            }
            else
            {
                await _studentGroupRosterRepository.CreateStudentGroupRosterAsync(roster);
            }
        }

        public async Task AssignToStudentGroup(long? studentId, long? organizationId, long? studentGroupId)
        {
            if (studentId != null && studentGroupId != null && organizationId != null)
            {
                StudentGroup? group = await _studentGroupRepository.GetStudentGroupAsync(studentGroupId);

                if (group != null && group.SiteId != null)
                {
                    long? siteId = group.SiteId;
                    SchoolYear? currentSchoolYear = await _schoolYearRepository.GetCurrentSchoolYear(organizationId, siteId);

                    if (currentSchoolYear != null)
                    {
                        long schoolYearId = currentSchoolYear.Id;
                        int? schoolYear = currentSchoolYear.SchoolYearValue;

                        StudentGroupRoster? roster = await _studentGroupRosterRepository.GetStudentGroupRosterAsync(organizationId, schoolYearId, studentGroupId, studentId);

                        await CreateRoster(roster, studentId, organizationId, group, currentSchoolYear);
                    }
                    else
                    {
                        throw new Exception("No current School Year for Site");
                    }
                }
                else
                {
                    throw new Exception("Student Group does not exist");
                }
            }
        }

        public async Task<Student?> SaveStudentAsync(SaveStudentAction action)
        {
            Student student = action.Student;
            long studentId = student.Id;

            Student? savedStudent;
            if (studentId > 0)
            {
                savedStudent = await UpdateStudentAsync(studentId, student);
            }
            else
            {
                savedStudent = await CreateStudentAsync(student);
                studentId = savedStudent.Id;
            }

            // save race list
            await SaveStudentRaceLinks(action.RaceLinkList, studentId);

            long? organizationId = student.OrganizationId;
            // assign to classroom
            await AssignToStudentGroup(studentId, organizationId, action.StudentGroupId);

            return savedStudent;
        }

        public async Task<StudentFormFieldsDto> GetStudentFormFields()
        {
            List<StudentLanguage> StudentLanguageList = await _studentRepository.GetStudentLanguages();
            List<StudentRace> StudentRaceList = await _studentRepository.GetStudentRaces();

            StudentFormFieldsDto ret = new();
            ret.StudentLanguageList = StudentLanguageList;
            ret.StudentRaceList = StudentRaceList;

            return ret;
        }

        public async Task<Student?> GetStudentAsync(long? id)
        {
            Student? student = await _studentRepository.GetStudentAsync(id);
            return student;
        }

        public async Task<KaplanPageable<StudentDisplayDto>> GetStudentPage(StudentListAction action)
        {
            List<StudentDisplayDto> studentList = await _studentRepository.GetStudentList(action);

            long? organizationId = action.OrganizationId;

            int studentCount = await _studentRepository.GetStudentCount(organizationId);

            PageQuery pageQuery = action.PageQuery;
            int pageSize = pageQuery.PageSize;

            int maxPages = (int)Math.Ceiling((double)studentCount / pageSize);

            KaplanPageable<StudentDisplayDto> pageable = new KaplanPageable<StudentDisplayDto>();
            pageable.PageContent = studentList;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<List<StudentRaceLink>> GetStudentRaceLinks(long? studentId)
        {
            List<StudentRaceLink> linkList = await _studentRepository.GetStudentRaceLinkList(studentId);
            return linkList;
        }

        public async Task<KaplanPageable<StudentGroupListDisplayDto>> GetAssignedStudentGroupPage(AssignStudentGroupListAction action)
        {
            List<StudentGroupListDisplayDto> studentGroupList = await _studentGroupRepository.GetAssignedStudentGroupList(action);

            long? organizationId = action.OrganizationId;
            long? studentId = action.StudentId;

            PageQuery pageQuery = action.PageQuery;
            int pageSize = pageQuery.PageSize;

            int studentGroupCount = await _studentGroupRepository.GetAssignedStudentGroupCount(organizationId, studentId, pageQuery.QueryText);

            int maxPages = (int)Math.Ceiling((double)studentGroupCount / pageSize);

            KaplanPageable<StudentGroupListDisplayDto> pageable = new KaplanPageable<StudentGroupListDisplayDto>();
            pageable.PageContent = studentGroupList;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<KaplanPageable<StudentGroupListDisplayDto>> GetUnAssignedStudentGroupPage(AssignStudentGroupListAction action)
        {
            List<StudentGroupListDisplayDto> studentGroupList = await _studentGroupRepository.GetUnAssignedStudentGroupList(action);

            long? organizationId = action.OrganizationId;
            long? studentId = action.StudentId;

            PageQuery pageQuery = action.PageQuery;
            int pageSize = pageQuery.PageSize;

            int studentGroupCount = await _studentGroupRepository.GetUnAssignedStudentGroupCount(organizationId, studentId, pageQuery.QueryText);

            int maxPages = (int)Math.Ceiling((double)studentGroupCount / pageSize);

            KaplanPageable<StudentGroupListDisplayDto> pageable = new KaplanPageable<StudentGroupListDisplayDto>();
            pageable.PageContent = studentGroupList;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task RemoveFromStudentGroup(long? studentId, long? organizationId, long? studentGroupId)
        {
            if (studentId == null)
            {
                throw new ArgumentNullException(nameof(studentId));
            }

            if (organizationId == null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (studentGroupId == null)
            {
                throw new ArgumentNullException(nameof(studentGroupId));
            }

            bool result = await _studentGroupRosterRepository.RemoveStudentGroupRosterAsync(studentId, organizationId, studentGroupId);

            if (!result)
            {
                throw new Exception("Failed to remove student from student group. Roster entry not found.");
            }
        }
    }
}
