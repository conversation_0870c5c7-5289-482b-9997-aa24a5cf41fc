﻿@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject IEntity3Service Entity3Service;
@inject UserAccessor UserAccessor
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

@if (entity3 is not null)
{
    <EditForm Model="entity3" FormName="formEntity3" OnValidSubmit="SubmitAsync" class="c4l-form entity3-add-form">
        <h3 class="c4l-form-heading">@Localizer["lbl_Add"] @entity3Name</h3>

        <DataAnnotationsValidator></DataAnnotationsValidator>
        <ValidationSummary></ValidationSummary>

        <div>
            <label class="col-form-label" for="entity3-name">
                @Localizer["lbl_Name"]
                <InputText 
                    @bind-Value="entity3.Name" 
                    class="form-control mt-2"
                    id="entity3-name" 
                    aria-required="true"
                />
            </label>
            <ValidationMessage For="() => entity3.Name" class="mt-2 text-danger" />
        </div>

        <div>
            <label class="col-form-label" for="entity3-address1">
                @Localizer["lbl_Address1"]
                <InputText 
                    @bind-Value="entity3.Address1" 
                    class="form-control mt-2"
                    id="entity3-address1" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity3-address2">
                @Localizer["lbl_Address2"]
                <InputText 
                    @bind-Value="entity3.Address2" 
                    class="form-control mt-2"
                    id="entity3-address2" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity3-city">
                @Localizer["lbl_City"]
                <InputText 
                    @bind-Value="entity3.City" 
                    class="form-control mt-2"
                    id="entity3-city" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity3-state">
                @Localizer["lbl_State"]
                <InputSelect 
                    @bind-Value="entity3.State" 
                    id="entity3-state" 
                >
                    <option disabled value="">-- @Localizer["lbl_State"] --</option>
                    @foreach (var stateCode in CompassResource.UsStates)
                    {
                        <option value="@stateCode">@stateCode</option>
                    }
                </InputSelect>
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity3-zipcode">
                @Localizer["lbl_ZipCode"]
                <InputText 
                    @bind-Value="entity3.ZipCode" 
                    class="form-control mt-2"
                    id="entity3-zipcode" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity3-contact-firstname">
                @Localizer["lbl_ContactFirstName"]
                <InputText 
                    @bind-Value="entity3.ContactFirstName" 
                    class="form-control mt-2"
                    id="entity3-contact-firstname" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity3-contact-lastname">
                @Localizer["lbl_ContactLastName"]
                <InputText 
                    @bind-Value="entity3.ContactLastName" 
                    class="form-control mt-2"
                    id="entity3-contact-lastname" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity3-contact-email">
                @Localizer["lbl_ContactEmail"]
                <InputText 
                    @bind-Value="entity3.ContactEmail" 
                    class="form-control mt-2"
                    id="entity3-contact-email" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity3-contact-phone">
                @Localizer["lbl_ContactPhone"]
                <InputText 
                    @bind-Value="entity3.ContactPhone" 
                    class="form-control mt-2"
                    id="entity3-contact-phone" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity3-contact-fax">
                @Localizer["lbl_ContactFax"]
                <InputText 
                    @bind-Value="entity3.ContactFax" 
                    class="form-control mt-2"
                    id="entity3-contact-fax" 
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity3-fax">
                @Localizer["lbl_Fax"]
                <InputText 
                    @bind-Value="entity3.Fax" 
                    class="form-control mt-2"
                    id="entity3-fax" 
                />
            </label>
        </div>

        <div class="form-submit-buttons-wrapper">
            <button class="c4l-button c4l-secondary-button c4l-form-button" type="submit">@Localizer["lbl_Save"]</button>
        </div>

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success mt-4" role="alert">
                <p class="text-center font-weight-600 mb-0">@successMessage</p>
            </div>
        }
    </EditForm>
}
