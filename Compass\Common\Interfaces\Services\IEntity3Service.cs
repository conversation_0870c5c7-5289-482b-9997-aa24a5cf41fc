﻿using Compass.Common.DTOs.Entity3;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Services
{
    public interface IEntity3Service
    {
        public Task<Entity3> CreateEntity3Async(Entity3 entity3);
        public Task<KaplanPageable<Entity3ListDisplayDto>> GetEntity3Page(Entity3ListAction action);
        public Task<DeleteReturnDto> DeleteEntity3(long? organizationId, long? entity1Id, long? entity2Id, long? entity3Id);
        public Task<UserEntity3Link> AssignEntity3User(CreateUserLinkAction action);
        public Task<bool> UnAssignEntity3User(CreateUserLinkAction action);
        public Task<int> GetEntity3Count(long orgId);
        public Task<List<Entity3>> GetEntities3Async(long? organizationId, long? entity2Id);
    }
}
