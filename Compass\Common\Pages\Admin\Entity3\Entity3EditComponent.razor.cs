﻿using Compass.Common.Data;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity3
{
    public partial class Entity3EditComponent : IDisposable
    {
        private long? entity3Id;
        private Compass.Common.Models.Entity3? entity3 { get; set; }

        private string? successMessage = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();

            if (commonSessionData is not null)
            {
                this.entity3Id = commonSessionData.CurrentEntity3Id;
                if (this.entity3Id > 0)
                {
                    this.entity3 = await Entity3Repository.GetEntity3Async(this.entity3Id);
                }
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        protected async Task SubmitAsync()
        {
            if (this.entity3 is not null)
            {
                successMessage = string.Empty;

                CommonSessionData? commonSessionData = await GetCommonSessionData();
                if (commonSessionData is not null)
                {
                    this.entity3.OrganizationId = commonSessionData.CurrentOrganizationId;
                    this.entity3.Entity1Id = commonSessionData.CurrentEntity1Id;
                    this.entity3.Entity2Id = commonSessionData.CurrentEntity2Id;

                    this.entity3 = await Entity3Repository.UpdateEntity3Async(this.entity3Id, this.entity3);

                    if (this.entity3 != null)
                    {
                        commonSessionData.SelectedEntityName = this.entity3.Name;
                    }

                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();

                    successMessage = "Information updated successfully!";
                }
            }
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
