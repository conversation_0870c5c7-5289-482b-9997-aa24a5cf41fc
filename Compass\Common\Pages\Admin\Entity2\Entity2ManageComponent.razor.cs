﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity2
{
    public partial class Entity2ManageComponent
    {
        private bool IsDeleteDialogVisible = false;
        private bool IsDisplayMessageVisible = false;

        private string EntityName = string.Empty;
        private string DialogMessage = string.Empty;
        private string DisplayMessage = string.Empty;
        private string Entity2Hierarchy = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            IsDeleteDialogVisible = false;
            IsDisplayMessageVisible = false;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                EntityName = commonSessionData.SelectedEntityName;
                DialogMessage = "Are you sure you want to delete " + EntityName + "?";
                Entity2Hierarchy = commonSessionData.Entity2Hierarchy;
            }
        }

        protected void OnDeleteSelected()
        {
            IsDeleteDialogVisible = true;
        }

        protected async Task OnDeleteDialogResult(bool result)
        {
            if (result)
            {
                await DeleteEntity2();
            }
            else
            {
                IsDeleteDialogVisible = false;
            }
        }

        protected void OnDisplayMessageResult()
        {
            IsDisplayMessageVisible = false;
        }

        private async Task DeleteEntity2()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                long? currentOrgId = commonSessionData.CurrentOrganizationId;
                long? currentEntity1Id = commonSessionData.CurrentEntity1Id;
                long? currentEntity2Id = commonSessionData.CurrentEntity2Id;

                DeleteReturnDto deleteReturnDto = await Entity2Service.DeleteEntity2(currentOrgId, currentEntity1Id, currentEntity2Id);

                IsDeleteDialogVisible = false;

                if (deleteReturnDto.Success)
                {
                    commonSessionData.ResetCurrentIdValues();

                    if (_currentUser != null)
                    {
                        await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                        await CommonSessionDataObserver.BroadcastStateChangeAsync();
                    }

                    NavigationManager.NavigateTo($"/entity2list");
                }
                else
                {
                    DisplayMessage = deleteReturnDto.Message;
                    IsDisplayMessageVisible = true;
                }
            }
        }
    }
}
