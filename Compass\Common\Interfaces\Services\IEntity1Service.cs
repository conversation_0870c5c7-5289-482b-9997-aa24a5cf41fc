﻿using Compass.Common.DTOs.Entity1;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Services
{
    public interface IEntity1Service
    {
        public Task<Entity1> CreateEntity1Async(Entity1 entity1);
        public Task<KaplanPageable<Entity1ListDisplayDto>> GetEntity1Page(Entity1ListAction action);
        public Task<DeleteReturnDto> DeleteEntity1(long? organizationId, long? entity1Id);
        public Task<UserEntity1Link> AssignEntity1User(CreateUserLinkAction action);
        public Task<bool> UnAssignEntity1User(CreateUserLinkAction action);
        public Task<int> GetEntity1Count(long orgId);
        Task<List<Entity1>> GetEntities1Async(long? organizationId);
    }
}
