﻿@page "/account/generate-recovery-codes"

@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity

@inject UserManager<ApplicationUser> UserManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager
@inject ILogger<GenerateRecoveryCodes> Logger

<PageTitle>Generate Two-Factor Authentication (2FA) Recovery Codes | C4L</PageTitle>

@if (recoveryCodes is not null)
{
    <ShowRecoveryCodes RecoveryCodes="recoveryCodes.ToArray()" StatusMessage="@message" />
}
else
{
    <form @formname="generate-recovery-codes" @onsubmit="OnSubmitAsync" method="post" class="c4l-form">
        <AntiforgeryToken />

        <h3 class="h4 c4l-form-heading">Generate two-factor authentication (2FA) recovery codes</h3>

        <div class="alert alert-warning" role="alert">
            <strong>Put these codes in a safe place.</strong>

            <p class="mt-3">If you lose your device and don't have the recovery codes <em>you will lose access to your account.</em></p>

            <span>Generating new recovery codes does not change the keys used in authenticator apps. If you wish to change the key used in an authenticator app you should <a class="font-weight-500" href="account/reset-authenticator-key">reset your authenticator keys.</a></span>
        </div>

        <button class="c4l-button c4l-form-button c4l-secondary-button" type="submit">Generate Recovery Codes</button>
    </form>
}

@code {
    private string? message;
    private ApplicationUser user = default!;
    private IEnumerable<string>? recoveryCodes;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);

        var isTwoFactorEnabled = await UserManager.GetTwoFactorEnabledAsync(user);
        if (!isTwoFactorEnabled)
        {
            throw new InvalidOperationException("Cannot generate recovery codes for user because they do not have 2FA enabled.");
        }
    }

    private async Task OnSubmitAsync()
    {
        var userId = await UserManager.GetUserIdAsync(user);
        recoveryCodes = await UserManager.GenerateNewTwoFactorRecoveryCodesAsync(user, 10);
        message = "You have generated new recovery codes.";

        Logger.LogInformation("User with ID '{UserId}' has generated new 2FA recovery codes.", userId);
    }
}
