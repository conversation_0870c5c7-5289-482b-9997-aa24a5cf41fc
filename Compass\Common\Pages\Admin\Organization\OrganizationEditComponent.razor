﻿@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject NavigationManager NavigationManager
@inject IOrganizationService OrganizationService
@inject UserAccessor UserAccessor
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject CultureService CultureService

<EditForm Model="Input" FormName="formOrganization" OnValidSubmit="OnValidateSubmit" class="c4l-form organization-edit-form">
    <h3 class="c4l-form-heading">@Localizer["lbl_EditOrganization"]</h3>

    <DataAnnotationsValidator />
    <ValidationSummary />

    <div>
        <label class="col-form-label" for="organizationName">
            @Localizer["lbl_OrganizationName"]
            <InputText 
                @bind-Value="Input.Name" 
                id="organizationName" 
                class="form-control mt-2"
                aria-required="true"
                placeholder="@Localizer["lbl_OrganizationName"]" 
            />
        </label>
        <ValidationMessage For="() => Input.Name" class="mt-2 text-danger" />
    </div>

    <div>
        <label class="col-form-label" for="organizationContactEmail">
            @Localizer["lbl_ContactEmail"]
            <InputText 
                @bind-Value="Input.ContactEmail" 
                id="organizationContactEmail" 
                class="form-control mt-2"
                placeholder="@Localizer["lbl_ContactEmail"]" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="organizationContactFirstName">
            @Localizer["lbl_ContactFirstName"]
            <InputText 
                @bind-Value="Input.ContactFirstName" 
                id="organizationContactFirstName" 
                class="form-control mt-2"
                placeholder="@Localizer["lbl_ContactFirstName"]" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="organizationContactLastName">
            @Localizer["lbl_ContactLastName"]
            <InputText 
                @bind-Value="Input.ContactLastName" 
                id="organizationContactLastName" 
                class="form-control mt-2"
                placeholder="@Localizer["lbl_ContactLastName"]" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="organizationPhoneNumber">
            @Localizer["lbl_ContactPhone"]
            <InputText 
                @bind-Value="Input.ContactPhoneNumber" 
                id="organizationPhoneNumber" 
                class="form-control mt-2"
                placeholder="@Localizer["lbl_ContactPhone"]" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="organizationAddress1">
            @Localizer["lbl_Address1"]
            <InputText 
                @bind-Value="Input.Address1" 
                id="organizationAddress1" 
                class="form-control mt-2"
                placeholder="@Localizer["lbl_Address1"]" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="organizationAddress2">
            @Localizer["lbl_Address2"]
            <InputText 
                @bind-Value="Input.Address2" 
                id="organizationAddress2" 
                class="form-control mt-2"
                placeholder="@Localizer["lbl_Address2"]" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="organizationCity">
            @Localizer["lbl_City"]
            <InputText 
                @bind-Value="Input.City" 
                id="organizationCity" 
                class="form-control mt-2"
                placeholder="@Localizer["lbl_City"]" 
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="organizationState">
            @Localizer["lbl_State"]
            <InputSelect 
                @bind-Value="Input.State" 
                id="organizationState" 
            >
                <option disabled value="">-- @Localizer["lbl_State"] --</option>
                @foreach (var stateCode in CompassResource.UsStates)
                {
                    <option value="@stateCode">@stateCode</option>
                }
            </InputSelect>
            <ValidationMessage For="() => Input.State" />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="organizationZipCode">
            @Localizer["lbl_ZipCode"]
            <InputText 
                @bind-Value="Input.ZipCode" 
                id="organizationZipCode" 
                class="form-control mt-2"
                placeholder="@Localizer["lbl_ZipCode"]" 
            />
        </label>
    </div>

    <div>
        <label class="form-label form-checkbox-label d-flex mt-2">
            @Localizer["lbl_MFARequired"]
            <InputCheckbox 
                @bind-Value="Input.MfaRequired" 
                class="darker-border-checkbox form-check-input" 
            />
        </label>
    </div>

    <div class="form-submit-buttons-wrapper">
        <button class="c4l-button c4l-form-button c4l-secondary-button" type="submit">@Localizer["lbl_Update"]</button>
    </div>

    @if (showSuccessMessage)
    {
        <div class="alert alert-success fade show mt-4" role="alert">
            <p class="text-center font-weight-600 mb-0">@successMessage</p>
        </div>
    }
</EditForm>
