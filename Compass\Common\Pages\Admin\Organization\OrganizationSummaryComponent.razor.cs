﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Organization
{
    public partial class OrganizationSummaryComponent : IDisposable
    {
        private string contactName = string.Empty;
        private string email = string.Empty;
        private string phone = string.Empty;

        private List<Compass.Common.Models.LicensePool> licensePoolPage = new();

        private int maxPages;
        private int currentPage;

        private long? organizationId;
        private bool isLoading = true;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private string organizationName = string.Empty;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                organizationId = commonSessionData.CurrentOrganizationId;
                Compass.Common.Models.Organization? currentOrganization = await OrganizationRepository.GetOrganizationAsync(organizationId);

                if (currentOrganization != null)
                {
                    organizationName = commonSessionData.CurrentOrganizationName;
                    string contactFirstName = currentOrganization.ContactFirstName ?? string.Empty;
                    string contactLastName = currentOrganization.ContactLastName ?? string.Empty;
                    contactName = contactFirstName + " " + contactLastName;

                    email = currentOrganization.ContactEmail ?? string.Empty;
                    phone = currentOrganization.ContactPhone ?? string.Empty;

                    currentPage = 1;
                    maxPages = 0;

                    await GetLicensePoolPage();
                }
            }
        }

        private async Task GetLicensePoolPage()
        {
            isLoading = true;
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;

            KaplanPageable<Compass.Common.Models.LicensePool> currentPage = await LicensePoolService.GetLicensePoolSummaryPages(pageQuery, organizationId);

            licensePoolPage = currentPage.PageContent;
            maxPages = currentPage.MaxPages;
            isLoading = false;
            StateHasChanged();
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetLicensePoolPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetLicensePoolPage();
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);
            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
