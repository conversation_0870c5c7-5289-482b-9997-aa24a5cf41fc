﻿using Compass.Common.DTOs.Entity1;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Compass.Common.Models;
using Compass.Common.Resources;

namespace Compass.Common.Services
{
    public class Entity1Service : IEntity1Service
    {
        private readonly IEntity1Repository _entity1Repository;
        private readonly IEntity2Repository _entity2Repository;
        private readonly IEntity3Repository _entity3Repository;
        private readonly ISiteRepository _siteRepository;
        private readonly IOrganizationHierarchyRepository _organizationHierarchyRepository;
        private readonly IUserEntity1AccessRepository _userEntity1AccessRepository;
        private readonly IUserEntity1LinkRepository _userEntity1LinkRepository;
        private readonly IUserRepository _userRepository;

        public Entity1Service(IEntity1Repository entity1Repository,
                                IEntity2Repository entity2Repository,
                                IEntity3Repository entity3Repository,
                                ISiteRepository siteRepository,
                                IOrganizationHierarchyRepository organizationHierarchyRepository,
                                IUserEntity1AccessRepository userEntity1AccessRepository,
                                IUserEntity1LinkRepository userEntity1LinkRepository,
                                IUserRepository userRepository)
        {
            _entity1Repository = entity1Repository;
            _entity2Repository = entity2Repository;
            _entity3Repository = entity3Repository;
            _siteRepository = siteRepository;
            _organizationHierarchyRepository = organizationHierarchyRepository;
            _userEntity1AccessRepository = userEntity1AccessRepository;
            _userEntity1LinkRepository = userEntity1LinkRepository;
            _userRepository = userRepository;
        }

        public async Task<Entity1> CreateEntity1Async(Entity1 entity1)
        {
            Entity1 createdEntity1 = await _entity1Repository.CreateEntity1Async(entity1);

            UserEntity1Access entity1UserAccess = new UserEntity1Access();
            entity1UserAccess.OrganizationId = entity1.OrganizationId;
            entity1UserAccess.Entity1Id = entity1.Id;
            entity1UserAccess.CanAdd = "Y";
            entity1UserAccess.CanUpdate = "Y";
            entity1UserAccess.CanDelete = "Y";
            entity1UserAccess.CanView = "Y";
            entity1UserAccess.CanAssign = "Y";

            await _userEntity1AccessRepository.AddUserEntity1AccessAsync(entity1UserAccess);

            Entity2 entity2 = new Entity2();
            entity2.OrganizationId = entity1.OrganizationId;
            entity2.Entity1Id = entity1.Id;
            entity2.Name = "Default";
            entity2.IsDeleted = "D";
            Entity2 entity2Result = await _entity2Repository.CreateEntity2Async(entity2);

            Entity3 entity3 = new Entity3();
            entity3.OrganizationId = entity1.OrganizationId;
            entity3.Entity1Id = entity1.Id;
            entity3.Entity2Id = entity2Result.Id;
            entity3.Name = "Default";
            entity3.IsDeleted = "D";
            await _entity3Repository.CreateEntity3Async(entity3);

            return createdEntity1;
        }

        public async Task<KaplanPageable<Entity1ListDisplayDto>> GetEntity1Page(Entity1ListAction action)
        {
            List<Entity1ListDisplayDto> entity1List = await _entity1Repository.GetEntity1List(action);


            PageQuery pageQuery = action.pageQuery;
            int pageSize = pageQuery.PageSize;

            int entity1Count = await _entity1Repository.GetEntity1Count(action.organizationId, pageQuery.QueryText);

            int maxPages = (int)Math.Ceiling((double)entity1Count / pageSize);

            KaplanPageable<Entity1ListDisplayDto> pageable = new KaplanPageable<Entity1ListDisplayDto>();
            pageable.PageContent = entity1List;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<int> GetEntity1Count(long orgId)
        {
            int count = await _entity1Repository.GetEntity1Count(orgId, string.Empty);
            return count;
        }

        private async Task<bool> ValidateHasUsers(long? organizationId, long? entity1Id)
        {
            int userCount = await _userRepository.GetEntity1UserCount(organizationId, entity1Id, string.Empty);

            if (userCount > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task<DeleteReturnDto> DeleteEntity1(long? organizationId, long? entity1Id)
        {
            DeleteReturnDto ret = new();
            ret.Success = true;

            OrganizationHierarchy? hierarchy = await _organizationHierarchyRepository.GetOrganizationHierarchyAsync(organizationId);

            if (hierarchy != null)
            {
                if (hierarchy.HierarchyEntity2EntityName != string.Empty)
                {
                    // validate entity2
                    int entity2Count = await _entity2Repository.GetEntity2Count(organizationId, entity1Id, "");

                    if (entity2Count > 0)
                    {
                        ret.Success = false;
                        ret.Message = nameof(CommonResource.err_Entity1HasActiveEntity2s);
                    }
                }
                else if (hierarchy.HierarchySiteEntityName != string.Empty)
                {
                    int siteCount = await _siteRepository.GetSiteCount(organizationId, entity1Id, null, null, string.Empty);

                    if (siteCount > 0)
                    {
                        ret.Success = false;
                        ret.Message = nameof(CommonResource.err_Entity1HasActiveSites);
                    }
                }

            }
            else
            {
                throw new Exception("Organization Hierarchy not set up");
            }

            // validate users
            bool hasUsers = await ValidateHasUsers(organizationId, entity1Id);
            if (hasUsers)
            {
                ret.Success = false;
                ret.Message = nameof(CommonResource.err_UsersAssigned);
            }

            //If valid then delete
            if (ret.Success)
            {
                bool success = await _entity1Repository.DeleteEntity1(entity1Id);
                ret.Success = success;
            }

            return ret;
        }

        public async Task<UserEntity1Link> AssignEntity1User(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            long? entity1Id = action.EntityId;

            UserEntity1Access? userEntity1Access = await _userEntity1AccessRepository.GetUserEntity1AccessAsync(organizationId, entity1Id);

            if (userEntity1Access is null)
            {
                throw new Exception("No Entity 1 Access Found");
            }

            UserEntity1Link createdLink = new UserEntity1Link();
            createdLink.OrganizationId = organizationId;
            createdLink.Entity1UserAccessId = userEntity1Access.Id;
            createdLink.UserId = action.UserId;
            createdLink.LinkStatus = CompassResource.LinkStatus_Active;
            createdLink.UserRole = "TEST"; // TODO need to figure out user roles

            UserEntity1Link newLink = await _userEntity1LinkRepository.AddUserEntity1LinkAsync(createdLink);

            return newLink;
        }

        public async Task<bool> UnAssignEntity1User(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            long? entity1Id = action.EntityId;
            UserEntity1Access? userEntity1Access = await _userEntity1AccessRepository.GetUserEntity1AccessAsync(organizationId, entity1Id);

            if (userEntity1Access is null)
            {
                throw new Exception("No Entity 1 Access Found");
            }

            string? userId = action.UserId;
            long? accessId = userEntity1Access.Id;

            UserEntity1Link? removeLink = await _userEntity1LinkRepository.GetUserEntity1LinkAsync(organizationId, userId, accessId);

            if (removeLink is null)
            {
                throw new Exception("No Link Found");
            }

            long? linkId = removeLink.Id;
            bool result = await _userEntity1LinkRepository.RemoveUserEntity1LinkAsync(linkId);

            return result;
        }

        public async Task<List<Entity1>> GetEntities1Async(long? organizationId)
        {
            if (organizationId == null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            List<Entity1> ret = await _entity1Repository.GetEntities1Async(organizationId);
            return ret;
        }
    }
}
