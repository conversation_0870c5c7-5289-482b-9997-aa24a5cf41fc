﻿@page "/account/personal-data"
@inject IdentityUserAccessor UserAccessor

<PageTitle>Manage Personal Data | C4L</PageTitle>

<h2 class="text-center">Manage Personal Data</h2>
<p class="text-center personal-data-subheading">Your account contains personal data that you have given us. This page allows you to download or delete that data.</p>

<div class="manage-data-form-wrapper d-flex">
    <form action="Account/Manage/DownloadPersonalData" method="post">
        <AntiforgeryToken />

        <div class="form-submit-buttons-wrapper personal-data-buttons-wrapper">
            <button class="c4l-button c4l-primary-button" type="submit">Download</button>
        </div>
    </form>

    <NavLink class="c4l-button c4l-danger-button" href="/account/delete-data">Delete</NavLink>
</div>

@code {
    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        _ = await UserAccessor.GetRequiredUserAsync(HttpContext);
    }
}
