.page-content {
  padding-block: clamp(1rem, -0.156rem + 4.931vw, 3rem);
  padding-inline: clamp(1.5rem, 1.211rem + 1.233vw, 2rem);
}

.d-flex {
  display: flex;
}

.d-grid {
  display: grid;
}

.d-block {
  display: block;
}

.d-none {
  display: none;
}

.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

:where(.c4l-search-table-wrapper) {
  margin-block: 6rem 2rem;
  width: min(100%, 1600px);
  margin-inline: auto;
}

:where(.search-input-wrapper) {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 0.75rem;
  margin-block-end: 1.75rem;
}

:where(.input-wrapper) {
  position: relative;
  width: 100%;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    z-index: -1;
    background-color: transparent;
    border-radius: 0.25rem;
    box-shadow: 0 0 6px hsl(278 37.3% 90%);
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
  }

  &:has(.form-control:active) {
    &::before {
      opacity: 1;
    }
  }

  &:has(.form-control:focus) {
    &::before {
      opacity: 1;
    }
  }
}

:where(.component-content-wrapper) {
  width: min(100%, 1600px);
  margin-block: 5rem;
  margin-inline: auto;
  overflow-y: auto;
  padding-block-end: 1rem;
}

:where(.c4l-table-scroll-wrapper) {
  overflow: auto;
  padding-block-end: 1rem;
}

:where(.search-buttons-wrapper) {
  display: flex;
  gap: 0.5rem;

  & .c4l-button {
    width: 100%;
  }
}

:where(.c4l-table-wrapper) {
  display: flex;
  position: relative;
  flex-direction: column;
  background-color: var(--white);
  border: 1px solid var(--c4l-primary-purple);
  border-radius: 0.25rem;
  overflow: auto;
  min-width: 850px;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background-color: transparent;
    border-radius: 0.25rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
    z-index: -1;
  }

  &:hover {
    &::before {
      opacity: 1;
    }
  }

  &.org-summary-table-wrapper {
    min-width: 1550px;
  }

  &.organization-users-wrapper {
    min-width: 1375px;
  }

  &.entity1-users-wrapper,
  &.entity2-users-wrapper,
  &.entity3-users-wrapper,
  &.sitelist-users-wrapper,
  &.grouplist-users-wrapper,
  &.site-wrapper {
    min-width: 1300px;
  }

  &.student-group-wrapper,
  &.users-wrapper {
    min-width: 1100px;
  }

  & .c4l-table-headings-wrapper {
    --c4l-table-heading-bg: hsl(278 37.3% 87.5%);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
    padding: 1rem;
    border-radius: 0.25rem 0.25rem 0 0;
    background-color: var(--c4l-table-heading-bg);
    text-align: left;
  }

  & .c4l-table-heading {
    font-weight: 600;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    max-width: 92.5%;
  }

  & .c4l-table-result-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
    align-items: center;
    padding: 1rem;
    transition: background-color var(--transition-speed) ease;
    background-color: var(--white);
    border-block-start: 1px solid var(--c4l-primary-purple);
    text-align: left;

    & button {
      display: flex;
      justify-content: center;
      gap: 0.5rem;
      width: fit-content;
    }
  }

  & .c4l-table-result-item {
    color: hsl(218 44.7% 24.1%);
    margin-block: 0;
    transition: color var(--transition-speed) ease;
    max-width: 92.5%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

  &.has-links {
    & button {
      text-decoration: none;
      transition: background-color var(--transition-speed) ease;
      padding: 0;
      background-color: var(--white);
      border: none;
      text-align: left;

      &:active,
      &:focus {
        & .c4l-table-result-wrapper {
          background-color: hsl(189 90.2% 92.5%);
        }
      }

      &:last-of-type {
        border-radius: 0 0 0.25rem 0.25rem;
      }
    }
  }

  &.has-license-info {
    & .c4l-table-title-wrapper {
      background-color: var(--c4l-primary-purple);
      padding: 1rem;
    }

    & .c4l-table-title {
      font-size: clamp(1.25rem, 1.146rem + 0.442vw, 1.5rem);
      color: var(--white);
    }

    & .c4l-table-headings-wrapper {
      border-radius: 0;
    }
  }
}

.page-count-wrapper {
  margin-block-start: 1rem;
}

:where(.status-message-wrapper) {
  width: min(100%, 45rem);
  margin-inline: auto;
  text-align: center;

  & .alert {
    font-weight: 500;
    margin-block-end: 1.25rem;
  }
}

.component-heading {
  margin-block: 1.5rem;
}

.external-logins-section {
  margin-block: 4rem;
}

:where(.c4l-pagination-wrapper) {
  margin-block-start: 1rem;
}

:where(.info-message-wrapper) {
  --info-text-color: hsl(236.4, 68.6%, 23.7%);
  border-radius: 0.25rem;
  padding-inline: 1rem;
  padding-block: 0.5rem;
  background-color: hsl(190 89.7% 87.5%);
  color: var(--info-text-color);
  padding: 1rem;
  border-inline-start: 0.375rem solid var(--info-text-color);

  & a {
    font-weight: 500;
    color: var(--info-text-color);
  }
}

@media (min-width: 48rem) {
  :where(.search-input-wrapper) {
    flex-direction: row;
  }

  :where(.search-buttons-wrapper) {
    & .c4l-button {
      width: fit-content;
    }
  }
}

@media (min-width: 75rem) {
  :where(.c4l-table-wrapper) {
    & .c4l-table-result-wrapper {
      justify-content: space-between;
      gap: 0;
    }

    &.has-links {
      & button {
        &:hover {
          & .c4l-table-result-wrapper {
            background-color: hsl(189 90.2% 92.5%);
          }

          & .c4l-table-result-item {
            font-weight: 500;
            color: var(--c4l-primary-purple);
          }
        }
      }
    }
  }
}
