﻿using Compass.Common.Data;
using Compass.Common.DTOs.Student;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Data.Common;
using System.Runtime.CompilerServices;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class StudentRepository : IStudentRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public StudentRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<Student> CreateStudentAsync(Student student)
        {
            // Get the authentication state
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            student.ModId = userId;
            student.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.Students.AddAsync(student);
                await _dbContext.SaveChangesAsync();
            }

            return student;
        }

        public async Task<Student?> UpdateStudentAsync(long? id, Student student)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (student is null)
            {
                throw new ArgumentNullException(nameof(student));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                Student? existingStudent = await _dbContext.Students.FindAsync(id);

                if (existingStudent is null)
                {
                    return null;
                }

                // Get the authentication state
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                existingStudent.ModId = userId;
                existingStudent.ModTs = DateTime.Now;

                // Update student properties
                existingStudent.FirstName = student.FirstName;
                existingStudent.MiddleName = student.MiddleName;
                existingStudent.LastName = student.LastName;
                existingStudent.SchoolId = student.SchoolId;
                existingStudent.Gender = student.Gender;
                existingStudent.BirthDate = student.BirthDate;
                existingStudent.EnrollDate = student.EnrollDate;
                existingStudent.HispanicLatino = student.HispanicLatino;
                existingStudent.PrimaryLanguageId = student.PrimaryLanguageId;
                existingStudent.SchoolLunch = student.SchoolLunch;
                existingStudent.DualLanguage = student.DualLanguage;
                existingStudent.IepIfsp = student.IepIfsp;
                existingStudent.Disability = student.Disability;

                _dbContext.Students.Update(existingStudent);
                await _dbContext.SaveChangesAsync();

                return existingStudent;
            }
        }

        public async Task<List<StudentLanguage>> GetStudentLanguages()
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.StudentLanguages.ToListAsync();
            }
        }

        public async Task<List<StudentRace>> GetStudentRaces()
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.StudentRaces.ToListAsync();
            }
        }

        public async Task<List<StudentRaceLink>> GetStudentRaceLinkList(long? studentId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<StudentRaceLink> raceLinkList = await _dbContext.StudentRaceLinks.Where(rl => rl.StudentId == studentId).ToListAsync();
                return raceLinkList;
            }
        }

        public async Task<StudentRaceLink> CreateStudentRaceLink(StudentRaceLink link)
        {
            // Get the authentication state
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            link.ModId = userId;
            link.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.StudentRaceLinks.AddAsync(link);
                await _dbContext.SaveChangesAsync();
            }

            return link;
        }

        public async Task DeleteStudentRaceLinks(List<StudentRaceLink> linkList)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                _dbContext.StudentRaceLinks.RemoveRange(linkList);
                await _dbContext.SaveChangesAsync();
            }
        }

        public async Task<Student?> GetStudentAsync(long? id)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Students.FirstOrDefaultAsync(o => o.Id == id);
            }
        }

        public async Task<List<StudentDisplayDto>> GetStudentList(StudentListAction action)
        {
            var results = new List<StudentDisplayDto>();
            PageQuery pageQuery = action.PageQuery;
            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", action.UserId),
                new SqlParameter("@OrganizationId", action.OrganizationId),
                new SqlParameter("@SearchCriteria", pageQuery.QueryText),
                new SqlParameter("@PageOffset", pageQuery.GetOffset()),
                new SqlParameter("@PageSize", pageQuery.PageSize)
            };

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                // Use ADO.NET directly for more control
                using DbCommand command = _dbContext.Database.GetDbConnection().CreateCommand();
                command.CommandText = "cmn_accessible_student_get_list";
                command.CommandType = CommandType.StoredProcedure;

                foreach (var param in parameters)
                {
                    command.Parameters.Add(param);
                }

                // Ensure connection is open
                if (command.Connection.State != ConnectionState.Open)
                {
                    await command.Connection.OpenAsync();
                }

                try
                {
                    using var reader = await command.ExecuteReaderAsync();

                    // Check if we have any rows
                    if (!reader.HasRows)
                    {
                        return results; // Return empty list
                    }

                    while (await reader.ReadAsync())
                    {
                        try
                        {
                            StudentDisplayDto student = new StudentDisplayDto
                            {
                                // Use GetOrdinal to find column positions and safely get values
                                Id = reader.GetInt64(reader.GetOrdinal("id")),
                                FirstName = reader.IsDBNull(reader.GetOrdinal("FirstName"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("FirstName")),
                                LastName = reader.IsDBNull(reader.GetOrdinal("LastName"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("LastName")),
                                BirthDate = reader.IsDBNull(reader.GetOrdinal("BirthDate"))
                                    ? null
                                    : reader.GetDateTime(reader.GetOrdinal("BirthDate")),
                                SchoolId = reader.IsDBNull(reader.GetOrdinal("SchoolId"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("SchoolId"))
                            };

                            results.Add(student);
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue processing other rows
                            System.Diagnostics.Debug.WriteLine($"Error processing row: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error executing stored procedure: {ex.Message}");
                    throw; // Rethrow the exception after logging
                }
            }

            return results;
        }

        public async Task<int> GetStudentCount(long? organizationId)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", userId),
                new SqlParameter("@OrganizationId", organizationId)
            };

            string sqlQuery = "EXEC [cmn_accessible_student_get_count] @UserId, @OrganizationId";

            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, parameters);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<int> results = await _dbContext.Database
                                .SqlQuery<int>(formattedQuery)
                                .ToListAsync();

                int count = results.FirstOrDefault();

                return count;
            }
        }
    }
}
