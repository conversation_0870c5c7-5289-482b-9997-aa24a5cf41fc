﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Resources;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;

namespace Compass.DECA.Pages.Summary
{
    public partial class DECA_StudentSummary : IDisposable
    {
        [Inject]
        public required NavigationManager NavigationManager { get; set; }
        [Inject]
        public required UserManager<ApplicationUser> UserManager { get; set; }
        [Inject]
        public required AuthenticationStateProvider AuthenticationStateProvider { get; set; }
        [Inject]
        public required UserSessionService UserSessionService { get; set; }
        [Inject]
        public required UserAccessor UserAccessor { get; set; }
        [Inject]
        public required IStringLocalizer<CommonResource> Localizer { get; set; }
        [Inject]
        public required CurrentCultureObserver CurrentLanguageObserver { get; set; }
        [Inject]
        public required CultureService CultureService { get; set; }

        private bool isLoading = true;
        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private long? _currentStudentId;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            isLoading = true;
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);
            CommonSessionData? commonSessionData = await GetCommonSessionData();

            if (commonSessionData is not null)
            {
                _currentStudentId = commonSessionData.CurrentStudentId;
                isLoading = false;
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }

        private void OnDECARatingClick()
        {
            NavigationManager.NavigateTo($"/deca-student-ratings");
        }

        private void OnDECAContactsClick()
        {
            NavigationManager.NavigateTo($"/deca-student-contacts");
        }
    }
}
