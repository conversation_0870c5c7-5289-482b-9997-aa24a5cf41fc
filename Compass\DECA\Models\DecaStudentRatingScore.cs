﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Deca.Models
{
    [Table("edeca_student_rating_scores")]
    public class DecaStudentRatingScore
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long Id { get; set; }

        [Column("mod_id")]
        [MaxLength(450)]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime? ModTs { get; set; }

        [Column("organization_id")]
        [Required]
        public long OrganizationId { get; set; }

        [Column("student_id")]
        [Required]
        public long StudentId { get; set; }

        [Column("edeca_student_rating_id")]
        [Required]
        public long EdecaStudentRatingId { get; set; }

        [Column("edeca_student_rating_score")]
        [Required]
        public int EdecaStudentRatingScore { get; set; }

        [Column("edeca_student_rating_score_date")]
        [Required]
        [DataType(DataType.Date)]
        public DateOnly EdecaStudentRatingScoreDate { get; set; }

        [Column("question_number")]
        [Required]
        public int QuestionNumber { get; set; }
    }
}