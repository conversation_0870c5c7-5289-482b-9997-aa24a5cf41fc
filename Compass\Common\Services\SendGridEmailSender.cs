﻿using Compass.Common.Data;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace Compass.Common.Services
{
    public class SendGridEmailSender(IOptions<AuthMessageSenderOptions> optionsAccessor,
    ILogger<SendGridEmailSender> logger) : IEmailSender<ApplicationUser>
    {
        private readonly ILogger logger = logger;
        private readonly string _apiKey;

        public AuthMessageSenderOptions Options { get; } = optionsAccessor.Value;

        private async Task HandleEmailSend(ApplicationUser user, string email, string subject, string plainTextContent, string htmlContent)
        {
            string? sendGridApiKey = Options.EmailAuthKey;
            //string sendGridApiKey = configuration["SendGridSettings:ApiKey"];
            SendGridClient client = new SendGridClient(sendGridApiKey);
            EmailAddress from = new EmailAddress("<EMAIL>", subject);
            EmailAddress to = new EmailAddress(email);
            SendGridMessage msg = MailHelper.CreateSingleEmail(from, to, subject, plainTextContent, htmlContent);
            await client.SendEmailAsync(msg);
        }

        public async Task SendConfirmationLinkAsync(ApplicationUser user, string email, string confirmationLink)
        {
            string subject = "Compass User Registration";
            string plainTextContent = $"Please confirm your account by <a href='{confirmationLink}'>clicking here</a>.";
            string htmlContent = $"Please confirm your account by <a href='{confirmationLink}'>clicking here</a>.";

            await HandleEmailSend(user, email, subject, plainTextContent, htmlContent);
        }

        public Task SendPasswordResetCodeAsync(ApplicationUser user, string email, string resetCode)
        {
            throw new NotImplementedException();
        }

        public async Task SendPasswordResetLinkAsync(ApplicationUser user, string email, string resetLink)
        {
            string subject = "Compass Password Reset";
            string plainTextContent = $"Please reset your password by <a href='{resetLink}'>clicking here</a>.";
            string htmlContent = $"Please reset your password by <a href='{resetLink}'>clicking here</a>.";

            await HandleEmailSend(user, email, subject, plainTextContent, htmlContent);
        }
    }
}
