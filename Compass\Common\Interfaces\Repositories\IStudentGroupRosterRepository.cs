﻿using Compass.Common.DTOs.Student;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IStudentGroupRosterRepository
    {
        public Task<StudentGroupRoster> CreateStudentGroupRosterAsync(StudentGroupRoster? roster);
        public Task<StudentGroupRoster?> UpdateStudentGroupRosterAsync(long? id, StudentGroupRoster? roster);
        public Task<StudentGroupRoster?> GetStudentGroupRosterAsync(long? organizationId, long? schoolYearId, long? studentGroupId, long? studentId);
        public Task<List<StudentDisplayDto>> GetRosterList(StudentGroupRosterListAction action);
        public Task<int> GetRosterCount(long? organizationId, long? studentGroupId);
        public Task<bool> RemoveStudentGroupRosterAsync(long? studentId, long? organizationId, long? studentGroupId);
    }
}
