using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_lapd3_subscales")]
    public class LAPD3SubScale
    {
        [Key]
        [Column("InstID")]
        public long Id { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Required]
        [Column("organization_id")]
        public long OrganizationId { get; set; }

        [Required]
        [Column("AssessmentInstID")]
        public long AssessmentId { get; set; }

        [Required]
        [Column("ChildInstID")]
        public long StudentId { get; set; }

        [Column("ExaminerInstID")]
        public string? ExaminerId { get; set; }

        [Required]
        [Column("SubscaleStaticID")]
        public long SubscaleStaticId { get; set; }

        [Column("RawScore")]
        public short? RawScore { get; set; }

        [Column("Basal")]
        public short? Basal { get; set; }

        [Column("Ceiling")]
        public short? Ceiling { get; set; }

        [Column("Validated")]
        public int? Validated { get; set; }

        [Required]
        [Column("ChronologicalAge")]
        public int ChronologicalAge { get; set; }

        [Column("Percentile")]
        public short? Percentile { get; set; }

        [Column("ZScore", TypeName = "decimal(9,2)")]
        public decimal? ZScore { get; set; }

        [Column("TScore")]
        public short? TScore { get; set; }

        [Column("NCE")]
        public short? NCE { get; set; }

        [Column("AE")]
        [StringLength(7)]
        public string? AE { get; set; }

        [Column("LapDomain")]
        [StringLength(30)]
        public string? LapDomain { get; set; }

        [Required]
        [Column("DateAssessed")]
        public DateTime DateAssessed { get; set; }
    }
}