﻿﻿using Compass.Common.Data;
using Compass.DECA.Interfaces.Repositories;
using Compass.Deca.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.DECA.Repositories
{
    public class DecaStudentRatingScoreRepository : IDecaStudentRatingScoreRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public DecaStudentRatingScoreRepository(
            IDbContextFactory<ApplicationDbContext> contextFactory,
            AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<DecaStudentRatingScore?> GetByIdAsync(long id)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<DecaStudentRatingScore>().FindAsync(id);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting student rating score by ID: {ex.Message}");
                throw;
            }
        }

        public async Task<List<DecaStudentRatingScore>> GetByStudentRatingIdAsync(long edecaStudentRatingId)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<DecaStudentRatingScore>()
                        .Where(rs => rs.EdecaStudentRatingId == edecaStudentRatingId)
                        .OrderBy(rs => rs.QuestionNumber)
                        .ToListAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting student rating scores by student rating ID: {ex.Message}");
                throw;
            }
        }

        public async Task<List<DecaStudentRatingScore>> GetByStudentIdAsync(long studentId)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<DecaStudentRatingScore>()
                        .Where(rs => rs.StudentId == studentId)
                        .OrderBy(rs => rs.EdecaStudentRatingScoreDate)
                        .ThenBy(rs => rs.QuestionNumber)
                        .ToListAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting student rating scores by student ID: {ex.Message}");
                throw;
            }
        }

        public async Task<DecaStudentRatingScore?> GetByStudentRatingIdAndQuestionNumberAsync(long edecaStudentRatingId, int questionNumber)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<DecaStudentRatingScore>()
                        .Where(rs => rs.EdecaStudentRatingId == edecaStudentRatingId && rs.QuestionNumber == questionNumber)
                        .FirstOrDefaultAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting student rating score by student rating ID and question number: {ex.Message}");
                throw;
            }
        }

        public async Task<DecaStudentRatingScore> CreateAsync(DecaStudentRatingScore ratingScore)
        {
            try
            {
                var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                var user = authState.User;
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                ratingScore.ModId = userId;
                ratingScore.ModTs = DateTime.Now;

                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    await _dbContext.Set<DecaStudentRatingScore>().AddAsync(ratingScore);
                    await _dbContext.SaveChangesAsync();
                    return ratingScore;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating student rating score: {ex.Message}");
                throw;
            }
        }

        public async Task<DecaStudentRatingScore?> UpdateAsync(long id, DecaStudentRatingScore ratingScore)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    var existingRatingScore = await _dbContext.Set<DecaStudentRatingScore>().FindAsync(id);
                    if (existingRatingScore == null)
                    {
                        return null;
                    }

                    var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                    var user = authState.User;
                    var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                    // Update the existing rating score with the new values
                    existingRatingScore.ModId = userId;
                    existingRatingScore.ModTs = DateTime.Now;
                    existingRatingScore.OrganizationId = ratingScore.OrganizationId;
                    existingRatingScore.StudentId = ratingScore.StudentId;
                    existingRatingScore.EdecaStudentRatingId = ratingScore.EdecaStudentRatingId;
                    existingRatingScore.EdecaStudentRatingScore = ratingScore.EdecaStudentRatingScore;
                    existingRatingScore.EdecaStudentRatingScoreDate = ratingScore.EdecaStudentRatingScoreDate;
                    existingRatingScore.QuestionNumber = ratingScore.QuestionNumber;

                    _dbContext.Set<DecaStudentRatingScore>().Update(existingRatingScore);
                    await _dbContext.SaveChangesAsync();
                    return existingRatingScore;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating student rating score: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> DeleteAsync(long id)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    var ratingScore = await _dbContext.Set<DecaStudentRatingScore>().FindAsync(id);
                    if (ratingScore == null)
                    {
                        return false;
                    }

                    _dbContext.Set<DecaStudentRatingScore>().Remove(ratingScore);
                    await _dbContext.SaveChangesAsync();
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting student rating score: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> DeleteByStudentRatingIdAsync(long edecaStudentRatingId)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    var ratingScores = await _dbContext.Set<DecaStudentRatingScore>()
                        .Where(rs => rs.EdecaStudentRatingId == edecaStudentRatingId)
                        .ToListAsync();

                    if (ratingScores.Any())
                    {
                        _dbContext.Set<DecaStudentRatingScore>().RemoveRange(ratingScores);
                        await _dbContext.SaveChangesAsync();
                    }

                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error deleting student rating scores by student rating ID: {ex.Message}");
                throw;
            }
        }
    }
}
