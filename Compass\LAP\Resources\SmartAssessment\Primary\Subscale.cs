﻿using Compass.LAP.Resources.Instruments;

namespace Compass.LAP.Resources.SmartAssessment.Primary
{
    public class Subscale
    {
        // Constants
        public const int LAP_DOMAIN_DLL_SEQUENCE = 8;
        public const int LAP_SUBSCALE_DLL_SEQUENCE = 8;
        public const int LAP_DIAGNOSTIC_DOMAIN_DLL_SEQUENCE = 6;
        public const int LAP_BK_EF_SEQUENCE = 6;

        // Properties
        public long? Id { get; set; }
        public long? AssessmentInstID { get; set; }
        public int? Basal { get; set; }
        public int? Ceiling { get; set; }
        public int? ChronologicalAge { get; set; }
        public DateTime? DateOfAssessment { get; set; }
        public string? Domain { get; set; }
        public int? DomainSequence { get; set; }
        public int? Sequence { get; set; }
        public string? SubscaleID { get; set; }
        public int? RawScore { get; set; }
        public int? SchoolYear { get; set; }
        public string? StatusDescription { get; set; }
        public string? SubscaleName { get; set; }
        public Checkpoint? CheckpointValue { get; set; }
        public int? Instrument { get; set; }
        public long? ChildInstID { get; set; }
        public long? ExaminerInstID { get; set; }
        public string? ExaminerName { get; set; }
        public string? ExaminerType { get; set; }
        public string? SoftwareSource { get; set; }
        public double? ZScore { get; set; }
        public long? SubscaleStaticID { get; set; }
        public char? Validated { get; set; }
        public bool BasalCeilingValidated { get; set; } = true;
        public int? SequenceInError { get; set; }

        public Subscale()
        {
        }

        public static Subscale Create(AssessmentItem item)
        {
            return new Subscale
            {
                Domain = item.Domain,
                SubscaleID = item.SubscaleID
            };
        }

        public override bool Equals(object? obj)
        {
            if (obj is not Subscale sub)
                return false;

            if (DomainSequence.HasValue && sub.DomainSequence.HasValue &&
                Sequence.HasValue && sub.Sequence.HasValue &&
                !string.IsNullOrEmpty(SubscaleID) && !string.IsNullOrEmpty(sub.SubscaleID))
            {
                return DomainSequence.Value == sub.DomainSequence.Value &&
                       Sequence.Value == sub.Sequence.Value &&
                       SubscaleID.Equals(sub.SubscaleID);
            }

            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(DomainSequence, Sequence, SubscaleID);
        }

        public static bool IsDualLanguage(int? assessmentLevel, int? subscaleID)
        {
            if (!assessmentLevel.HasValue || !subscaleID.HasValue)
                return false;

            return assessmentLevel == AssessmentLevel.LAP_3 && subscaleID == 8 ||
                   assessmentLevel == AssessmentLevel.LAP_BK && subscaleID == 8 ||
                   (assessmentLevel == AssessmentLevel.LAP_D || assessmentLevel == AssessmentLevel.LAP_D3) && subscaleID >= 11;
        }

        public static bool IsExecutiveFunction(int? assessmentLevel, int? subscaleSequence)
        {
            if (!assessmentLevel.HasValue || !subscaleSequence.HasValue)
                return false;

            return assessmentLevel == AssessmentLevel.LAP_BK && subscaleSequence == LAP_BK_EF_SEQUENCE;
        }
    }
}
