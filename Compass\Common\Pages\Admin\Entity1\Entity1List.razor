﻿@page "/entity1list"
@using Compass.Common.DTOs.Entity1
@using Compass.Common.DTOs.Generic
@using Compass.Common.Helpers
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject NavigationManager NavigationManager
@inject IEntity1Service Entity1Service
@inject UserSessionService UserSessionService
@inject UserAccessor UserAccessor
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<h1 class="page-title horizontal-line">@entity1Hierarchy List</h1>

<div class="c4l-search-table-wrapper">
    <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

    <LoaderComponent IsLoading="isLoading">
        <div class="c4l-table-scroll-wrapper">
            <div class="c4l-table-wrapper has-links entity1-wrapper">
                <div class="c4l-table-headings-wrapper">
                    <h6 class="c4l-table-heading">@Localizer["lbl_Name"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_ContactName"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_ContactEmail"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_ContactPhone"]</h6>
                </div>

                @foreach (Entity1ListDisplayDto entity in entity1Results)
                {
                    <button name="entity 1 select button" type="button" title="Select @entity.Name" @onclick="() => OnEntity1Selected(entity)">
                        <div class="c4l-table-result-wrapper entity1-result-wrapper">
                            <p class="c4l-table-result-item">@entity.Name</p>
                            <p class="c4l-table-result-item">@entity.ContactName</p>
                            <p class="c4l-table-result-item">@entity.ContactEmail</p>
                            <p class="c4l-table-result-item">@entity.ContactPhone</p>
                        </div>
                    </button>
                }
            </div>
        </div>

        <div class="c4l-pagination-wrapper">
            <div class="c4l-pagination-buttons-wrapper">
                <div class="buttons-wrapper">
                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnPreviousClicked()"
                        disabled="@(currentPage <= 1)"
                    >
                        @Localizer["lbl_Previous"]
                    </button>

                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnNextClicked()"
                        disabled="@(currentPage >= maxPages)"
                    >
                        @Localizer["lbl_Next"]
                    </button>
                </div>
            </div>

            <div class="page-count-wrapper font-weight-500">
                <span class="current-page-number">@currentPage</span> @Localizer["lbl_of"] @maxPages
            </div>
        </div>
    </LoaderComponent>
</div>
