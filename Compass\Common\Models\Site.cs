﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.Common.Models
{
    [Table("cmn_sites")]
    public class Site
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("mod_id")]
        public string ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Column("hierarchy_entity_1_id")]
        public long? Entity1Id { get; set; }

        [Column("hierarchy_entity_2_id")]
        public long? Entity2Id { get; set; }

        [Column("hierarchy_entity_3_id")]
        public long? Entity3Id { get; set; }

        [Required]
        [Column("name")]
        public string? Name { get; set; } = string.Empty;

        [Column("contact_email")]
        public string? ContactEmail { get; set; } = string.Empty;

        [Column("address_1")]
        public string? Address1 { get; set; } = string.Empty;

        [Column("address_2")]
        public string? Address2 { get; set; } = string.Empty;

        [Column("city")]
        public string? City { get; set; } = string.Empty;

        [Column("zip_code")]
        public string? ZipCode { get; set; } = string.Empty;

        [Column("state")]
        public string? State { get; set; } = string.Empty;

        [Column("contact_first_name")]
        public string? ContactFirstName { get; set; } = string.Empty;

        [Column("contact_last_name")]
        public string? ContactLastName { get; set; } = string.Empty;

        [Column("contact_phone")]
        public string? ContactPhone { get; set; } = string.Empty;

        [Column("contact_fax")]
        public string? ContactFax { get; set; } = string.Empty;

        [Column("fax")]
        public string? Fax { get; set; } = string.Empty;

        [Column("is_deleted")]
        public string? IsDeleted { get; set; } = "N";
    }
}
