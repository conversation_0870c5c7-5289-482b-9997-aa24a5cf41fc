﻿using Compass.Common.Data;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;

namespace Compass.Common.Pages.Admin.Site
{
    public partial class SiteEditComponent : IDisposable
    {
        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        private long? siteId;
        private long? organizationId;

        private string? successMessage = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.siteId = commonSessionData.CurrentSiteId;
                this.organizationId = commonSessionData.CurrentOrganizationId;
                if (this.siteId > 0)
                {
                    Compass.Common.Models.Site? site = await SiteRepository.GetSiteAsync(this.siteId);
                    SetSiteValues(site);
                }
            }
        }

        private void SetSiteValues(Compass.Common.Models.Site? site)
        {
            if (site != null)
            {
                Input.SiteName ??= site.Name;
                Input.Address1 ??= site.Address1;
                Input.Address2 ??= site.Address2;
                Input.City ??= site.City;
                Input.ZipCode ??= site.ZipCode;
                Input.State ??= site.State;
                Input.ContactFirstName ??= site.ContactFirstName;
                Input.ContactLastName ??= site.ContactLastName;
                Input.ContactEmail ??= site.ContactEmail;
                Input.ContactPhone ??= site.ContactPhone;
                Input.ContactFax ??= site.ContactFax;
                Input.Fax ??= site.Fax;
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private Compass.Common.Models.Site GetSiteInput()
        {
            Compass.Common.Models.Site site = new();

            if (siteId != null)
            {
                site.Id = (long)siteId;
            }
            site.OrganizationId = organizationId;
            site.Name = Input.SiteName;
            site.ContactEmail = Input.ContactEmail;
            site.Address1 = Input.Address1;
            site.Address2 = Input.Address2;
            site.City = Input.City;
            site.ZipCode = Input.ZipCode;
            site.State = Input.State;
            site.ContactFirstName = Input.ContactFirstName;
            site.ContactLastName = Input.ContactLastName;
            site.ContactPhone = Input.ContactPhone;
            site.ContactFax = Input.ContactFax;
            site.Fax = Input.Fax;

            return site;
        }

        protected async Task SubmitAsync()
        {
            Compass.Common.Models.Site site = GetSiteInput();
            this.successMessage = string.Empty;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                site.OrganizationId = commonSessionData.CurrentOrganizationId;
                site.Entity1Id = commonSessionData.CurrentEntity1Id;
                site.Entity2Id = commonSessionData.CurrentEntity2Id;
                site.Entity3Id = commonSessionData.CurrentEntity3Id;

                Compass.Common.Models.Site? updatedSite = await SiteRepository.UpdateSiteAsync(this.siteId, site);

                if (updatedSite != null && updatedSite.Name != null)
                {
                    commonSessionData.SelectedEntityName = updatedSite.Name;
                }

                successMessage = "Information updated successfully!";

                await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                await CommonSessionDataObserver.BroadcastStateChangeAsync();
            }
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }

        public sealed class InputModel
        {
            [Required]
            [Display(Name = "Site Name")]
            public string? SiteName { get; set; }

            [Display(Name = "Address 1")]
            public string? Address1 { get; set; }

            [Display(Name = "Address 2")]
            public string? Address2 { get; set; }

            [Display(Name = "City")]
            public string? City { get; set; }

            [Display(Name = "Zip Code")]
            public string? ZipCode { get; set; }

            [Display(Name = "State")]
            public string? State { get; set; }

            [Required]
            [Display(Name = "Contact First Name")]
            public string? ContactFirstName { get; set; }

            [Required]
            [Display(Name = "Contact Last Name")]
            public string? ContactLastName { get; set; }

            [Required]
            [Display(Name = "Contact Phone")]
            public string? ContactPhone { get; set; }

            [Required]
            [Display(Name = "Contact Email")]
            public string? ContactEmail { get; set; }

            [Display(Name = "Contact Fax")]
            public string? ContactFax { get; set; }

            [Display(Name = "Fax")]
            public string? Fax { get; set; }
        }
    }
}
