﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Student;
using Compass.Common.DTOs.StudentGroup;
using Compass.Common.Helpers;
using Compass.Common.Models;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Compass.Common.Pages.Admin.Student
{
    public partial class StudentEditComponent
    {
        public long? selectedStudentId { get; set; }

        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        private long? currentOrganizationId;
        private long? currentSiteId;

        //Field info
        private List<StudentRace> studentRaceList = new();
        private List<StudentLanguage> studentLanguageList = new();

        //List of student group fields
        private List<StudentGroupListDisplayDto> assignedStudentGroupResults = new();
        private string studentGroupHierarchy = string.Empty;
        private string siteHierarchy = string.Empty;
        private string entity3Hierarchy = string.Empty;
        private string entity2Hierarchy = string.Empty;
        private string entity1Hierarchy = string.Empty;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        private int maxPages;
        private int currentPage;

        //Generic page info
        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private bool isLoading = false;
        private bool noSearchResults = false;
        private bool showSuccessMessage = false;
        private string successMessage = "Information saved successfully!";

        private string dialogMessage = string.Empty;

        //Dialog Box Values
        private bool isRemoveDialogVisible = false;
        private bool isAssignStudentGroupVisible = false;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();

            if (commonSessionData != null)
            {
                //Set the selectedStudent
                selectedStudentId = commonSessionData.CurrentStudentId;
                if (selectedStudentId.HasValue)
                {
                    Compass.Common.Models.Student? selectedStudent = await StudentService.GetStudentAsync(selectedStudentId);
                    SetInitialValues(selectedStudent);

                    List<StudentRaceLink> raceLinkList = await StudentService.GetStudentRaceLinks(selectedStudentId);
                    SetStudentRaceLinks(raceLinkList);

                    //Get Student Form Fields
                    StudentFormFieldsDto formFields = await StudentService.GetStudentFormFields();
                    studentRaceList = formFields.StudentRaceList;
                    studentLanguageList = formFields.StudentLanguageList;

                    studentGroupHierarchy = commonSessionData.StudentGroupHierarchy;
                    siteHierarchy = commonSessionData.SiteHierarchy;
                    entity3Hierarchy = commonSessionData.Entity3Hierarchy;
                    entity2Hierarchy = commonSessionData.Entity2Hierarchy;
                    entity1Hierarchy = commonSessionData.Entity1Hierarchy;
                    currentOrganizationId = commonSessionData.CurrentOrganizationId;
                    currentSiteId = commonSessionData.CurrentSiteId;
                    dialogMessage = "Are you sure you want to remove student from " + studentGroupHierarchy;
                    currentPage = 1;
                    maxPages = 0;
                    await GetStudentGroupPage();
                }
            }
        }

        private async Task GetStudentGroupPage()
        {
            isLoading = true;
            AssignStudentGroupListAction action = new();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            if (currentOrganizationId is not null)
            {
                action.PageQuery = pageQuery;

                action.OrganizationId = currentOrganizationId;
                action.UserId = _currentUserId;
                action.SiteId = currentSiteId;
                action.StudentId = selectedStudentId;

                KaplanPageable<StudentGroupListDisplayDto> currentPage = await StudentService.GetAssignedStudentGroupPage(action);

                assignedStudentGroupResults = currentPage.PageContent;
                maxPages = currentPage.MaxPages;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && assignedStudentGroupResults.Count == 0;
                isLoading = false;

                StateHasChanged();
            }
            else
            {
                selectedStudentId = null;
            }
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetStudentGroupPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetStudentGroupPage();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetStudentGroupPage();
        }

        private long? removeStudentGroupId;

        protected void OnRemoveClick(long studentGroupId)
        {
            removeStudentGroupId = studentGroupId;
            isRemoveDialogVisible = true;
        }

        protected async Task OnRemoveDialogResult(bool result)
        {
            if (result)
            {
                await StudentService.RemoveFromStudentGroup(selectedStudentId, currentOrganizationId, removeStudentGroupId);
                currentPage = 1;
                maxPages = 0;
                await GetStudentGroupPage();
            }

            isRemoveDialogVisible = false;
        }

        protected void OnAssignClick()
        {
            isAssignStudentGroupVisible = true;
        }

        protected async Task OnAssignStudentGroupResult(StudentGroupListDisplayDto studentGroup)
        {
            currentPage = 1;
            maxPages = 0;
            await GetStudentGroupPage();
            isAssignStudentGroupVisible = false;
        }

        private void SetStudentRaceLinks(List<StudentRaceLink> raceLinkList)
        {
            List<long> raceIdList = new();
            foreach (StudentRaceLink raceLink in raceLinkList)
            {
                raceIdList.Add(raceLink.Id);
            }

            Input.SelectedRaces = raceIdList;
        }

        private bool GetBoolFieldValue(string? value)
        {
            if (value != null)
            {
                if (value.Equals("Y"))
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        private void SetInitialValues(Compass.Common.Models.Student? selectedStudent)
        {
            if (selectedStudent != null)
            {
                Input.Id ??= selectedStudent.Id;

                Input.FirstName ??= selectedStudent.FirstName;
                Input.MiddleName ??= selectedStudent.MiddleName;
                Input.LastName ??= selectedStudent.LastName;
                Input.SchoolId ??= selectedStudent.SchoolId;
                Input.Gender ??= selectedStudent.Gender;
                Input.BirthDate ??= selectedStudent.BirthDate;
                Input.EnrollDate ??= selectedStudent.EnrollDate;
                Input.Language ??= selectedStudent.PrimaryLanguageId;
                Input.Disability ??= selectedStudent.Disability;

                Input.HispanicLatino = GetBoolFieldValue(selectedStudent.HispanicLatino);
                Input.SchoolLunch = GetBoolFieldValue(selectedStudent.SchoolLunch);
                Input.DualLanguage = GetBoolFieldValue(selectedStudent.DualLanguage);
                Input.IEPIFSP = GetBoolFieldValue(selectedStudent.IepIfsp);

                if (string.IsNullOrEmpty(selectedStudent.Disability))
                {
                    Input.HasDisability = false;
                }
                else
                {
                    Input.HasDisability = true;
                }
            }
        }

        protected void ToggleRaceSelections(long value, bool? isChecked)
        {
            if (Input.SelectedRaces != null)
            {
                if (isChecked == true)
                {
                    if (!Input.SelectedRaces.Contains(value))
                        Input.SelectedRaces.Add(value);
                }
                else
                {
                    Input.SelectedRaces.Remove(value);
                }
            }
            else
            {
                Input.SelectedRaces = new();
            }
        }

        private string SanitizeRaceId(string race_string)
        {
            if(string.IsNullOrEmpty(race_string)) return string.Empty;
            
            string sanitized_race_string = Regex.Replace(race_string, @"[^a-zA-Z0-9\-]", "-");
            sanitized_race_string = Regex.Replace(sanitized_race_string, @"-+", "-");
            sanitized_race_string = sanitized_race_string.Trim('-');
            return "student-" + sanitized_race_string;
        }

        public async Task SaveStudentAsync(EditContext editContext)
        {
            if (Input != null)
            {
                showSuccessMessage = false;

                Compass.Common.Models.Student student = new Compass.Common.Models.Student();
                student.Id = Input.Id ?? 0;
                student.FirstName = Input.FirstName;
                student.MiddleName = Input.MiddleName;
                student.LastName = Input.LastName;
                student.SchoolId = Input.SchoolId;
                student.Gender = Input.Gender;
                student.BirthDate = Input.BirthDate;
                student.EnrollDate = Input.EnrollDate;
                student.HispanicLatino = Input.HispanicLatino ? "Y" : "N";
                student.PrimaryLanguageId = Input.Language;
                student.SchoolLunch = Input.SchoolLunch ? "Y" : "N";
                student.DualLanguage = Input.DualLanguage ? "Y" : "N";
                student.IepIfsp = Input.IEPIFSP ? "Y" : "N";
                student.Disability = Input.HasDisability ? Input.Disability : "";

                CommonSessionData? commonSessionData = await GetCommonSessionData();
                if (commonSessionData != null)
                {
                    student.OrganizationId = commonSessionData.CurrentOrganizationId;

                    List<StudentRaceLink> raceLinkList = new List<StudentRaceLink>();
                    if (Input.SelectedRaces != null)
                    {
                        foreach (long raceId in Input.SelectedRaces)
                        {
                            StudentRaceLink raceLink = new StudentRaceLink();
                            raceLink.StudentId = student.Id;
                            raceLink.StudentRaceId = raceId;
                            raceLink.OrganizationId = student.OrganizationId;
                            raceLinkList.Add(raceLink);
                        }
                    }

                    SaveStudentAction action = new SaveStudentAction();
                    action.Student = student;
                    action.RaceLinkList = raceLinkList;

                    await StudentService.SaveStudentAsync(action);

                    showSuccessMessage = true;
                    StateHasChanged();
                }
            }
        }

        protected void OnCancelClick()
        {
            NavigationManager.NavigateTo("/studentlist");
        }

        public sealed class InputModel
        {
            [Required]
            public long? Id { get; set; }

            [Required]
            [Display(Name = "First Name")]
            public string? FirstName { get; set; }

            [Display(Name = "Middle Name")]
            public string? MiddleName { get; set; }

            [Required]
            [Display(Name = "Last Name")]
            public string? LastName { get; set; }

            [Display(Name = "School Id")]
            public string? SchoolId { get; set; }

            [Display(Name = "Gender")]
            public string? Gender { get; set; }

            [Required]
            [Display(Name = "Birth Date")]
            public DateTime? BirthDate { get; set; }

            [Required]
            [Display(Name = "Enroll Date")]
            public DateTime? EnrollDate { get; set; }

            [Display(Name = "Hispanic Latino")]
            public bool HispanicLatino { get; set; }

            [Display(Name = "Race")]
            public List<long>? SelectedRaces { get; set; }

            [Required]
            [Display(Name = "Language")]
            public long? Language { get; set; }

            [Display(Name = "Free/Reduced Lunch")]
            public bool SchoolLunch { get; set; }

            [Display(Name = "Dual Language Learner")]
            public bool DualLanguage { get; set; }

            [Display(Name = "IFSP/IEP")]
            public bool IEPIFSP { get; set; }

            [Display(Name = "Disability")]
            public string? Disability { get; set; }

            public bool HasDisability { get; set; }
        }
    }
}
