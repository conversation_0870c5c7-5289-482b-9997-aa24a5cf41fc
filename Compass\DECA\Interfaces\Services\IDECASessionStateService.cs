using Compass.DECA.Models;

namespace Compass.DECA.Interfaces.Services
{
    /// <summary>
    /// Interface for a service that manages session state for DECA components
    /// </summary>
    public interface IDECASessionStateService
    {
        /// <summary>
        /// The current rating ID
        /// </summary>
        long RatingId { get; set; }

        /// <summary>
        /// The current checkpoint
        /// </summary>
        int Checkpoint { get; set; }

        /// <summary>
        /// Set session context in a single operation (recommended for better performance)
        /// </summary>
        Task SetSessionContextAsync(DECASessionContext context);

        /// <summary>
        /// Get session context in a single operation (recommended for better performance)
        /// </summary>
        Task<DECASessionContext> GetSessionContextAsync();
    }
}
