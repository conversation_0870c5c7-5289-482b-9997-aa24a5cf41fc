using Compass.C4L.DTOs;
using Compass.C4L.Models;
using Compass.Common.Data;
using Compass.Common.Models;
using Compass.Common.Pages.Admin.StudentGroup;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace Compass.C4L.Pages
{
    public partial class C4L_LessonDetails
    {
        [Inject]
        private NavigationManager NavigationManager { get; set; } = default!;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private long? _classroomId;
        private long? _organizationId;

        // Properties to display lesson details
        public string LessonTitle { get; set; } = string.Empty;
        public string LessonType { get; set; } = string.Empty;
        public int Unit { get; set; }
        public int Week { get; set; }
        public int Day { get; set; }
        public int LessonTypeSequence { get; set; }
        public int TitleSequence { get; set; }
        public string Language { get; set; } = "English";
        public string ActiveTab { get; set; } = "Overview";
        public bool IsPreparationCompleted { get; set; } = false;
        public string? AtAGlance { get; set; }
        public C4LLessonContentDto lessonContentDto { get; set; } = new C4LLessonContentDto();

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            (ApplicationUser? user, string? userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;

            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
                if (commonSessionData is not null)
                {
                    _organizationId = commonSessionData.CurrentOrganizationId;
                }
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                // Get session data - this could throw exceptions from UserAccessor or UserSessionService
                CommonSessionData? commonSessionData = await GetCommonSessionData();

                // Get the lesson details from the distributed session service
                if (SessionStateService != null)
                {
                    C4LLessonContext lessonContext = await SessionStateService.GetLessonContextAsync();
                    C4LNavigationContext navigationContext = await SessionStateService.GetNavigationContextAsync();

                    LessonTitle = lessonContext.SelectedLessonTitle;
                    LessonType = lessonContext.SelectedLessonType;
                    Unit = lessonContext.SelectedLessonUnit;
                    Week = lessonContext.SelectedLessonWeek;
                    Day = lessonContext.SelectedLessonDay;
                    LessonTypeSequence = lessonContext.SelectedLessonTypeSequence;
                    TitleSequence = lessonContext.SelectedLessonTitleSequence;
                    Language = lessonContext.SelectedLessonLanguage;
                    _classroomId = navigationContext.C4L_ClassroomId;
                }

                if (!string.IsNullOrEmpty(Language.Trim()))
                {
                    Language = Language == "English" ? "en" : "es";
                }

                // This API call could throw network or serialization exceptions
                lessonContentDto = await C4LCmsApiService.GetC4LLessonContent(Language, Unit, Week, Day, LessonTypeSequence, TitleSequence);

                // If we don't have a classroom ID from the session state service, try to get it from the student group
                if (!_classroomId.HasValue)
                {
                    if (commonSessionData is not null)
                    {
                        _organizationId = commonSessionData.CurrentOrganizationId;
                        long? studentGroupId = commonSessionData.CurrentStudentGroupId;
                        if (studentGroupId.HasValue)
                        {
                            // This could throw database exceptions
                            C4LClassroom? c4lc = await C4LClassroomService.GetByStudentGroupIdAsync(studentGroupId.Value);
                            if (c4lc is not null)
                            {
                                _classroomId = c4lc.Id;
                            }
                        }
                    }
                }

                // Check if preparation is completed - this calls multiple services that could throw exceptions
                await CheckPreparationStatus();
            }
            catch (Exception ex)
            {
                // Log the error with Serilog
                Logger.LogError(ex, "Error initializing C4L_LessonDetailsComponent: {ErrorMessage}", ex.Message);

                // Also display in console for immediate visibility during development
                Console.WriteLine($"Error in C4L_LessonDetailsComponent: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        private async Task CheckPreparationStatus()
        {
            // Get the organization ID from the session
            CommonSessionData? commonSessionData = await GetCommonSessionData();

            if (_classroomId.HasValue && UserSessionService != null)
            {
                if (_organizationId.HasValue)
                {
                    // First, we need to get the preparation ID for this lesson
                    C4LLessonPreparation? preparation = await LessonPreparationService.GetPreparationAsync(
                        Language,
                        Unit,
                        Week,
                        Day,
                        LessonTypeSequence,
                        TitleSequence);

                    if (preparation != null)
                    {
                        // If we don't have a classroom ID from the session state service, try to get it from the student group
                        if (!_classroomId.HasValue)
                        {
                            if (commonSessionData is not null)
                            {
                                _organizationId = commonSessionData.CurrentOrganizationId;
                                long? studentGroupId = commonSessionData.CurrentStudentGroupId;
                                if (studentGroupId.HasValue)
                                {
                                    C4LClassroom? c4lc = await C4LClassroomService.GetByStudentGroupIdAsync(studentGroupId.Value);
                                    if (c4lc is not null)
                                    {
                                        _classroomId = c4lc.Id;
                                    }
                                }
                            }
                        }

                        C4LLessonPreparationCompleted? completedPrep = await LessonPreparationCompletedService.GetByPreparationIdAndSchoolYearAsync(
                            _classroomId.Value,
                            preparation.Id,
                            null);

                        IsPreparationCompleted = completedPrep != null && completedPrep.DateCompleted.HasValue;
                        StateHasChanged();
                    }
                }
            }
        }

        private void SetActiveTab(string tabName)
        {
            ActiveTab = tabName;
            StateHasChanged();
        }

        private void ReturnToLessons()
        {
            // Navigate back to the lessons page
            NavigationManager.NavigateTo($"/c4l-lessons");
        }

        private async Task UpdateCompletedStatus(bool isComplete)
        {
            try
            {
                if (_currentUserId != null && _classroomId.HasValue && UserSessionService != null)
                {
                    // Get the organization ID from the session
                    CommonSessionData? commonSessionData = await GetCommonSessionData();

                    if (commonSessionData is not null)
                    {
                        _organizationId = commonSessionData.CurrentOrganizationId;
                        if (_organizationId.HasValue)
                        {
                            // First, we need to get the preparation
                            C4LLessonPreparation? preparation = await LessonPreparationService.GetPreparationAsync(
                                Language,
                                Unit,
                                Week,
                                Day,
                                LessonTypeSequence,
                                TitleSequence);

                            if (preparation != null)
                            {
                                // Log the action being performed
                                Logger.LogInformation(
                                    "Updating preparation completion status to {IsComplete} for Unit {Unit}, Week {Week}, Day {Day}, LessonType {LessonType}, Title {Title}",
                                    isComplete, Unit, Week, Day, LessonTypeSequence, TitleSequence);

                                // Save the completion status
                                await LessonPreparationCompletedService.SaveCompletionStatusAsync(
                                    preparation.Id,
                                    _organizationId.Value,
                                    _classroomId.Value,
                                    isComplete);

                                // Update the status
                                IsPreparationCompleted = isComplete;
                                StateHasChanged();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error with Serilog
                Logger.LogError(ex, "Error updating preparation completion status to {IsComplete}: {ErrorMessage}",
                    isComplete, ex.Message);

                // Also display in console for immediate visibility during development
                Console.WriteLine($"Error updating preparation completion status: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
