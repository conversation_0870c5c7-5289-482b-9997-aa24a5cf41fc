@using Compass.Common.Resources
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<section class="search-input-wrapper">
  <div class="input-wrapper">
    <input 
      type="text" 
      id="search-input"
      class="form-control" 
      placeholder="@Localizer["lbl_SearchAllFields"]" 
      aria-label="@Localizer["lbl_SearchAllFields"]" 
      @bind="SearchText" 
      @onkeyup="@HandleKeyPress"
    />
  </div>

  <div class="search-buttons-wrapper">
    <button 
      class="c4l-button c4l-primary-button" 
      type="button" 
      @onclick="TriggerSearch" 
    >
      @Localizer["lbl_Search"]
    </button>
  </div>
</section>

@if (NoSearchResults)
{
  <div class="info-message-wrapper mb-4">
    <p class="my-0 text-center">There are no matching search results for <strong>@(SearchText)</strong>. Try your search again.</p>
  </div>
}
