using Compass.LAP.Resources.SmartAssessment.Observation;

namespace Compass.LAP.Resources.SmartAssessment.Primary
{
    public class AssessmentItem
    {
        public long? Id { get; set; }
        public DateTime? AchieveDate { get; set; }
        public DateTime? AssessDate { get; set; }
        public long? AssessmentInstID { get; set; }
        public int? ChronologicalAge { get; set; }
        public long? CustomerInstID { get; set; }
        public string? Domain { get; set; }
        public int? DomainSequence { get; set; }
        public int? Instrument { get; set; }
        public string? ItemID { get; set; }
        public int? OriginalValue { get; set; }
        public string? OtherItemData { get; set; }
        public int? RetestedValue { get; set; }
        public int? SchoolYear { get; set; }
        public string? SelectedItems { get; set; }
        public int? Sequence { get; set; }
        public string? Subscale { get; set; }
        public string? SubscaleID { get; set; }
        public int? SubscaleSequence { get; set; }
        public string? UserComment { get; set; }
        public int? Value { get; set; }
        public long? ChildInstID { get; set; }
        public long? ObserverInstID { get; set; }
        public string? ObserverName { get; set; }
        public bool Scored { get; set; }
        public long? ItemStaticID { get; set; }
        public long? SubscaleInstID { get; set; }
        public long? SubscaleStaticID { get; set; }
        public DateTime? ObservationDate { get; set; }
        public Checkpoint? Checkpoint { get; set; }
        public ProgressObservationItem? ProgressObservationItem { get; set; }

        public static AssessmentItem Create(Milestone milestone)
        {
            AssessmentItem ret = new AssessmentItem();
            ret.Domain = milestone.Domain;
            ret.DomainSequence = milestone.DomainSequence;
            ret.Instrument = milestone.Instrument;
            ret.ItemID = milestone.ItemID;
            ret.Sequence = milestone.Sequence;
            ret.SubscaleID = milestone.SubscaleID;
            ret.SubscaleSequence = milestone.SubscaleSequence;
            return ret;
        }
    }
}
