﻿@page "/grouplist"
@using Compass.Common.DTOs.Generic
@using Compass.Common.DTOs.StudentGroup
@using Compass.Common.Data
@using Compass.Common.Helpers
@using Compass.Common.Interfaces.Services
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject NavigationManager NavigationManager
@inject IStudentGroupService StudentGroupService
@inject UserAccessor UserAccessor
@inject CommonSessionDataObserver CommonSessionDataObserver

<h1 class="page-title horizontal-line">@studentGroupHierarchy List</h1>

<div class="c4l-search-table-wrapper">
    <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

    <LoaderComponent IsLoading="isLoading">
        <div class="c4l-table-scroll-wrapper">
            <div class="c4l-table-wrapper has-links student-group-wrapper">
                <div class="c4l-table-headings-wrapper student-group-headings-wrapper">
                    <h6 class="c4l-table-heading">Name</h6>
                    @if (@entity1Hierarchy != string.Empty)
                    {
                        <h6 class="c4l-table-heading">@entity1Hierarchy</h6>
                    }

                    @if (@entity2Hierarchy != string.Empty)
                    {
                        <h6 class="c4l-table-heading">@entity2Hierarchy</h6>
                    }

                    @if (@entity3Hierarchy != string.Empty)
                    {
                        <h6 class="c4l-table-heading">@entity3Hierarchy</h6>
                    }

                    @if (@siteHierarchy != string.Empty)
                    {
                        <h6 class="c4l-table-heading">@siteHierarchy</h6>
                    }
                </div>

                @foreach (StudentGroupListDisplayDto studentGroup in studentGroupResults)
                {
                    <button class="student-group-result-button" name="student group select button" type="button" title="Select @studentGroup.Name" @onclick="() => OnSiteSelected(studentGroup)">
                        <div class="c4l-table-result-wrapper student-group-result-wrapper">
                            <p class="c4l-table-result-item site-result-item">@studentGroup.Name</p>
                            @if(!string.IsNullOrEmpty(@studentGroup.Entity1Name))
                            {
                                <p class="c4l-table-result-item site-result-item">@studentGroup.Entity1Name</p> 
                            }

                            @if (!string.IsNullOrEmpty(@studentGroup.Entity2Name))
                            {
                                <p class="c4l-table-result-item site-result-item">@studentGroup.Entity2Name</p>
                            }

                            @if (!string.IsNullOrEmpty(@studentGroup.Entity3Name))
                            {
                                <p class="c4l-table-result-item site-result-item">@studentGroup.Entity3Name</p>
                            }

                            @if (!string.IsNullOrEmpty(@studentGroup.SiteName))
                            {
                                <p class="c4l-table-result-item site-result-item">@studentGroup.SiteName</p>
                            }
                        </div>
                    </button>
                }
            </div>
        </div>

        <div class="c4l-pagination-wrapper">
            <div class="c4l-pagination-buttons-wrapper">
                <div class="buttons-wrapper">
                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnPreviousClicked()"
                        disabled="@(currentPage <= 1)"
                    >
                        Previous
                    </button>

                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnNextClicked()"
                        disabled="@(currentPage >= maxPages)"
                    >
                        Next
                    </button>
                </div>
            </div>

            <div class="page-count-wrapper font-weight-500">
                <span class="current-page-number">@currentPage</span> of @maxPages
            </div>
        </div>
    </LoaderComponent>
</div>
