﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class UserStudentGroupAccessRepository : IUserStudentGroupAccessRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public UserStudentGroupAccessRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<UserStudentGroupAccess?> GetUserStudentGroupAccessAsync(long? organizationId, long? studentGroupId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (studentGroupId is null)
            {
                throw new ArgumentNullException(nameof(studentGroupId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.UserStudentGroupAccesses.FirstOrDefaultAsync(o => o.OrganizationId == organizationId && o.StudentGroupId == studentGroupId);
            }
        }

        public async Task<UserStudentGroupAccess?> AddUserStudentGroupAccessAsync(UserStudentGroupAccess? userStudentGroupAccess)
        {
            if (userStudentGroupAccess is null)
            {
                throw new ArgumentNullException(nameof(userStudentGroupAccess));
            }

            // Get the authentication state 
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            userStudentGroupAccess.ModId = userId;
            userStudentGroupAccess.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.UserStudentGroupAccesses.AddAsync(userStudentGroupAccess);
                await _dbContext.SaveChangesAsync();
            }

            return userStudentGroupAccess;
        }
    }
}
