using Compass.Common.Data;
using Compass.Common.DTOs.LicensePool;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface ILicensePoolRepository
    {
        public Task<LicensePool?> GetLicensePoolAsync(long? licensePoolId);
        public Task<LicensePool> AddLicensePoolAsync(LicensePool licensePool);
        public Task<LicensePool?> UpdateLicensePoolAsync(long? id, LicensePool licensePool);
        public Task<List<LicensePoolListDisplayDto>> GetLicensePoolDisplayList(PageQuery pageQuery, long? organizationId);
        public Task<int> GetLicensePoolCount(long? organizationId);
        public Task<List<LicensePool>> GetLicensePoolList(PageQuery pageQuery, long? organizationId);
        public Task<int> CountActiveLicense(long? organizationId, string? product);
        public Task<LicensePool?> GetLicensePoolForIncrementation(long? organizationId, string? product);
        public Task<bool> IncrementUsedLicensePool(long? licensePoolId);
        public ApplicationDbContext CreateDbContext();
        public Task<bool> IncrementUsedLicensePoolWithContext(long? licensePoolId, ApplicationDbContext dbContext);
    }
}
