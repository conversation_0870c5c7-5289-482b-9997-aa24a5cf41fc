﻿@page "/Account/Login"

@using System.ComponentModel.DataAnnotations
@using Compass.Common.Data
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Models
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity

@inject SignInManager<ApplicationUser> SignInManager
@inject ILogger<Login> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager
@inject CommonSessionDataObserver CommonSessionDataObserver;
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserManager<ApplicationUser> UserManager
@inject UserSessionService UserSessionService
@inject UserAccessor UserAccessor
@inject CommonSessionDataObserver CommonSessionDataObserver;
@inject IOrganizationHierarchyRepository OrganizationHierarchyRepository

<PageTitle>Log in</PageTitle>

<h1>Log in</h1>
<div class="row">
    <div class="col-md-4">
        <section>
            <StatusMessage Message="@errorMessage" />
            <EditForm Model="Input" method="post" OnValidSubmit="LoginUser" FormName="login">
                <DataAnnotationsValidator />
                <h2>Use a local account to log in.</h2>
                <hr />
                <ValidationSummary class="text-danger" role="alert" />
                <div class="form-floating mb-3">
                    <InputText @bind-Value="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="<EMAIL>" />
                    <label for="email" class="form-label">Email</label>
                    <ValidationMessage For="() => Input.Email" class="text-danger" />
                </div>
                <div class="form-floating mb-3">
                    <InputText type="password" @bind-Value="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" placeholder="password" />
                    <label for="password" class="form-label">Password</label>
                    <ValidationMessage For="() => Input.Password" class="text-danger" />
                </div>
                <div class="checkbox mb-3">
                    <label class="form-label">
                        <InputCheckbox @bind-Value="Input.RememberMe" class="darker-border-checkbox form-check-input" />
                        Remember me
                    </label>
                </div>
                <div>
                    <button type="submit" class="w-100 btn btn-lg btn-primary">Log in</button>
                </div>
                <div>
                    <p>
                        <a href="forgot-password">Forgot your password?</a>
                    </p>
                    <p>
                        <a href="@(NavigationManager.GetUriWithQueryParameters("register-account", new Dictionary<string, object?> { ["ReturnUrl"] = ReturnUrl }))">Register as a new user</a>
                    </p>
                    <p>
                        <a href="Account/ResendEmailConfirmation">Resend email confirmation</a>
                    </p>
                </div>
            </EditForm>
        </section>
    </div>
    <div class="col-md-6 col-md-offset-2">
        <section>
            <h3>Use another service to log in.</h3>
            <hr />
            <ExternalLoginPicker />
        </section>
    </div>
</div>

@code {
    private string? errorMessage;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    private string? _currentUserId;
    private ApplicationUser? _currentUser;

    protected override async Task OnInitializedAsync()
    {
        if (HttpMethods.IsGet(HttpContext.Request.Method))
        {
            // Clear the existing external cookie to ensure a clean login process
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);
        }
    }

    private async Task SetCommonSessionData()
    {
         /*
         * The following code did not work for some reason. Even through the user had
         * just been signed in the it was not registered yet that the user was signed in
         * and so the UserAccessor.GetUserAndIdAsync() returned null.
        */
        //var (user, userId) = await UserAccessor.GetUserAndIdAsync();
        //_currentUser = user;
        //_currentUserId = userId;

        // Retrieving the current user from the database
        _currentUser = await UserManager.FindByNameAsync(Input.Email);

        if (_currentUser != null)
        {
            CommonSessionData commonSessionData = new CommonSessionData();
            commonSessionData.CurrentOrganizationId = _currentUser.OrganizationId;
            if (_currentUser.OrganizationId != null && _currentUser.OrganizationId > 0)
            {
                string? userId = _currentUser.Id;
                List<VisibleEntity> visibleEntities = await OrganizationHierarchyRepository.GetVisibleEntities(userId, _currentUser.OrganizationId);

                for (int i = 0; i < visibleEntities.Count(); i++)
                {
                    VisibleEntity ve = visibleEntities[i];
                    if (ve.EntityLevel == 0)
                    {
                        commonSessionData.CurrentOrganizationName = ve.EntityHierarchy ?? "";
                    }
                    else if (ve.EntityLevel <= 1)
                    {
                        commonSessionData.Entity1Hierarchy = ve.EntityHierarchy ?? "";
                    }
                    else if (ve.EntityLevel <= 2)
                    {
                        commonSessionData.Entity2Hierarchy = ve.EntityHierarchy ?? "";
                    }
                    else if (ve.EntityLevel <= 3)
                    {
                        commonSessionData.Entity3Hierarchy = ve.EntityHierarchy ?? "";
                    }
                    else if (ve.EntityLevel <= 4)
                    {
                        commonSessionData.SiteHierarchy = ve.EntityHierarchy ?? "";
                    }
                    else if (ve.EntityLevel <= 5)
                    {
                        commonSessionData.StudentGroupHierarchy = ve.EntityHierarchy ?? "";
                    }
                    else if (ve.EntityLevel <= 6)
                    {
                        commonSessionData.StudentHierarchy = ve.EntityHierarchy ?? "";
                    }
                }
            }

            await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
            await CommonSessionDataObserver.BroadcastStateChangeAsync();
        }

    }

    public async Task LoginUser()
    {
        // This doesn't count login failures towards account lockout
        // To enable password failures to trigger account lockout, set lockoutOnFailure: true
        var result = await SignInManager.PasswordSignInAsync(Input.Email, Input.Password, Input.RememberMe, lockoutOnFailure: false);
        if (result.Succeeded)
        {
            Logger.LogInformation("User logged in.");

            await SetCommonSessionData();

            // RedirectManager.RedirectTo(ReturnUrl);
            RedirectManager.RedirectTo("/");

            await CommonSessionDataObserver.BroadcastStateChangeAsync();
        }
        else if (result.RequiresTwoFactor)
        {
            RedirectManager.RedirectTo(
                "Account/LoginWith2fa",
                new() { ["returnUrl"] = ReturnUrl, ["rememberMe"] = Input.RememberMe });
        }
        else if (result.IsLockedOut)
        {
            Logger.LogWarning("User account locked out.");
            RedirectManager.RedirectTo("Account/Lockout");
        }
        else
        {
            errorMessage = "Error: Invalid login attempt.";
        }
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";

        [Required]
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [Display(Name = "Remember me?")]
        public bool RememberMe { get; set; }
    }
}
