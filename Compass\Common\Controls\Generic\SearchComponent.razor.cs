﻿using Compass.Common.Resources;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;

namespace Compass.Common.Controls.Generic
{
    public partial class SearchComponent
    {
        [Parameter]
        public string SearchText { get; set; } = string.Empty;

        [Parameter]
        public EventCallback<string> OnSearch { get; set; }

        [Parameter]
        public bool NoSearchResults { get; set; } = false;

        [Inject]
        private IStringLocalizer<CommonResource> Localizer { get; set; } = default!;

        private async Task TriggerSearch()
        {
            await OnSearch.InvokeAsync(SearchText);
        }

        public async Task HandleKeyPress(KeyboardEventArgs e)
        {
            if (e.Code == "Enter" || e.Code == "NumpadEnter")
            {
                await TriggerSearch();
            }
        }
    }
}
