﻿using Compass.Common.Data;
using Compass.Common.SessionHandlers;
using static Compass.Common.Controls.Generic.DropMenu;

namespace Compass.Common.Pages.Admin.StudentGroup
{
    public partial class StudentGroupTabs
    {
        private static readonly int SUMMARY_INDEX = 1;
        private static readonly int MANAGE_INDEX = 2;
        private static readonly int EDIT_INDEX = 3;
        private static readonly int SUPPORT_INDEX = 4;
        private static readonly int REPORT_INDEX = 5;
        private static readonly int USER_INDEX = 7;
        private static readonly int ROSTER_INDEX = 8;

        private int currentTab = SUMMARY_INDEX;
        private Type? currentTabComponent;
        private string currentStudentGroupName = string.Empty;

        private long? studentGroupId;
        private long? organizationId;

        private bool hasC4LAccess = false;

        private readonly Dictionary<int, Type> tabComponents = new()
        {
            { SUMMARY_INDEX, typeof(StudentGroupSummaryComponent) },
            { MANAGE_INDEX, typeof(StudentGroupManageComponent) },
            { EDIT_INDEX, typeof(StudentGroupAddEditComponent) },
            { SUPPORT_INDEX, typeof(StudentGroupSummaryComponent) },
            { REPORT_INDEX, typeof(StudentGroupReportsComponent) },
            { USER_INDEX, typeof(StudentGroupUsers) },
            { ROSTER_INDEX, typeof(StudentGroupRoster) }
        };

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CommonSessionDataObserver.AddStateChangeAsyncListeners(UpdateSudentGroupName);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                currentStudentGroupName = commonSessionData.SelectedEntityName;
                currentTab = SUMMARY_INDEX;
                // Initialize with the first tab's component
                currentTabComponent = tabComponents[currentTab];

                this.studentGroupId = commonSessionData.CurrentStudentGroupId;
                this.organizationId = commonSessionData.CurrentOrganizationId;
            }
        }

        private async Task UpdateSudentGroupName()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.currentStudentGroupName = commonSessionData.SelectedEntityName;
                StateHasChanged();
            }
        }

        protected void ChangeTab(int tabIndex)
        {
            currentTab = tabIndex;
            if (tabIndex == EDIT_INDEX)
            {
                currentTabComponent = tabComponents[currentTab];
            }
            else
            {
                currentTabComponent = tabComponents[currentTab];
            }
        }

        public void SwapToStudentGroupAddEdit()
        {
            currentTabComponent = typeof(StudentGroupAddEditComponent);
            StateHasChanged();
        }

        public void Dispose()
        {
            CommonSessionDataObserver.RemoveStateChangeAsyncListeners(UpdateSudentGroupName);
        }
    }
}

public enum C4LTab
{
    Lessons,
    Assessments,
    LearningCenters
}
