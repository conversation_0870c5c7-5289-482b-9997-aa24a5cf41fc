@page "/c4l-non-contact-day/{c4l_classroomId:long}"
@using Compass.C4L.Models
@using Compass.C4L.Interfaces.Services
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Forms

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="component-content-wrapper non-contact-days-wrapper">
    <div class="non-contact-days-list">
        <table class="table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                </tr>
            </thead>
            <tbody>
                @foreach (C4LNonContactDay nonContactDay in nonContactDays)
                {
                    <tr>
                        <td>@nonContactDay.Description</td>
                        <td>@nonContactDay.StartDate.ToShortDateString()</td>
                        <td>@nonContactDay.EndDate.ToShortDateString()</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

<style>
    .non-contact-days-wrapper {
        padding: 20px;
    }

    .non-contact-days-list {
        margin-top: 2rem;
    }

    .table {
        width: 100%;
        margin-bottom: 1rem;
        background-color: transparent;
    }

    .table th,
    .table td {
        padding: 0.75rem;
        vertical-align: top;
        border-top: 1px solid #dee2e6;
    }

    .table thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #dee2e6;
    }
</style>
