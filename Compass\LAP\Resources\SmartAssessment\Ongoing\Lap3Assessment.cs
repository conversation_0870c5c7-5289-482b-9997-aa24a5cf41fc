using Compass.LAP.Resources.Instruments;

namespace Compass.LAP.Resources.SmartAssessment.Ongoing
{
    public class Lap3Assessment : OngoingAssessment
    {
        public static readonly int LAP3_WARN_AGE = 36;
        public static readonly int LAP3_MINIMUM_AGE = 30;

        public Lap3Assessment()
        {
            Instrument = AssessmentLevel.LAP_3;
        }

        public override int? GetTotalSubscales()
        {
            return 7;
        }
    }
}
