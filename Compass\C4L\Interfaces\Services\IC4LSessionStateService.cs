using Compass.C4L.Models;

namespace Compass.C4L.Interfaces.Services
{
    /// <summary>
    /// Interface for a service that manages session state for C4L components
    /// </summary>
    public interface IC4LSessionStateService
    {
        /// <summary>
        /// The current curriculum unit
        /// </summary>
        int CurrentUnit { get; set; }

        /// <summary>
        /// The current curriculum week
        /// </summary>
        int CurrentWeek { get; set; }

        /// <summary>
        /// The current calendar week number (since classroom start date)
        /// </summary>
        string CurrentCalendarUnitWeeks { get; set; }

        /// <summary>
        /// The current Monday date for the displayed week
        /// </summary>
        DateTime CurrentMondayDate { get; set; }

        /// <summary>
        /// The classroom start date
        /// </summary>
        DateTime ClassroomStartDate { get; set; }

        /// <summary>
        /// The classroom ID
        /// </summary>
        long? C4L_ClassroomId { get; set; }

        /// <summary>
        /// The selected lesson title
        /// </summary>
        string SelectedLessonTitle { get; set; }

        /// <summary>
        /// The selected lesson type
        /// </summary>
        string SelectedLessonType { get; set; }

        /// <summary>
        /// The selected lesson unit
        /// </summary>
        int SelectedLessonUnit { get; set; }

        /// <summary>
        /// The selected lesson week
        /// </summary>
        int SelectedLessonWeek { get; set; }

        /// <summary>
        /// The selected lesson day
        /// </summary>
        int SelectedLessonDay { get; set; }

        /// <summary>
        /// The selected lesson type sequence
        /// </summary>
        int SelectedLessonTypeSequence { get; set; }

        /// <summary>
        /// The selected lesson title sequence
        /// </summary>
        int SelectedLessonTitleSequence { get; set; }

        /// <summary>
        /// The selected lesson language
        /// </summary>
        string SelectedLessonLanguage { get; set; }

        /// <summary>
        /// The selected Non-Contact Day ID
        /// </summary>
        long? NonContactDayId { get; set; }

        /// <summary>
        /// Set navigation context in a single operation (recommended for better performance)
        /// </summary>
        Task SetNavigationContextAsync(C4LNavigationContext context);

        /// <summary>
        /// Get navigation context in a single operation (recommended for better performance)
        /// </summary>
        Task<C4LNavigationContext> GetNavigationContextAsync();

        /// <summary>
        /// Set lesson context in a single operation (recommended for better performance)
        /// </summary>
        Task SetLessonContextAsync(C4LLessonContext context);

        /// <summary>
        /// Get lesson context in a single operation (recommended for better performance)
        /// </summary>
        Task<C4LLessonContext> GetLessonContextAsync();
    }
}
