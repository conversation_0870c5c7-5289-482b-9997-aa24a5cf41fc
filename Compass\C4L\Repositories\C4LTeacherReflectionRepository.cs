using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System.Security.Claims;

namespace Compass.C4L.Repositories
{
    public class C4LTeacherReflectionRepository : IC4LTeacherReflectionRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public C4LTeacherReflectionRepository(
            IDbContextFactory<ApplicationDbContext> contextFactory,
            AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<C4LTeacherReflection?> GetByIdAsync(long id)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<C4LTeacherReflection>().FindAsync(id);
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error retrieving teacher reflection with ID {Id}", id);
                throw;
            }
        }

        public async Task<C4LTeacherReflection> CreateAsync(C4LTeacherReflection teacherReflection)
        {
            try
            {
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                teacherReflection.ModId = userId;
                teacherReflection.ModTs = DateTime.Now;

                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    await _dbContext.Set<C4LTeacherReflection>().AddAsync(teacherReflection);
                    await _dbContext.SaveChangesAsync();
                    return teacherReflection;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error creating teacher reflection for classroom {ClassroomId} and lesson {LessonId}", 
                    teacherReflection.ClassroomId, teacherReflection.LessonId);
                throw;
            }
        }

        public async Task<C4LTeacherReflection?> UpdateAsync(C4LTeacherReflection teacherReflection)
        {
            try
            {
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                teacherReflection.ModId = userId;
                teacherReflection.ModTs = DateTime.Now;

                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    _dbContext.Set<C4LTeacherReflection>().Update(teacherReflection);
                    await _dbContext.SaveChangesAsync();
                    return teacherReflection;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error updating teacher reflection with ID {Id}", teacherReflection.Id);
                throw;
            }
        }

        public async Task<List<C4LTeacherReflection>> GetByClassroomIdAndLessonIdAsync(long classroomId, int lessonId)
        {
            try
            {
                using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
                {
                    return await _dbContext.Set<C4LTeacherReflection>()
                        .Where(tr => tr.ClassroomId == classroomId && tr.LessonId == lessonId)
                        .OrderByDescending(tr => tr.ModTs)
                        .ToListAsync();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error retrieving teacher reflections for classroom {ClassroomId} and lesson {LessonId}", 
                    classroomId, lessonId);
                throw;
            }
        }
    }
}
