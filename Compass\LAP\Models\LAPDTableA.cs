using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_lapd_table_a")]
    public class LAPDTableA
    {
        [Key]
        [Column("InstID")]
        public long Id { get; set; }

        [Required]
        [Column("PercentileRanks")]
        public int PercentileRanks { get; set; }

        [Required]
        [Column("NCE")]
        public int NCE { get; set; }

        [Required]
        [Column("TScore")]
        public int TScore { get; set; }

        [Required]
        [Column("zScore")]
        public double ZScore { get; set; }

        [Required]
        [Column("Language")]
        public int Language { get; set; }

        [Required]
        [Column("Instrument")]
        public int Instrument { get; set; }
    }
}