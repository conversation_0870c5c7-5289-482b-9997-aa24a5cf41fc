﻿@page "/Account/Manage/EnableAuthenticator"

@using System.ComponentModel.DataAnnotations
@using System.Globalization
@using System.Text
@using System.Text.Encodings.Web
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity
@using Net.Codecrete.QrCodeGenerator
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Rendering
@using Microsoft.AspNetCore.Components
@using Compass.Components.Account
@using Compass.Components.Account.Shared

@inject UserManager<ApplicationUser> UserManager
@inject IdentityUserAccessor UserAccessor
@inject UrlEncoder UrlEncoder
@inject IdentityRedirectManager RedirectManager
@inject ILogger<EnableAuthenticator> Logger

<PageTitle>Configure Authenticator App | C4L</PageTitle>

@if (recoveryCodes is not null)
{
    <ShowRecoveryCodes RecoveryCodes="recoveryCodes.ToArray()" StatusMessage="@message" />
}
else
{
    <StatusMessage Message="@message" AlertType="@alertType" />

    <h2 class="text-center mb-4">Configure authenticator app</h2>

    <div>
        <h4 class="mb-3">To use an authenticator app go through the following steps:</h4>

        <ol style="padding-inline:0;">
            <li>
                <p>
                    Download a two-factor authenticator app like Microsoft Authenticator for
                    <a class="font-weight-500" href="https://go.microsoft.com/fwlink/?Linkid=825072">Android</a> and
                    <a class="font-weight-500" href="https://go.microsoft.com/fwlink/?Linkid=825073">iOS</a> or
                    Google Authenticator for
                    <a class="font-weight-500" href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&amp;hl=en">Android</a> and
                    <a class="font-weight-500" href="https://itunes.apple.com/us/app/google-authenticator/id388497605?mt=8">iOS</a>.
                </p>
            </li>

            <li>
                <p>Scan the QR Code or enter this key <kbd>@sharedKey</kbd> into your two factor authenticator app. Spaces and casing do not matter.</p>

                <div class="mb-3">
                    <img src="@qrCodeDataUrl" alt="QR Code" style="height:300px; width:300px;" />
                </div>
            </li>

            <li>
                <p>Once you have scanned the QR code or input the key above, your two factor authentication app will provide you with a unique code. Enter the code in the confirmation box below.</p>

                <div class="verification-form-wrapper mt-4 mb-5">
                    <EditForm Model="Input" FormName="send-code" OnValidSubmit="OnValidSubmitAsync" method="post" class="c4l-form">
                        <DataAnnotationsValidator />

                        <h3 class="h4 c4l-form-heading">Enter your verification code</h3>

                        <div class="form-floating mb-3">
                            <InputText @bind-Value="Input.Code" class="form-control" id="verification-code" autocomplete="off" placeholder="Please enter the code." />
                            <label for="verification-code" class="control-label form-label">Verification Code</label>
                            <ValidationMessage For="() => Input.Code" class="text-danger" />
                        </div>

                        <button type="submit" class="c4l-button c4l-form-button c4l-primary-button">Verify code</button>

                        <ValidationSummary class="text-danger" role="alert" />
                    </EditForm>
                </div>
            </li>
        </ol>
    </div>
}

@code {
    private const string AuthenticatorUriFormat = "otpauth://totp/{0}:{1}?secret={2}&issuer={0}&digits=6";

    private string? message;
    private string? alertType = "danger";
    private ApplicationUser user = default!;
    private string? sharedKey;
    private string? qrCodeDataUrl;
    private IEnumerable<string>? recoveryCodes;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);

        await LoadSharedKeyAndQrCodeUriAsync(user);
    }

    private async Task OnValidSubmitAsync()
    {
        // Strip spaces and hyphens
        var verificationCode = Input.Code.Replace(" ", string.Empty).Replace("-", string.Empty);

        var is2faTokenValid = await UserManager.VerifyTwoFactorTokenAsync(
            user, UserManager.Options.Tokens.AuthenticatorTokenProvider, verificationCode);

        if (!is2faTokenValid)
        {
            message = "Error: Verification code is invalid.";
            return;
        }

        await UserManager.SetTwoFactorEnabledAsync(user, true);
        var userId = await UserManager.GetUserIdAsync(user);
        Logger.LogInformation("User with ID '{UserId}' has enabled 2FA with an authenticator app.", userId);

        alertType = "success";
        message = "Your authenticator app has been verified.";

        if (await UserManager.CountRecoveryCodesAsync(user) == 0)
        {
            recoveryCodes = await UserManager.GenerateNewTwoFactorRecoveryCodesAsync(user, 10);
        }
        else
        {
            RedirectManager.RedirectToWithStatus("account/manage-2fa", message, HttpContext);
        }
    }

    private async ValueTask LoadSharedKeyAndQrCodeUriAsync(ApplicationUser user)
    {
        // Load the authenticator key & QR code URI to display on the form
        var unformattedKey = await UserManager.GetAuthenticatorKeyAsync(user);
        if (string.IsNullOrEmpty(unformattedKey))
        {
            await UserManager.ResetAuthenticatorKeyAsync(user);
            unformattedKey = await UserManager.GetAuthenticatorKeyAsync(user);
        }

        sharedKey = FormatKey(unformattedKey!);

        var email = await UserManager.GetEmailAsync(user);

        // Use a simpler issuer name for better compatibility with Google Authenticator
        var authenticatorUri = GenerateQrCodeUri(email!, unformattedKey!);

        // Generate QR code with higher error correction level for better scanning
        var qr = QrCode.EncodeText(authenticatorUri, QrCode.Ecc.High);

        // Convert QR code to a data URL for better compatibility with mobile scanners
        qrCodeDataUrl = ConvertQrCodeToDataUrl(qr);
    }

    private string ConvertQrCodeToDataUrl(QrCode qrCode)
    {
        // Create a bitmap representation of the QR code
        int scale = 10; // Scale factor for the QR code
        int size = qrCode.Size * scale;

        // Create a string builder to build an SVG representation
        var svgBuilder = new StringBuilder();
        svgBuilder.Append($"<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"{size}\" height=\"{size}\" viewBox=\"0 0 {qrCode.Size} {qrCode.Size}\">");

        // Add a white background
        svgBuilder.Append($"<rect width=\"{qrCode.Size}\" height=\"{qrCode.Size}\" fill=\"white\"/>");

        // Add black squares for each module
        for (int y = 0; y < qrCode.Size; y++)
        {
            for (int x = 0; x < qrCode.Size; x++)
            {
                if (qrCode.GetModule(x, y))
                {
                    svgBuilder.Append($"<rect x=\"{x}\" y=\"{y}\" width=\"1\" height=\"1\" fill=\"black\"/>");
                }
            }
        }

        svgBuilder.Append("</svg>");

        // Convert SVG to a data URL
        string svgString = svgBuilder.ToString();
        byte[] svgBytes = Encoding.UTF8.GetBytes(svgString);
        string base64Svg = Convert.ToBase64String(svgBytes);

        return $"data:image/svg+xml;base64,{base64Svg}";
    }

    private string FormatKey(string unformattedKey)
    {
        var result = new StringBuilder();
        int currentPosition = 0;
        while (currentPosition + 4 < unformattedKey.Length)
        {
            result.Append(unformattedKey.AsSpan(currentPosition, 4)).Append(' ');
            currentPosition += 4;
        }
        if (currentPosition < unformattedKey.Length)
        {
            result.Append(unformattedKey.AsSpan(currentPosition));
        }

        return result.ToString().ToLowerInvariant();
    }

    private string GenerateQrCodeUri(string email, string unformattedKey)
    {
        // Use a shorter issuer name for better compatibility
        return string.Format(
            CultureInfo.InvariantCulture,
            AuthenticatorUriFormat,
            UrlEncoder.Encode("Compass"),
            UrlEncoder.Encode(email),
            unformattedKey);
    }

    private sealed class InputModel
    {
        [Required]
        [StringLength(7, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Text)]
        [Display(Name = "Verification Code")]
        public string Code { get; set; } = "";
    }
}
