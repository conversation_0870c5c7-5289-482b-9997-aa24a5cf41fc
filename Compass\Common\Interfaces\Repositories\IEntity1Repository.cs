﻿using Compass.Common.DTOs.Entity1;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IEntity1Repository
    {
        Task<List<Entity1>> GetEntities1Async(long? organizationId);
        Task<Entity1> CreateEntity1Async(Entity1 entity1Hierarchy);
        Task<Entity1?> GetEntity1Async(long? id);
        Task<Entity1?> UpdateEntity1Async(long? id, Entity1 entity1Hierarchy);
        Task<List<Entity1ListDisplayDto>> GetEntity1List(Entity1ListAction action);
        Task<int> GetEntity1Count(long? organizationId, string queryText);
        Task<bool> DeleteEntity1(long? id);
    }
}
