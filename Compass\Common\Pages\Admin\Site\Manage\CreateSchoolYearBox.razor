﻿@using Compass.Common.Interfaces.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject ISiteService SiteService

<div class="dialog-overlay" style="@(IsVisible ? "display: flex;" : "display: none;")">
    <div class="dialog-box">
        <h3 class="mb-3">Create School Year</h3>

        <EditForm Model="Input" FormName="formUser" OnValidSubmit="OnSubmitClick">
            <DataAnnotationsValidator></DataAnnotationsValidator>
            <ValidationSummary></ValidationSummary>

            <div class="form-floating mb-3">
                <InputNumber Id="school-year-value" class="form-control" @bind-Value="Input.SchoolYear" autocomplete="school-year" required placeholder="School Year" />
                <label class="form-label" for="school-year-value">School Year</label>
            </div>

            <div class="form-floating mb-3">
                <InputText Id="school-year-description" class="form-control" @bind-Value="Input.Description" autocomplete="description" required placeholder="Description" />
                <label for="school-year-description">Description</label>
            </div>

            <div class="form-submit-buttons-wrapper">
                <button class="c4l-button c4l-form-button c4l-primary-button" type="submit">Create</button>
                <button class="c4l-button c4l-form-button c4l-secondary-button" type="button" @onclick="OnCancelClick">Cancel</button>
            </div>
        </EditForm>
    </div>
</div>
