﻿@using Compass.Common.DTOs.Generic
@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Services
@using Compass.Common.Pages.Prompts.Generic
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject NavigationManager NavigationManager
@inject IEntity3Service Entity3Service
@inject UserAccessor UserAccessor
@inject CommonSessionDataObserver CommonSessionDataObserver

<button type="button" class="c4l-button c4l-danger-button" @onclick="() => OnDeleteSelected()">Delete @(EntityName ?? "this " + Entity3Hierarchy)?</button>

<DialogBox 
    Title="Attention"
           Message="@DialogMessage"
    IsVisible="@IsDeleteDialogVisible" 
    DialogResult="OnDeleteDialogResult" 
/>

<MessageBox 
    Title="Attention"
    Message="@DisplayMessage"
    IsVisible="@IsDisplayMessageVisible"
    IsLocalized=true
    OnClose="OnDisplayMessageResult" 
/>
