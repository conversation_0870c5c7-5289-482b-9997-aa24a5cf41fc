﻿using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Student;
using Compass.Common.DTOs.StudentGroup;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Services
{
    public interface IStudentService
    {
        public Task<Student?> SaveStudentAsync(SaveStudentAction action);
        public Task<StudentFormFieldsDto> GetStudentFormFields();
        public Task<Student?> GetStudentAsync(long? id);
        public Task<KaplanPageable<StudentDisplayDto>> GetStudentPage(StudentListAction action);
        public Task<List<StudentRaceLink>> GetStudentRaceLinks(long? studentId);
        public Task<KaplanPageable<StudentGroupListDisplayDto>> GetAssignedStudentGroupPage(AssignStudentGroupListAction action);
        public Task<KaplanPageable<StudentGroupListDisplayDto>> GetUnAssignedStudentGroupPage(AssignStudentGroupListAction action);
        public Task AssignToStudentGroup(long? studentId, long? organizationId, long? studentGroupId);
        public Task RemoveFromStudentGroup(long? studentId, long? organizationId, long? studentGroupId);
    }
}
