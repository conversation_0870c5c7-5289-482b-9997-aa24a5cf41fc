﻿using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IUserOrganizationLinkRepository
    {
        public Task<UserOrganizationLink> AddUserOrganizationLinkAsync(UserOrganizationLink userOrganizationLink);
        public Task<UserOrganizationLink?> GetUserOrganizationLinkAsync(long? organizationId, string? userId, long? accessId);
        public Task<bool> RemoveUserOrganizationLinkAsync(long? linkId);
    }
}
