﻿@page "/organization-list"
@using Compass.Common.DTOs.Generic
@using Compass.Common.DTOs.Organization
@using Compass.Common.Data
@using Compass.Common.Helpers
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Interfaces.Services
@using Compass.Common.Models
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject NavigationManager NavigationManager
@inject IOrganizationService OrganizationService;
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject UserAccessor UserAccessor
@inject IOrganizationHierarchyRepository OrganizationHierarchyRepository
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<PageTitle>Organization List | C4L</PageTitle>

<h1 class="page-title horizontal-line">@Localizer["lbl_OrganizationList"]</h1>

<div class="c4l-search-table-wrapper">
    <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

    <LoaderComponent IsLoading="isLoading">
        <div class="c4l-table-scroll-wrapper">
            <div class="c4l-table-wrapper has-links organization-wrapper">
                <div class="c4l-table-headings-wrapper organization-heading-wrapper">
                    <h6 class="c4l-table-heading">@Localizer["lbl_OrganizationName"]</h6>
                    <h6 class="c4l-table-heading">@Localizer["lbl_ContactEmail"]</h6>
                </div>

                @foreach (OrganizationListDisplayDto org in organizationResults)
                {
                    <button class="organization-result-button" name="organization select button" type="button" title="Select @org.OrgName" @onclick="() => OnOrgSelected(org.Id)">
                        <div class="c4l-table-result-wrapper organization-result-wrapper">
                            <p class="c4l-table-result-item org-result-item">@org.OrgName</p>
                            <p class="c4l-table-result-item org-result-item">@org.OrgContactEmail</p>
                        </div>
                    </button>
                }
            </div>
        </div>

        <div class="c4l-pagination-wrapper">
            <div class="c4l-pagination-buttons-wrapper">
                <div class="buttons-wrapper">
                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnPreviousClicked()"
                        disabled="@(currentPage <= 1)"
                    >
                        @Localizer["lbl_Previous"]
                    </button>

                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnNextClicked()"
                        disabled="@(currentPage >= maxPages)"
                    >
                        @Localizer["lbl_Next"]
                    </button>
                </div>

                @if (isCurrentUserSuperAdmin)
                {
                    <div class="add-organization-button-wrapper">
                        <button class="c4l-button c4l-secondary-button add-organization-button" type="button" title="@Localizer["lbl_AddOrganization"]" @onclick="OnCreate">@Localizer["lbl_AddOrganization"]</button>
                    </div>
                }
            </div>

            <div class="page-count-wrapper font-weight-500">
                <span class="current-page-number">@currentPage</span> @Localizer["lbl_of"] @maxPages
            </div>
        </div>
    </LoaderComponent>
</div>
