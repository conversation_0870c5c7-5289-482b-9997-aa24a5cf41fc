﻿using Compass.Common.Data;
using Compass.Common.Pages.Admin.Entity2;
using Compass.Common.Pages.Admin.Entity3;
using Compass.Common.Pages.Admin.Site;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity1
{
    public partial class Entity1Tabs : IDisposable
    {
        private static readonly int SUMMARY_INDEX = 1;
        private static readonly int MANAGE_INDEX = 2;
        private static readonly int ADD_CHILD_INDEX = 3;
        private static readonly int EDIT_INDEX = 4;
        private static readonly int SUPPORT_INDEX = 5;
        private static readonly int REPORT_INDEX = 6;
        private static readonly int USER_INDEX = 7;

        private int currentTab = SUMMARY_INDEX;
        private Type? currentTabComponent;
        private Type? childHierarchyComponentType;

        private string currentEntity1Name = string.Empty;
        private string childHierarchyEntity = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private readonly Dictionary<int, Type> tabComponents = new()
    {
        { SUMMARY_INDEX, typeof(Entity1SummaryComponent) },
        { MANAGE_INDEX, typeof(Entity1ManageComponent) },
        { EDIT_INDEX, typeof(Entity1EditComponent) },
        { SUPPORT_INDEX, typeof(Entity1SummaryComponent) },
        { REPORT_INDEX, typeof(Entity1ReportsComponent) },
        { USER_INDEX, typeof(Entity1Users) }
    };

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            currentTab = SUMMARY_INDEX;
            // Initialize with the first tab's component
            currentTabComponent = tabComponents[currentTab];

            CommonSessionDataObserver.AddStateChangeAsyncListeners(UpdateEntity1Name);

            await SetChildOrganizationHierarchy();
        }
        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        private async Task SetChildOrganizationHierarchy()
        {
            childHierarchyEntity = "";

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                if (commonSessionData.Entity2Hierarchy != string.Empty)
                {
                    childHierarchyEntity = commonSessionData.Entity2Hierarchy;
                    childHierarchyComponentType = typeof(Entity2AddComponent);
                }
                else if (commonSessionData.Entity3Hierarchy != string.Empty)
                {
                    childHierarchyEntity = commonSessionData.Entity3Hierarchy;
                    childHierarchyComponentType = typeof(Entity3AddComponent);
                }
                else if (commonSessionData.SiteHierarchy != string.Empty)
                {
                    childHierarchyEntity = commonSessionData.SiteHierarchy;
                    childHierarchyComponentType = typeof(SiteAddComponent);
                }

                this.currentEntity1Name = commonSessionData.SelectedEntityName;
            }
        }

        private async Task UpdateEntity1Name()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.currentEntity1Name = commonSessionData.SelectedEntityName;
                StateHasChanged();
            }
        }

        protected void ChangeTab(int tabIndex)
        {
            currentTab = tabIndex;

            if (currentTab == ADD_CHILD_INDEX)
            {
                currentTabComponent = childHierarchyComponentType;
            }
            else
            {
                currentTabComponent = tabComponents[currentTab];
            }
        }

        public void Dispose()
        {
            CommonSessionDataObserver.RemoveStateChangeAsyncListeners(UpdateEntity1Name);
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
