﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity1
{
    public partial class Entity1ManageComponent
    {
        private bool IsDeleteDialogVisible = false;
        private bool IsDisplayMessageVisible = false;

        private string EntityName = string.Empty;
        private string DialogMessage = string.Empty;
        private string DisplayMessage = string.Empty;
        private string Entity1Hierarchy = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            IsDeleteDialogVisible = false;
            IsDisplayMessageVisible = false;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                EntityName = commonSessionData.SelectedEntityName;
                DialogMessage = "Are you sure you want to delete " + EntityName + "?";
                Entity1Hierarchy = commonSessionData.Entity1Hierarchy;
            }
        }

        protected void OnDeleteSelected()
        {
            IsDeleteDialogVisible = true;
        }

        protected async Task OnDeleteDialogResult(bool result)
        {
            if (result)
            {
                await DeleteEntity1();
            }
            else
            {
                IsDeleteDialogVisible = false;
            }
        }

        protected void OnDisplayMessageResult()
        {
            IsDisplayMessageVisible = false;
        }

        private async Task DeleteEntity1()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                long? currentOrgId = commonSessionData.CurrentOrganizationId;
                long? currentEntity1Id = commonSessionData.CurrentEntity1Id;

                DeleteReturnDto deleteReturnDto = await Entity1Service.DeleteEntity1(currentOrgId, currentEntity1Id);

                IsDeleteDialogVisible = false;

                if (deleteReturnDto.Success)
                {
                    commonSessionData.ResetCurrentIdValues();

                    if (_currentUser != null)
                    {
                        await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                        await CommonSessionDataObserver.BroadcastStateChangeAsync();
                    }

                    NavigationManager.NavigateTo($"/entity1list");
                }
                else
                {
                    DisplayMessage = deleteReturnDto.Message;
                    IsDisplayMessageVisible = true;
                }
            }
        }
    }
}
