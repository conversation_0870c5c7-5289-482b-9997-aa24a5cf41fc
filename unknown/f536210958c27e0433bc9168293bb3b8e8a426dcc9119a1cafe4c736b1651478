﻿@using Compass.Common.Data
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Models
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization
@implements IDisposable

@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject NavigationManager NavigationManager
@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject CommonSessionDataObserver CommonSessionDataObserver;
@inject UserAccessor UserAccessor
@inject IStringLocalizer<CommonResource> Localizer
@inject IOrganizationHierarchyRepository OrganizationHierarchyRepository
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<div class="sidebar-nav position-relative" style="@(isUserLoggedIn ? "display:block;" : "display:none;")">
    <div class="sidebar-dropdown-wrapper navbar navbar-dark">
        <div class="dropdown">
            <button 
                class="btn btn-secondary dropdown-toggle compass-app-link" 
                type="button"
                id="dropdownMenuButton"
                @onclick="ToggleAppDropdown" 
                data-bs-toggle="dropdown" 
                aria-expanded="false"
            >
                <span class="selected-app">@selectedApp</span>
            </button>
            
            <ul class="dropdown-menu" style="@DropdownStyle" aria-labelledby="dropdownMenuButton">
                <li class="dropdown-menu-li">
                    <NavLink class="dropdown-item" href="/" title="Connect for learning" @onclick="@(() => UpdateSelectedPage("C4L"))">
                        <svg fill="none" height="36" viewBox="0 0 36 36" width="36" xmlns="http://www.w3.org/2000/svg">
                            <path d="m0 6.75c0-3.72792 3.02208-6.75 6.75-6.75h22.5c3.7279 0 6.75 3.02208 6.75 6.75v22.5c0 3.7279-3.0221 6.75-6.75 6.75h-22.5c-3.72792 0-6.75-3.0221-6.75-6.75z" fill="#5e366e"/><g fill="#fff"><path d="m22.4489 9.84929v12.26381h6.6257v-2.6932h-3.5698v-9.57061z"/><path d="m22.4426 23.0333-.0402 2.8922 3.0452.0418.0402-2.8923z"/><path d="m17.3647 19.3887 3.9562-5.8474.0523-3.79791-7.1166 10.19141.6099 2.1139 6.3375.0851.0355-2.6932z"/><path d="m21.3648 7.03356c-.071-.027-.1399-.05192-.2151-.07891-.917-.33432-2.0805-.4859-3.4883-.4859-3.2732 0-6.0263 1.09431-8.19864 3.31409-2.17446 2.22186-3.27526 4.95866-3.27526 8.24366 0 3.2851 1.07156 5.8391 3.24393 7.9987s4.83347 3.2248 8.01477 3.2248c1.1008 0 2.2037-.1516 3.2732-.4257.2297-.0581.4407-.1204.6433-.1806v-2.8677c-.2235.0997-.447.2118-.7331.3323-.7708.3011-1.7358.4651-2.8638.4651-2.4502 0-4.516-.8223-6.1411-2.4918-1.62512-1.6695-2.4502-3.7232-2.4502-6.1859s.82508-4.5994 2.5066-6.2959c1.6794-1.69652 3.7452-2.54581 6.1954-2.54581 1.2575 0 2.4376.1682 3.4862.64579v-2.66415z"/><path d="m33.1854 21.1392c0 .5254-.424.9407-.9629.9407s-.9692-.4174-.9692-.9407.4303-.9365.9754-.9365c.5452 0 .9588.4111.9588.9365zm-1.6919 0c0 .4174.3154.7475.7352.7475.4074.0063.7165-.3301.7165-.7413 0-.4111-.3091-.7537-.729-.7537-.4198 0-.7227.3364-.7227.7475zm.5807.4859h-.2173v-.9365c.0857-.0125.2068-.0291.3614-.0291.1775 0 .2569.0291.3217.0748.0564.0394.0981.108.0981.1993 0 .1142-.0856.189-.1942.2222v.0104c.0919.0291.1378.1017.1671.2284.0292.1433.0522.1931.0689.2284h-.2298c-.0292-.0353-.0459-.1142-.0752-.2284-.0167-.0976-.0751-.1433-.1942-.1433h-.1024v.3717zm.0062-.5254h.1024c.1211 0 .2172-.0394.2172-.137 0-.0851-.0626-.1433-.2005-.1433-.0585 0-.0982.0063-.1212.0104v.2679z"/></g>
                        </svg>
                        <span>Connect4Learning</span>
                    </NavLink>
                </li>

                <li class="dropdown-menu-li">
                    <NavLink class="dropdown-item" href="/" title="e-DECA" @onclick="@(() => UpdateSelectedPage("eDECA"))">
                        <svg fill="none" height="36" viewBox="0 0 36 36" width="36" xmlns="http://www.w3.org/2000/svg"><path d="m36 18c0 9.9411-7.9964 18-17.8605 18-9.86403 0-17.860432-8.0589-17.860432-18 0-9.94113 7.996402-18 17.860432-18 9.8641 0 17.8605 8.05887 17.8605 18z" fill="#006654"/><path d="m27.4884 10.1209c-1.91-1.68476-4.4754-2.52715-7.6962-2.52715-2.6122 0-4.9903.54023-7.1344 1.62984-2.1347 1.08961-3.80129 2.54551-4.99972 4.37671-1.19844 1.8313-1.79765 3.7359-1.79765 5.6953 0 1.172.4026 2.3258 1.19843 3.4428.58986.7966 1.18907 1.1995 1.81638 1.1995.28088 0 .53368-.1099.74902-.3204.21534-.2106.3277-.4579.3277-.7234 0-.2198-.05618-.4029-.16853-.5494-.11236-.1465-.35579-.3296-.7303-.5311-1.31079-.6867-1.96618-1.758-1.96618-3.223s.53368-3.0308 1.60103-4.6148c1.06736-1.5841 2.51862-2.8568 4.34432-3.8091 1.8351-.96142 3.7826-1.43756 5.8424-1.43756 2.453 0 4.4379.7783 5.9547 2.31656 1.264 1.2911 1.9943 2.9209 2.2096 4.8987.0468.4029.0655.8149.0655 1.2453 0 1.9411-.4494 3.7175-1.3388 5.329-.8895 1.6115-2.0973 2.8385-3.6141 3.6717-1.5167.8424-3.2957 1.2636-5.3367 1.2636-.7116-.0458-1.1329-.2839-1.264-.3754-.1685-.1374-.309-.293-.412-.4945-.3089-.6135-.2153-1.4192.0749-2.7652l1.0112-4.6789 1.0955-5.0635s.0187-.0824.028-.1191c.1779-.8149.4026-1.7122.6086-2.5088.0843-.3022.1592-.5952.2341-.8516 0-.1098-.103-.1648-.2528-.1648-.5524.3114-1.6759.7875-2.5186 1.108-.2996.1098-.5618.2106-.749.2655-.1592.0824-.2247.3937-.1405.5036l.0843.0641c.3558.0916.5431.2655.6086.5768.0843.3755 0 .9432-.1966 1.8222l-2.0786 9.6325c-.5711 2.6645-.6928 2.9667-2.2377 3.1681l-.6085.0733c-.18729.1556-.22475.5585-.0843.6318 1.2265-.055 2.1628-.0733 3.2582-.0733.9082 0 2.5186.2014 4.1758.2014h.3371c.1966 0 .3932-.0091.5898-.0183 1.7322-.0915 3.5579-.4395 5.1402-1.1903 1.4231-.6776 2.6403-1.5841 3.6234-2.6554.0281-.0274.0562-.064.0843-.0915.0655-.0641.1217-.1374.1778-.2106.1124-.1282.2248-.2655.3277-.4029.0562-.0732.1218-.1557.1779-.2289.0188-.0275.0375-.0549.0656-.0824.1217-.174.2434-.3388.3651-.5219 1.3295-1.9778 1.9943-4.157 1.9943-6.5194 0-2.5455-.955-4.6606-2.865-6.3454z" fill="#fff"/>
                        </svg>
                        <span>e-DECA</span>
                    </NavLink>
                </li>

                <li class="dropdown-menu-li">
                    <NavLink class="dropdown-item" href="/" title="LAP" @onclick="@(() => UpdateSelectedPage("LAP"))">
                        <svg fill="none" height="36" viewBox="0 0 36 36" width="36" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><clipPath id="a"><path d="m0 0h36v36h-36z"/></clipPath><g clip-path="url(#a)"><path d="m.279068 6.75c0-3.72792 2.998652-6.75 6.697672-6.75h22.32556c3.6991 0 6.6977 3.02208 6.6977 6.75v22.5c0 3.7279-2.9986 6.75-6.6977 6.75h-22.32556c-3.69902 0-6.697672-3.0221-6.697672-6.75z" fill="#3b7bbf"/><path d="m7.49223 8.15625h-3.00332v15.84885h6.93989v-2.7402h-3.93657zm7.92217 0-2.9437 15.84885h2.9284l.5492-3.4317h2.4281l1.0985 7.2704h2.9696l-3.4408-19.68755zm.9363 9.85445.8139-5.1621.814 5.1621h-1.6264zm11.0632-9.85445h-4.2074v19.68755h3.0033v-9.7994h1.2041c2.6804 0 4.0972-1.2041 4.0972-3.4807v-2.9253c0-2.27807-1.4168-3.48062-4.0972-3.48062zm1.0939 6.01125c0 .8506-.332 1.1811-1.1812 1.1811h-1.1153v-4.4966h1.1153c.8507 0 1.1812.332 1.1812 1.1812z" fill="#fff"/></g>
                        </svg>
                        <span>LAP</span>
                    </NavLink>
                </li>
            </ul>
        </div>
    </div>

    <input type="checkbox" title="Navigation menu" class="navbar-toggler" />

    <div class="nav-scrollable position-relative" onclick="document.querySelector('.navbar-toggler').click()">
        <nav class="nav-links-wrapper flex-column">
            <div class="nav-item">
                <NavLink class="nav-link" data-color="neutral-200" href="/" Match="NavLinkMatch.All">
                    <svg fill="none" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                        <clipPath id="a"><path d="m0 0h24v24h-24z"/></clipPath><g clip-path="url(#a)"><path d="m23.8199 10.1474-10.5-9.749978c-.5775-.5325-1.47-.5325-2.04 0l-10.500026 9.749978c-.6075.5625-.645 1.515-.075 2.1225.2925.315.697496.48 1.102496.48.3675 0 .735-.135 1.02-.3975l9.47253-8.80498 9.48 8.80498c.6075.5625 1.5525.525 2.1225-.075.5625-.6075.525-1.56-.075-2.1225zm-19.02003 2.6025v9.45c0 .9975.8025 1.8 1.8 1.8h2.7v-6.45c0-.9975.80253-1.8 1.80003-1.8h2.4c.9975 0 1.8.8025 1.8 1.8v6.45h2.7c.9975 0 1.8-.8025 1.8-1.8v-9.45l-7.5-6.74998z" fill="var(--neutral-200)"/></g>
                    </svg>
                    @Localizer["lbl_home"]
                </NavLink>
            </div>

            <AuthorizeView>
                <Authorized>
                    @if (isSuperAdmin)
                    {
                        <div class="nav-item">
                            <NavLink class="nav-link" data-color="neutral-200" href="organization-list">
                                <svg fill="none" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                    <clipPath id="a"><path d="m0 0h24v24h-24z"/></clipPath><g clip-path="url(#a)"><path d="m6.75 3.75h-3v3h3zm4.5 0h-3v3h3zm4.5 0h-3v3h3zm-9 4.5h-3v3h3zm4.5 0h-3v3h3zm4.5 0h-3v3h3zm-9 4.5h-3v3h3zm4.5 0h-3v3h3zm4.5 0h-3v3h3zm7.2-6.75h-3.45v-4.875c0-.6225-.5025-1.125-1.125-1.125h-17.25c-.6225 0-1.125.5025-1.125 1.125v21.75c0 .6225.5025 1.125 1.125 1.125h21.825c.5775 0 1.05-.4725 1.05-1.05v-15.9c0-.5775-.4725-1.05-1.05-1.05zm-5.7 0v15.75h-4.5v-4.5h-6v4.5h-4.5v-19.5h15zm4.5 15.75h-2.25v-2.25h2.25zm0-3.75h-2.25v-2.25h2.25zm0-3.75h-2.25v-2.25h2.25zm0-3.75h-2.25v-2.25h2.25z" fill="var(--neutral-200)"/></g>
                                </svg>
                                @Localizer["lbl_OrganizationList"]
                            </NavLink>
                        </div>
                    }

                    @if (currentOrganizationId != null)
                    {
                        @if (!string.IsNullOrEmpty(organizationName))
                        {
                            <div class="nav-item">
                                <NavLink class="nav-link" data-color="neutral-200" href="organization" @onclick="() => OnNavBtnClick()">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="var(--white)" class="bi bi-buildings" viewBox="0 0 16 16">
                                        <path d="M14.763.075A.5.5 0 0 1 15 .5v15a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5V14h-1v1.5a.5.5 0 0 1-.5.5h-9a.5.5 0 0 1-.5-.5V10a.5.5 0 0 1 .342-.474L6 7.64V4.5a.5.5 0 0 1 .276-.447l8-4a.5.5 0 0 1 .487.022M6 8.694 1 10.36V15h5zM7 15h2v-1.5a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 .5.5V15h2V1.309l-7 3.5z"/>
                                        <path d="M2 11h1v1H2zm2 0h1v1H4zm-2 2h1v1H2zm2 0h1v1H4zm4-4h1v1H8zm2 0h1v1h-1zm-2 2h1v1H8zm2 0h1v1h-1zm2-2h1v1h-1zm0 2h1v1h-1zM8 7h1v1H8zm2 0h1v1h-1zm2 0h1v1h-1zM8 5h1v1H8zm2 0h1v1h-1zm2 0h1v1h-1zm0-2h1v1h-1z"/>
                                    </svg>
                                    @organizationName
                                </NavLink>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(entity1EntityHierarchy))
                        {
                            <div class="nav-item">
                                <NavLink class="nav-link" data-color="neutral-200" href="entity1list" @onclick="() => OnNavBtnClick()">
                                    <svg fill="none" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <clipPath id="a"><path d="m0 0h24v24h-24z"/></clipPath><g clip-path="url(#a)"><path d="m18.6525 18.2025c-.975 1.2225-2.2425 2.205-3.705 2.8125.2475-.87.435-1.7475.6075-2.6325-.2925-.165-.57-.375-.7875-.645-.165-.2025-.33-.405-.5025-.615-.2325 1.4925-.5775 2.97-1.035 4.4175-.615.1275-1.245.195-1.8975.195s-1.2825-.0675-1.89755-.195c-.465-1.4775-.8175-2.985-1.05-4.5075.9825.09 1.96505.1425 2.94755.1425s1.9125-.045 2.865-.135c-.3675-.4575-.7425-.9375-1.1175-1.425-1.635.09-3.27005.06-4.89755-.105-.2175-2.0925-.2175-4.2 0-6.2925.345-.0375.69-.0525 1.0275-.0825-.1875-.5175-.33-1.005-.4125-1.4775-.1425.0075-.2775.015-.42.03.12-.765.2625-1.5225.4425-2.28.0825-.525.2175-1.035.405-1.5225.0675-.2325.135-.4725.21-.705.03 0 .0675-.0075.0975-.015.4425-.915 1.05005-1.74 1.77755-2.43-6.40505.03-11.610049 5.235-11.610049 11.64s5.212499 11.625 11.625049 11.625c6.4125 0 11.505-5.1075 11.6175-11.4225-1.125 1.8075-2.52 3.63-3.8025 5.175-.1425.1725-.3075.3225-.4875.4575zm-16.70255-5.8275c0-.6525.0675-1.2825.195-1.8975 1.4775-.465 2.985-.8175 4.5075-1.05-.18 1.9575-.18 3.9375 0 5.895-1.5225-.2325-3.03-.585-4.5075-1.05-.1275-.615-.195-1.245-.195-1.8975zm.7275 3.6225c1.365.3825 2.7525.675 4.155.87.195 1.4025.4875 2.79.87 4.155-2.265-.9525-4.08-2.7675-5.025-5.025zm4.155-8.115c-1.4025.195-2.79.4875-4.155.87.9525-2.265 2.7675-4.08 5.025-5.025-.3825 1.365-.675 2.7525-.87 4.155zm9.08255 8.91c.54.645 1.5374.645 2.07 0 1.89-2.28 5.715-7.2375 5.715-10.0425 0-3.7275-3.0225-6.75-6.75-6.75s-6.75 3.0225-6.75 6.75c0 2.805 3.825 7.755 5.715 10.0425zm1.035-13.7925c2.07 0 3.75 1.68 3.75 3.75s-1.68 3.75-3.75 3.75-3.75-1.68-3.75-3.75 1.68-3.75 3.75-3.75z" fill="var(--neutral-200)"/></g>
                                    </svg>
                                    <span style="transform:translateX(-0.25rem);">@entity1EntityHierarchy List</span>
                                </NavLink>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(entity2EntityHierarchy))
                        {
                            <div class="nav-item">
                                <NavLink class="nav-link" data-color="neutral-200" href="entity2list" @onclick="() => OnNavBtnClick()">
                                    <svg fill="none" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <clipPath id="a"><path d="m0 0h24v24h-24z"/></clipPath><g clip-path="url(#a)"><path d="m16.0733 2.25c-2.6382 0-4.7768 2.13871-4.7768 4.77686 0 1.91204 2.5094 5.71254 3.8906 7.65964.4798.6763 1.2931.6758 1.7725 0 1.3811-1.9469 3.8906-5.74734 3.8906-7.65964 0-2.63815-2.1387-4.77686-4.7769-4.77686zm0 2.04785c.3585-.00011.7136.07039 1.0448.20748.3313.13708.6323.33808.8859.59149.2535.25342.4545.55429.5918.88545.1372.33115.2079.68611.208 1.04459-.0001.35847-.0708.71343-.208 1.04458-.1373.33116-.3383.63204-.5918.88545-.2536.25341-.5546.45441-.8859.59149-.3312.13709-.6863.20759-1.0448.20748-.7237-.00015-1.4178-.28772-1.9295-.79947-.5118-.51176-.7993-1.2058-.7995-1.92953.0002-.72374.2877-1.41778.7995-1.92954.5117-.51175 1.2058-.79932 1.9295-.79947zm-9.94626 1.70215c-2.63813-.00001-4.77686 2.13871-4.77686 4.7769 0 1.912 2.50935 5.7125 3.89063 7.6596.4797.6763 1.29306.6758 1.77246 0 1.38112-1.9469 3.89063-5.7473 3.89063-7.6596 0-2.63819-2.13866-4.77691-4.77686-4.7769zm0 2.04785c.72373.00015 1.4178.28772 1.92956.79947.51176.51176.79928 1.20578.79944 1.92958-.00016.7237-.28768 1.4177-.79944 1.9295-.51176.5117-1.20583.7993-1.92956.7995-.72372-.0002-1.41783-.2878-1.92957-.7995-.51174-.5118-.79928-1.2058-.79944-1.9295.00016-.7238.2877-1.41779.79944-1.92955.51174-.51175 1.20585-.79933 1.92957-.7995zm14.97066 2.85645c-.4762.9321-1.0296 1.8288-1.6069 2.707l1.7065 6.4014c-.9179-.2021-2.2267-.5363-3.5551-1.0283-1.2227-.4529-2.4542-1.042-3.3824-1.7461-.9281-.7041-1.5246-1.4896-1.6699-2.3613-.1185-.7112-.3684-1.3267-.7075-1.8575-.2369.6253-.518 1.2324-.8291 1.8252.0218.0903.0414.1833.0571.2783.2298 1.3783 1.1332 2.4678 2.2442 3.3106 1.0064.7635 2.2057 1.3521 3.3955 1.8164h-14.31008l.62073-2.332c-.31897-.491-.624-.9917-.9375-1.4825-.22267-.3892-.4548-.7819-.68115-1.1806l-1.553834 5.8301c-.044438.1666-.050263.3412-.016479.5103.0337834.1691.1062563.3282.2113036.4649.1050474.1368.2400994.2476.3947754.3238.154676.0763.324866.116.497314.116h21.75002c.1724 0 .3423-.0397.4969-.116.1547-.0762.2901-.187.3952-.3238.105-.1367.1771-.2958.2109-.4649s.0283-.3437-.0161-.5103z" fill="var(--neutral-200)"/></g>
                                    </svg>
                                    @entity2EntityHierarchy List
                                </NavLink>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(entity3EntityHierarchy))
                        {
                            <div class="nav-item">
                                <NavLink class="nav-link" data-color="neutral-200" href="entity3list" @onclick="() => OnNavBtnClick()">
                                    <svg fill="none" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <g fill="var(--neutral-200)"><path d="m16.7173 17.5649c3.0075.465 5.3782 1.4628 5.3782 3.0528-.0002 2.3173-5.0552 3.3749-9.75 3.375-4.69494 0-9.75057-1.0576-9.75077-3.375 0-1.5825 2.35519-2.5729 5.34008-3.0454.3225.4725.64503.9306.96753 1.3806-3.14973.3975-4.81485 1.2596-4.81494 1.6721 0 .5475 2.88762 1.875 8.25 1.875 5.3625 0 8.25-1.3275 8.25-1.875-.0001-.4125-1.6801-1.2821-4.845-1.6721.3225-.45.6451-.9156.9676-1.3806z"/><path clip-rule="evenodd" d="m12.3147 0c4.1474 0 7.5152 3.35981 7.5154 7.51465 0 3.25495-4.6276 9.70535-6.6226 12.33035-.45.5925-1.3349.5925-1.7849 0-1.98749-2.6175-6.62255-9.0679-6.62255-12.33035.00019-4.14722 3.36743-7.51445953 7.51465-7.51465zm.0227 3c-2.48243.00008-4.5 2.01755-4.5 4.5s2.01757 4.4999 4.5 4.5c2.4825 0 4.5-2.0175 4.5-4.5s-2.0175-4.5-4.5-4.5z" fill-rule="evenodd"/></g>
                                    </svg>
                                    @entity3EntityHierarchy List
                                </NavLink>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(siteEntityHierarchy))
                        {
                            <div class="nav-item">
                                <NavLink class="nav-link" data-color="neutral-200" href="sitelist" @onclick="() => OnNavBtnClick()">
                                    <svg fill="none" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <clipPath id="a"><path d="m0 0h24v24h-24z"/></clipPath><g clip-path="url(#a)"><path d="m23.3174 11.0324-8.1674-4.08749v-2.445l-3-1.5-3.00005 1.5v2.445l-8.1675 4.08749c-.7425.3675-1.0425002 1.275-.6675 2.01.3675.7425 1.2675 1.0425 2.01.6675l9.83245-4.91249 9.8326 4.91249c.2175.105.4424.1575.6674.1575.5476 0 1.08-.3 1.3426-.8325.3674-.7425.0674-1.6425-.6675-2.01zm-15.32995-7.36499 4.16255-2.085 4.1625 2.085c.1049.0525.225.0825.3375.0825.2775 0 .5399-.15.6749-.4125.1875-.3675.0375-.8175-.3375-1.005l-4.4999-2.2499979c-.21-.105-.4575-.105-.6675 0l-4.50005 2.2499979c-.3675.1875-.5175.6375-.3375 1.005.1875.3675.6375.5175 1.005.3375zm-4.8375 11.33249v7.2c0 .9975.8025 1.8 1.8 1.8h4.95v-4.35c0-.495.40495-.9.89995-.9h2.7c.495 0 .9001.405.9001.9v4.35h4.95c.9975 0 1.8-.8025 1.8-1.8v-7.2l-9-4.5zm4.5 6.75h-2.25v-3h2.25zm9.00005-3h2.25v3h-2.25zm-2.25-3.75c0 1.245-1.0051 2.25-2.25 2.25-1.245 0-2.25005-1.005-2.25005-2.25s1.00505-2.25 2.25005-2.25c1.2449 0 2.25 1.005 2.25 2.25z" fill="var(--neutral-200)"/></g>
                                    </svg>
                                    @siteEntityHierarchy List
                                </NavLink>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(studentGroupEntityHierarchy))
                        {
                            <div class="nav-item">
                                <NavLink class="nav-link" data-color="neutral-200" href="grouplist" @onclick="() => OnNavBtnClick()">
                                    <svg fill="none" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <clipPath id="a"><path d="m0 0h24v24h-24z"/></clipPath><g clip-path="url(#a)"><path d="m9.83245 12.18c.24005.3825.66755.57 1.26755.57 2.2725 0 6.285-2.82 6.735-3.1425.3375-.24.4125-.7125.1725-1.0425s-.7125-.4125-1.0425-.1725c-1.08.7725-4.155 2.7375-5.7225 2.8575.06-.1425.135-.3075.1875-.4275.3-.645.6375-1.3725.24-1.995-.24-.3825-.6675-.57-1.2675-.57-2.49005 0-4.07255 2.4975-4.14005 2.6025-.2175.3525-.1125.8175.24 1.035s.8175.1125 1.035-.24c.015-.015 1.155-1.7925 2.72255-1.89-.06.1425-.135.3075-.1875.4275-.30005.645-.63755 1.3725-.24005 1.995zm-5.9325-6.93h16.50005v11.25h2.25v-11.475c0-1.1175-.855-2.025-1.8975-2.025h-17.20505c-1.05 0-1.8975.9075-1.8975 2.025v11.475h2.25zm19.50005 12.75h-4.635c.0825-.2325.135-.4875.135-.75 0-1.2375-1.0125-2.25-2.25-2.25h-5.25c-.7425 0-1.35.6075-1.35 1.35v1.65h-9.150046c-.555 0-.9225002.585-.6675 1.0875l.465.9225c.3.6075.922496.99 1.597496.99h19.71005c.675 0 1.2975-.3825 1.5975-.99l.465-.9225c.2475-.5025-.1125-1.0875-.6675-1.0875zm-6.75 0h-5.1v-1.5h5.1c.4125 0 .75.3375.75.75s-.3375.75-.75.75z" fill="var(--neutral-200)"/></g>
                                    </svg>
                                    @studentGroupEntityHierarchy List
                                </NavLink>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(organizationName))
                        {
                            <div class="nav-item">
                                <NavLink class="nav-link" data-color="neutral-200" href="userlist" @onclick="() => OnNavBtnClick()">
                                    <svg fill="none" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <clipPath id="a"><path d="m0 0h24v24h-24z"/></clipPath><g clip-path="url(#a)"><path d="m7.35001 12c2.4825 0 4.49999-2.0175 4.49999-4.5s-2.01749-4.5-4.49999-4.5-4.5 2.0175-4.5 4.5 2.0175 4.5 4.5 4.5zm9.74999 0c2.07 0 3.75-1.68 3.75-3.75s-1.68-3.75-3.75-3.75-3.75 1.68-3.75 3.75 1.68 3.75 3.75 3.75zm-3.0825 3.57c-.6375-1.2675-1.935-2.07-3.3525-2.07h-6.62249c-1.4175 0-2.7225.8025-3.352501 2.07l-.51 1.02c-1.0125 2.025.4575 4.41 2.722501 4.41h8.90249c2.265 0 3.735-2.385 2.7225-4.41zm9.51 1.02-.51-1.02c-.6375-1.2675-1.935-2.07-3.3525-2.07h-5.34c.4125.405.765.87 1.035 1.4025l.51 1.02c.7125 1.4175.6375 3.075-.2025 4.425-.15.24-.3225.45-.5025.6525h5.6325c2.265 0 3.735-2.385 2.7225-4.41z" fill="var(--neutral-200)"/></g>
                                    </svg>
                                    @Localizer["lbl_UserList"]
                                </NavLink>
                            </div>
                        }

                        <div class="nav-item">
                            <NavLink class="nav-link" data-color="neutral-200" href="studentlist" @onclick="() => OnNavBtnClick()">
                                <svg fill="none" width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <g fill="var(--neutral-200)"><path d="m7.22222 9c.825 0 1.58276.47259 1.95776 1.2151l1.91972 3.8401-.0074.0073c.27.54.1198 1.2299-.3977 1.5374-.1874.1049-.3822.1574-.5771.1574-.41252 0-.8099-.225-1.0049-.6225l-.49512-.9895v6.487c0 .6225-.50252 1.125-1.125 1.125-.6225 0-1.125-.5025-1.125-1.125v-2.625c-.0001-.4124-.33758-.75-.75-.75-.41244 0-.7499.3376-.75.75v2.625c0 .6225-.50252 1.125-1.125 1.125-.6225 0-1.125-.5025-1.125-1.125v-6.487l-.46509.9375c-.24751.4947-.82485.7873-1.357176.6225-.674981-.2175-.982749-.9752-.675293-1.5827l1.965089-3.9075c.3675-.74251 1.12526-1.2151 1.95776-1.2151z"/><path d="m19.9876 9c.8249.00005 1.582.47267 1.957 1.2151l1.9204 3.8401v.0073c.27.54.1198 1.2299-.3977 1.5374-.1874.1049-.3822.1574-.5771.1574-.4125 0-.8099-.225-1.0049-.6225l-.4951-.9895v6.487c0 .6224-.5026 1.1249-1.125 1.125-.6225 0-1.125-.5025-1.125-1.125v-2.625c-.0001-.4123-.3377-.7499-.75-.75-.4125 0-.7499.3376-.75.75v2.625c0 .6224-.5026 1.1249-1.125 1.125-.6225 0-1.125-.5025-1.125-1.125v-6.487l-.4651.9375c-.2475.4949-.8255.7875-1.3579.6225-.6749-.2175-.9821-.9753-.6746-1.5827l1.9571-3.9075c.3675-.7425 1.1252-1.21509 1.9577-1.2151z"/><path d="m5.6248 2.25c1.6875 0 3.05274 1.36523 3.05274 3.05273-.00013 1.6874-1.36531 3.05201-3.05274 3.05201-1.68736-.00007-3.0526-1.36466-3.05273-3.05201 0-1.68745 1.36529-3.05266 3.05273-3.05273z"/><path d="m18.3975 2.25c1.6875.00003 3.0527 1.36525 3.0527 3.05273-.0001 1.68738-1.3653 3.05198-3.0527 3.05201-1.6874 0-3.0526-1.36461-3.0527-3.05201 0-1.6875 1.3652-3.05273 3.0527-3.05273z"/></g>
                                </svg>
                                @studentEntityHierarchy List
                            </NavLink>
                        </div>

                        @if (isSuperAdmin)
                        {
                            <div class="nav-item">
                                <NavLink class="nav-link" data-color="neutral-200" href="license-pool-list">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="var(--white)" class="bi bi-file-earmark-text" viewBox="0 0 16 16">
                                        <path d="M5.5 7a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1zM5 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5m0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5"/>
                                        <path d="M9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.5zm0 1v2A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1z"/>
                                    </svg>
                                    License Pools
                                </NavLink>
                            </div>
                        }
                    }

                    <div class="nav-item">
                        <NavLink class="nav-link" data-color="neutral-200" href="support-resources">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" height="24" viewBox="0 0 24 24" width="24">
                                <path d="m12.3.75c-6.20245 0-11.24995 5.0475-11.24995 11.25s5.0475 11.25 11.24995 11.25c6.2025 0 11.25-5.0475 11.25-11.25s-5.0475-11.25-11.25-11.25zm0 20.25c-4.96495 0-8.99995-4.035-8.99995-9s4.035-9 8.99995-9c4.965 0 9 4.035 9 9s-4.035 9-9 9zm0-6c-.825 0-1.5.675-1.5 1.5s.675 1.5 1.5 1.5 1.5-.675 1.5-1.5-.675-1.5-1.5-1.5zm1.605-9.03c-1.485-.6525-3.135-.345-4.22995.7275-.6.585-.975 1.335-1.0875 2.1525-.0375.2775.1725.525.45.525h1.37245c.21 0 .3825-.1425.435-.345.0675-.27.2025-.525.4125-.7275.375-.3675.8925-.5025 1.4175-.39.5475.1275.99.585 1.1025 1.1325.18.8925-.405 1.5975-1.0875 1.7775-.885.2325-1.5 1.0575-1.5 2.0025v.9675c0 .2475.2025.45.45.45h1.35c.2475 0 .45-.2025.45-.45v-.8475c2.0475-.6375 3.18-2.88 2.355-5.01-.3375-.87-1.02-1.5975-1.875-1.9725z" fill="var(--neutral-200)"/>
                            </svg>
                            Support & Resources
                        </NavLink>
                    </div>

                    <div class="nav-item account-sidebar-nav-link">
                        <NavLink class="nav-link" data-color="neutral-200" href="account/manage">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" height="24" viewBox="0 0 24 24" width="24">
                                <clipPath id="a"><path d="m0 0h24v24h-24z"/></clipPath><g clip-path="url(#a)"><path d="m16.3047.00000003c-.0618-.********-.123.********-.18.********-.0571.0236264-.1089.0582602-.1525.1019265-.0437.043667-.0784.09551-.102.152564-.0236.057055-.0357.118205-.0357.179956v.956545c-.5679.13281-1.1107.35534-1.6084.65918l-.6724-.67237c-.0437-.0436-.0956-.07815-.1527-.10168-.0571-.02352-.1184-.03556-.1802-.03542-.0617.00014-.1228.01245-.1798.03624-.057.02378-.1088.05856-.1523.10236l-1.125 1.125c-.0437.04367-.0784.0955-.102.15256-.0237.05705-.0357.11821-.0357.17996 0 .06176.012.12291.0357.17997.0236.05705.0583.10889.102.15255l.2461.2461c.5529.41345.9184 1.06941.9184 1.79882v.31202c.1556.05966.3077.12406.46.19189l.2212-.22119c.4787-.47874 1.1368-.69068 1.7754-.64014.4512-.41687 1.0525-.6738 1.7153-.67383 1.3979-.00002 2.5312 1.13329 2.5312 2.53125 0 .66277-.2569 1.26412-.6738 1.71534.0503.63868-.1628 1.29659-.6416 1.77543l-.2212.2211c.0675.1522.1312.3045.1905.46h.3134c.727 0 1.3822.3628 1.7959.9126l.2505.2505c.0437.0437.0955.0783.1525.102.0571.0236.1183.0358.18.0358.0618 0 .123-.0122.18-.0358.0571-.0237.1089-.0583.1526-.102l1.125-1.125c.0878-.0881.1371-.2074.1371-.3318s-.0493-.2437-.1371-.3318l-.6724-.67234c.3037-.49767.5263-1.04065.6592-1.6084h.958c.0617.00002.1229-.01212.18-.03575.057-.02362.1088-.05825.1525-.10192s.0784-.09551.102-.15257.0357-.11822.0357-.17998v-1.59085c0-.06176-.0121-.12291-.0357-.17997s-.0583-.10891-.102-.15258c-.0437-.04366-.0955-.0783-.1525-.10192-.0571-.02362-.1183-.03577-.18-.03574h-.9463c-.1349-.56935-.3598-1.11311-.6665-1.61133l.668-.66797c.0436-.04366.0783-.09549.1019-.15255.0237-.05705.0359-.11821.0359-.17997s-.0122-.12292-.0359-.17997c-.0236-.05706-.0583-.1089-.1019-.15255l-1.125-1.12647c-.0437-.04368-.0955-.07834-.1526-.10198-.057-.02365-.1182-.03582-.18-.03582-.0617 0-.1229.01217-.18.03582-.057.02364-.1088.0583-.1525.10198l-.6679.66797c-.4982-.30703-1.0421-.53144-1.6114-.6665v-.944825c0-.061758-.0121-.122913-.0357-.179973-.0236-.057059-.0583-.108905-.102-.152572-.0436-.043668-.0954-.0783024-.1525-.1019243-.0571-.023622-.1182-.03576859-.18-.03574567zm-7.78273 4.49999997c-.0985-.00003-.19592.01936-.28692.05704-.09101.03768-.1737.09292-.24335.16257s-.12492.15234-.1626.24335c-.03768.091-.05715.18854-.05713.28704v1.31104c-.82012.19184-1.60436.51332-2.32324.95214l-.92139-.91992c-.14056-.14021-.331-.21896-.52954-.21896s-.38898.07875-.52954.21896l-1.5249 1.5249c-.06965.06963-.1249.1523-.1626.24329-.0377.09098-.05713.1885-.05713.28699 0 .09848.01943.196.05713.28699.0377.09098.09295.17365.1626.24328l.91992.91989c-.43882.7189-.7603 1.5032-.95215 2.3233h-1.311032c-.098499-.0001-.19592.0193-.286927.057-.091006.0377-.173698.0929-.2433468.1626-.06964916.0696-.1249159.1523-.1625976.2433-.0376816.091-.0571556.1886-.0571286.2871v2.1562c-.000027.0985.019447.1961.0571286.2871.0376817.091.09294844.1737.1625976.2433.0696488.0697.1523408.1249.2433468.1626.091007.0377.188428.0571.286927.057h1.311032c.192.8197.5134 1.6033.95215 2.3218l-.91992.9214c-.14063.1406-.21954.3314-.21954.5303s.07891.3896.21954.5302l1.5249 1.5249c.14054.1403.33099.2191.52954.2191.19856 0 .389-.0788.52954-.2191l.92139-.9199c.7188.4389 1.50312.7603 2.32324.9522v1.311c-.00002.0985.01945.196.05713.287.03768.0911.09295.1737.1626.2434.06965.0696.15234.1249.24335.1626.091.0376.18842.057.28692.057h2.15623c.0985 0 .1961-.0194.2871-.057.091-.0377.1737-.093.2434-.1626.0696-.0697.1249-.1523.1626-.2434.0377-.091.0569-.1885.0569-.287v-1.2949c.8224-.1947 1.608-.5193 2.3277-.9624l.9155.914c.1406.1402.331.219.5295.219.1986 0 .389-.0788.5296-.219l1.5263-1.5249c.0697-.0696.1249-.1523.1626-.2432.0377-.091.0572-.1886.0572-.287 0-.0985-.0195-.196-.0572-.287s-.0929-.1737-.1626-.2433l-.914-.9155c.4434-.7197.7688-1.5052.9638-2.3277h1.2935c.0985.0001.1961-.0193.2871-.057s.1737-.0929.2434-.1626c.0696-.0696.1249-.1523.1626-.2433.0376-.091.0569-.1886.0569-.2871v-2.1562c0-.0985-.0193-.1961-.0569-.2871-.0377-.091-.093-.1737-.1626-.2433-.0697-.0697-.1524-.1249-.2434-.1626s-.1886-.0571-.2871-.057h-1.311c-.1919-.8201-.5134-1.6044-.9522-2.3233l.9199-.91989c.0697-.06962.1249-.15228.1626-.24327.0377-.09098.0572-.18851.0572-.287s-.0195-.19602-.0572-.28701-.0929-.17365-.1626-.24327l-1.5249-1.5249c-.1406-.14061-.3314-.2196-.5302-.2196-.1989 0-.3897.07899-.5303.2196l-.9214.91992c-.7185-.43875-1.5021-.76022-2.3218-.95214v-1.31104c0-.0985-.0192-.19604-.0569-.28704-.0377-.09101-.093-.1737-.1626-.24335-.0697-.06965-.1524-.12489-.2434-.16257s-.1886-.05707-.2871-.05704zm1.07813 6.0938c2.0193 0 3.6562 1.637 3.6562 3.6562 0 2.0193-1.6369 3.6562-3.6562 3.6562-2.01923 0-3.65625-1.6369-3.65625-3.6562 0-2.0192 1.63702-3.6562 3.65625-3.6562z" fill="var(--neutral-200)"/></g>
                            </svg>
                            Account Settings
                        </NavLink>
                    </div>

                    @if (context.User.Identity?.Name is not null) 
                    {
                        <div class="nav-item logout-sidebar-nav-link">
                            <form action="Account/Logout" method="post">
                                <AntiforgeryToken />
                                <input type="hidden" name="ReturnUrl" value="login" />
                                
                                <button type="submit" class="nav-link account-logout-link d-flex" data-color="neutral-200">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="var(--neutral-200)" width="20" height="20" viewBox="0 0 640 512">
                                        <path
                                            d="M208 96a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM123.7 200.5c1-.4 1.9-.8 2.9-1.2l-16.9 63.5c-5.6 21.1-.1 43.6 14.7 59.7l70.7 77.1 22 88.1c4.3 17.1 21.7 27.6 38.8 23.3s27.6-21.7 23.3-38.8l-23-92.1c-1.9-7.8-5.8-14.9-11.2-20.8l-49.5-54 19.3-65.5 9.6 23c4.4 10.6 12.5 19.3 22.8 24.5l26.7 13.3c15.8 7.9 35 1.5 42.9-14.3s1.5-35-14.3-42.9L281 232.7l-15.3-36.8C248.5 154.8 208.3 128 163.7 128c-22.8 0-45.3 4.8-66.1 14l-8 3.5c-32.9 14.6-58.1 42.4-69.4 76.5l-2.6 7.8c-5.6 16.8 3.5 34.9 20.2 40.5s34.9-3.5 40.5-20.2l2.6-7.8c5.7-17.1 18.3-30.9 34.7-38.2l8-3.5zm-30 135.1L68.7 398 9.4 457.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L116.3 441c4.6-4.6 8.2-10.1 10.6-16.1l14.5-36.2-40.7-44.4c-2.5-2.7-4.8-5.6-7-8.6zM550.6 153.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L530.7 224 384 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l146.7 0-25.4 25.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l80-80c12.5-12.5 12.5-32.8 0-45.3l-80-80z" />
                                    </svg> 
                                    @($"Logout {context.User.Identity?.Name}")
                                </button>
                            </form>
                        </div>
                    }
                </Authorized>
            </AuthorizeView>
        </nav>
    </div>

    <AuthorizeView>
        <Authorized>
            <div class="account-links-wrapper">
                @if (context.User.Identity?.Name is not null)
                {
                    <a class="nav-link account-settings-link d-flex" href="account/manage" data-color="neutral-200">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" height="24" viewBox="0 0 24 24" width="24">
                            <clipPath id="a"><path d="m0 0h24v24h-24z"/></clipPath><g clip-path="url(#a)"><path d="m16.3047.00000003c-.0618-.********-.123.********-.18.********-.0571.0236264-.1089.0582602-.1525.1019265-.0437.043667-.0784.09551-.102.152564-.0236.057055-.0357.118205-.0357.179956v.956545c-.5679.13281-1.1107.35534-1.6084.65918l-.6724-.67237c-.0437-.0436-.0956-.07815-.1527-.10168-.0571-.02352-.1184-.03556-.1802-.03542-.0617.00014-.1228.01245-.1798.03624-.057.02378-.1088.05856-.1523.10236l-1.125 1.125c-.0437.04367-.0784.0955-.102.15256-.0237.05705-.0357.11821-.0357.17996 0 .06176.012.12291.0357.17997.0236.05705.0583.10889.102.15255l.2461.2461c.5529.41345.9184 1.06941.9184 1.79882v.31202c.1556.05966.3077.12406.46.19189l.2212-.22119c.4787-.47874 1.1368-.69068 1.7754-.64014.4512-.41687 1.0525-.6738 1.7153-.67383 1.3979-.00002 2.5312 1.13329 2.5312 2.53125 0 .66277-.2569 1.26412-.6738 1.71534.0503.63868-.1628 1.29659-.6416 1.77543l-.2212.2211c.0675.1522.1312.3045.1905.46h.3134c.727 0 1.3822.3628 1.7959.9126l.2505.2505c.0437.0437.0955.0783.1525.102.0571.0236.1183.0358.18.0358.0618 0 .123-.0122.18-.0358.0571-.0237.1089-.0583.1526-.102l1.125-1.125c.0878-.0881.1371-.2074.1371-.3318s-.0493-.2437-.1371-.3318l-.6724-.67234c.3037-.49767.5263-1.04065.6592-1.6084h.958c.0617.00002.1229-.01212.18-.03575.057-.02362.1088-.05825.1525-.10192s.0784-.09551.102-.15257.0357-.11822.0357-.17998v-1.59085c0-.06176-.0121-.12291-.0357-.17997s-.0583-.10891-.102-.15258c-.0437-.04366-.0955-.0783-.1525-.10192-.0571-.02362-.1183-.03577-.18-.03574h-.9463c-.1349-.56935-.3598-1.11311-.6665-1.61133l.668-.66797c.0436-.04366.0783-.09549.1019-.15255.0237-.05705.0359-.11821.0359-.17997s-.0122-.12292-.0359-.17997c-.0236-.05706-.0583-.1089-.1019-.15255l-1.125-1.12647c-.0437-.04368-.0955-.07834-.1526-.10198-.057-.02365-.1182-.03582-.18-.03582-.0617 0-.1229.01217-.18.03582-.057.02364-.1088.0583-.1525.10198l-.6679.66797c-.4982-.30703-1.0421-.53144-1.6114-.6665v-.944825c0-.061758-.0121-.122913-.0357-.179973-.0236-.057059-.0583-.108905-.102-.152572-.0436-.043668-.0954-.0783024-.1525-.1019243-.0571-.023622-.1182-.03576859-.18-.03574567zm-7.78273 4.49999997c-.0985-.00003-.19592.01936-.28692.05704-.09101.03768-.1737.09292-.24335.16257s-.12492.15234-.1626.24335c-.03768.091-.05715.18854-.05713.28704v1.31104c-.82012.19184-1.60436.51332-2.32324.95214l-.92139-.91992c-.14056-.14021-.331-.21896-.52954-.21896s-.38898.07875-.52954.21896l-1.5249 1.5249c-.06965.06963-.1249.1523-.1626.24329-.0377.09098-.05713.1885-.05713.28699 0 .09848.01943.196.05713.28699.0377.09098.09295.17365.1626.24328l.91992.91989c-.43882.7189-.7603 1.5032-.95215 2.3233h-1.311032c-.098499-.0001-.19592.0193-.286927.057-.091006.0377-.173698.0929-.2433468.1626-.06964916.0696-.1249159.1523-.1625976.2433-.0376816.091-.0571556.1886-.0571286.2871v2.1562c-.000027.0985.019447.1961.0571286.2871.0376817.091.09294844.1737.1625976.2433.0696488.0697.1523408.1249.2433468.1626.091007.0377.188428.0571.286927.057h1.311032c.192.8197.5134 1.6033.95215 2.3218l-.91992.9214c-.14063.1406-.21954.3314-.21954.5303s.07891.3896.21954.5302l1.5249 1.5249c.14054.1403.33099.2191.52954.2191.19856 0 .389-.0788.52954-.2191l.92139-.9199c.7188.4389 1.50312.7603 2.32324.9522v1.311c-.00002.0985.01945.196.05713.287.03768.0911.09295.1737.1626.2434.06965.0696.15234.1249.24335.1626.091.0376.18842.057.28692.057h2.15623c.0985 0 .1961-.0194.2871-.057.091-.0377.1737-.093.2434-.1626.0696-.0697.1249-.1523.1626-.2434.0377-.091.0569-.1885.0569-.287v-1.2949c.8224-.1947 1.608-.5193 2.3277-.9624l.9155.914c.1406.1402.331.219.5295.219.1986 0 .389-.0788.5296-.219l1.5263-1.5249c.0697-.0696.1249-.1523.1626-.2432.0377-.091.0572-.1886.0572-.287 0-.0985-.0195-.196-.0572-.287s-.0929-.1737-.1626-.2433l-.914-.9155c.4434-.7197.7688-1.5052.9638-2.3277h1.2935c.0985.0001.1961-.0193.2871-.057s.1737-.0929.2434-.1626c.0696-.0696.1249-.1523.1626-.2433.0376-.091.0569-.1886.0569-.2871v-2.1562c0-.0985-.0193-.1961-.0569-.2871-.0377-.091-.093-.1737-.1626-.2433-.0697-.0697-.1524-.1249-.2434-.1626s-.1886-.0571-.2871-.057h-1.311c-.1919-.8201-.5134-1.6044-.9522-2.3233l.9199-.91989c.0697-.06962.1249-.15228.1626-.24327.0377-.09098.0572-.18851.0572-.287s-.0195-.19602-.0572-.28701-.0929-.17365-.1626-.24327l-1.5249-1.5249c-.1406-.14061-.3314-.2196-.5302-.2196-.1989 0-.3897.07899-.5303.2196l-.9214.91992c-.7185-.43875-1.5021-.76022-2.3218-.95214v-1.31104c0-.0985-.0192-.19604-.0569-.28704-.0377-.09101-.093-.1737-.1626-.24335-.0697-.06965-.1524-.12489-.2434-.16257s-.1886-.05707-.2871-.05704zm1.07813 6.0938c2.0193 0 3.6562 1.637 3.6562 3.6562 0 2.0193-1.6369 3.6562-3.6562 3.6562-2.01923 0-3.65625-1.6369-3.65625-3.6562 0-2.0192 1.63702-3.6562 3.65625-3.6562z" fill="var(--neutral-200)"/></g>
                        </svg>
                        Account Settings
                    </a>
                }

                <form action="/Account/Logout" method="post">
                    <AntiforgeryToken />
                    <input type="hidden" name="ReturnUrl" value="login" />

                    <button type="submit" class="nav-link account-logout-link d-flex" data-color="neutral-200">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="var(--neutral-200)" width="20" height="20" viewBox="0 0 640 512">
                            <path
                                d="M208 96a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM123.7 200.5c1-.4 1.9-.8 2.9-1.2l-16.9 63.5c-5.6 21.1-.1 43.6 14.7 59.7l70.7 77.1 22 88.1c4.3 17.1 21.7 27.6 38.8 23.3s27.6-21.7 23.3-38.8l-23-92.1c-1.9-7.8-5.8-14.9-11.2-20.8l-49.5-54 19.3-65.5 9.6 23c4.4 10.6 12.5 19.3 22.8 24.5l26.7 13.3c15.8 7.9 35 1.5 42.9-14.3s1.5-35-14.3-42.9L281 232.7l-15.3-36.8C248.5 154.8 208.3 128 163.7 128c-22.8 0-45.3 4.8-66.1 14l-8 3.5c-32.9 14.6-58.1 42.4-69.4 76.5l-2.6 7.8c-5.6 16.8 3.5 34.9 20.2 40.5s34.9-3.5 40.5-20.2l2.6-7.8c5.7-17.1 18.3-30.9 34.7-38.2l8-3.5zm-30 135.1L68.7 398 9.4 457.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L116.3 441c4.6-4.6 8.2-10.1 10.6-16.1l14.5-36.2-40.7-44.4c-2.5-2.7-4.8-5.6-7-8.6zM550.6 153.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L530.7 224 384 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l146.7 0-25.4 25.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l80-80c12.5-12.5 12.5-32.8 0-45.3l-80-80z" />
                        </svg>
                        Logout
                    </button>
                </form>
            </div>
        </Authorized>
    </AuthorizeView>
</div>
