﻿using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Data;
using Compass.Common.Pages.Admin.StudentGroup;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Routing;

namespace Compass.C4L.Pages
{
    public partial class C4L_NonContactDayAddEdit
    {
        [Inject]
        public required IC4LNonContactDayService NonContactDayService { get; set; }

        [Inject]
        public required IC4LClassroomService C4LClassroomService { get; set; }

        [Inject]
        private IC4LSessionStateService SessionStateService { get; set; } = default!;

        [Inject]
        private NavigationManager NavigationManager { get; set; } = default!;

        public long? nonContactDayId { get; set; }

        private C4LNonContactDay nonContactDay { get; set; } = new();
        private string errorMessage = string.Empty;
        private string successMessage = string.Empty;
        private long? c4l_classroomId;
        private long? organizationId;
        private bool isEditMode => nonContactDayId.HasValue;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            (ApplicationUser? user, string? userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            nonContactDayId = null;
            if (commonSessionData is not null)
            {
                this.organizationId = commonSessionData.CurrentOrganizationId;
                C4LNavigationContext navigationContext = await SessionStateService.GetNavigationContextAsync();
                if (navigationContext.C4L_ClassroomId.HasValue)
                {
                    this.c4l_classroomId = navigationContext.C4L_ClassroomId;
                }

                nonContactDayId = navigationContext.NonContactDayId;
            }

            if (this.c4l_classroomId.HasValue)
            {
                if (isEditMode && nonContactDayId.HasValue)
                {
                    // Load existing non-contact day for editing
                    nonContactDay = await NonContactDayService.GetNonContactDayAsync(nonContactDayId.Value);
                }
                else
                {
                    // Initialize new non-contact day with defaults
                    nonContactDay = new C4LNonContactDay
                    {
                        C4L_ClassroomId = this.c4l_classroomId.Value,
                        StartDate = DateTime.Today,
                        EndDate = DateTime.Today
                    };
                }
            }
        }

        private async Task HandleSaveClick()
        {
            if (nonContactDay.EndDate < nonContactDay.StartDate)
            {
                errorMessage = "End date must be after start date";
                return;
            }

            try
            {
                if (this.c4l_classroomId.HasValue)
                {
                    bool isValid = await NonContactDayService.ValidateDateRange(
                        this.c4l_classroomId.Value,
                        nonContactDay.StartDate,
                        nonContactDay.EndDate,
                        isEditMode ? nonContactDayId : null
                    );

                    nonContactDay.OrganizationId = this.organizationId;

                    if (!isValid)
                    {
                        errorMessage = "Date range overlaps with existing non-contact days";
                        return;
                    }

                    if (isEditMode)
                    {
                        await NonContactDayService.UpdateNonContactDayAsync(nonContactDay);
                    }
                    else
                    {
                        await NonContactDayService.CreateNonContactDayAsync(nonContactDay);
                    }

                    successMessage = "Non-contact day saved successfully";
                    errorMessage = string.Empty;

                    // Return to the non-contact days list after successful save
                    NavigationManager.NavigateTo("/c4l-noncontactdays");
                }
            }
            catch (Exception ex)
            {
                errorMessage = "Failed to save non-contact day";
                Console.WriteLine($"Error saving non-contact day: {ex.Message}");
            }
        }

        private void OnCancelClick()
        {
            NavigationManager.NavigateTo("/c4l-noncontactdays");
        }
    }
}
