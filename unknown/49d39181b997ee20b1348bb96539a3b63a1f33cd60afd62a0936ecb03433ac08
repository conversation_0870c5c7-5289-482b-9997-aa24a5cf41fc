﻿namespace Compass.LAP.Resources.SmartAssessment.Primary
{
    public class CompletedAssessment
    {
        protected Checkpoint? checkpoint;
        protected int? instrument;
        protected DateTime? dateOfAssessment;
        protected int? domainSequence;
        protected int? subscaleSequence;
        protected int? type;

        public Checkpoint? Checkpoint
        {
            get { return checkpoint; }
            set { checkpoint = value; }
        }

        public int? Instrument
        {
            get { return instrument; }
            set { instrument = value; }
        }

        public int? DomainSequence
        {
            get { return domainSequence; }
            set { domainSequence = value; }
        }

        public int? SubscaleSequence
        {
            get { return subscaleSequence; }
            set { subscaleSequence = value; }
        }

        public DateTime? DateOfAssessment
        {
            get { return dateOfAssessment; }
            set { dateOfAssessment = value; }
        }

        public int? Type
        {
            get { return type; }
            set { type = value; }
        }
    }
}
