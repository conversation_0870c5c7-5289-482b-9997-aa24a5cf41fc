﻿@page "/Account/ResendEmailConfirmation"

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities

@inject UserManager<ApplicationUser> UserManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager

<PageTitle>Resend Email Confirmation | C4L</PageTitle>

<h1 class="page-title mb-5">Resend email confirmation</h1>

<StatusMessage Message="@message" AlertType="@alertType" />

<EditForm Model="Input" FormName="resend-email-confirmation" OnValidSubmit="OnValidSubmitAsync" method="post" class="c4l-form">
    <DataAnnotationsValidator />
    <ValidationSummary class="text-danger" role="alert" />
    <h3 class="c4l-form-heading mb-2">Enter your email.</h3>

    <div class="form-floating mb-3">
        <InputText @bind-Value="Input.Email" class="form-control" aria-required="true" placeholder="<EMAIL>" />
        <label for="email" class="form-label">Email</label>
        <ValidationMessage For="() => Input.Email" class="text-danger" />
    </div>

    <button type="submit" class="c4l-button c4l-primary-button c4l-form-button">Resend email</button>
</EditForm>

@code {
    private string? message;
    private string? alertType = "success";

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    private async Task OnValidSubmitAsync()
    {
        var user = await UserManager.FindByEmailAsync(Input.Email!);
        if (user is null)
        {
            message = "Verification email sent. Please check your email.";
            return;
        }

        var userId = await UserManager.GetUserIdAsync(user);
        var code = await UserManager.GenerateEmailConfirmationTokenAsync(user);
        code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
        var callbackUrl = NavigationManager.GetUriWithQueryParameters(
            NavigationManager.ToAbsoluteUri("Account/ConfirmEmail").AbsoluteUri,
            new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code });
        await EmailSender.SendConfirmationLinkAsync(user, Input.Email, HtmlEncoder.Default.Encode(callbackUrl));

        message = "Verification email sent. Please check your email.";
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";
    }
}
