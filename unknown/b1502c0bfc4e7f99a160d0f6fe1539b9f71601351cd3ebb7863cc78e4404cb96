.footer {
  background-color: var(--c4l-light-gray);
  padding-block: 1.75rem;
  --_footer-facade-height: 1.5rem;

  &::before {
    content: '';
    position: absolute;
    top: calc(var(--_footer-facade-height) * -1);
    left: 0;
    width: 100%;
    height: var(--_footer-facade-height);
    background: linear-gradient(0deg, var(--white) 0, hsla(0, 0%, 100%, 0.3333) 100%);
    box-shadow: rgba(255, 255, 255, 0.16) 0px -8px 6px, rgba(255, 255, 255, 0.23) 0px -4px 6px;
  }
}

.footer-content-wrapper {
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.footer-links-wrapper {
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

::deep .footer-link,
.footer-copyright-text {
  font-size: 1.125rem;
  color: var(--c4l-dark-gray);
  margin-block: 0;
}

::deep .footer-link {
  text-decoration: none;
}

.footer-social-links-wrapper {
  gap: 0.75rem;
  align-items: center;
  margin-block-start: 0.5rem;
}

@media (min-width: 64rem) {
  .footer {
    &::before {
      left: 285px;
      width: calc(100% - 285px);
    }
  }
}

@media (min-width: 80rem) {
  .footer-content-wrapper {
    flex-direction: row;
    justify-content: space-between;
    gap: 0;
  }

  .footer-links-wrapper {
    flex-direction: row;
    justify-content: flex-start;
  }

  ::deep .footer-link {
    transition: color var(--transition-speed) ease;

    &:hover {
      color: var(--c4l-primary-purple);
    }
  }

  .footer-social-path {
    transition: fill var(--transition-speed) ease;
  }

  .footer-social-links-wrapper {
    margin-block-start: 0rem;
  }

  .footer-social-link {
    &:hover {
      & .footer-social-path {
        fill: var(--c4l-primary-purple);
      }
    }
  }
}
