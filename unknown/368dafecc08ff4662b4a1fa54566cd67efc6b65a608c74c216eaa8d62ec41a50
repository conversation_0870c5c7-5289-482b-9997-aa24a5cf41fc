﻿@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity

@inject SignInManager<ApplicationUser> SignInManager

<ul class="nav-tabs-wrapper">
    <li class="nav-item">
        <NavLink class="c4l-tab c4l-primary-tab" href="Account/Manage" Match="NavLinkMatch.All">Profile</NavLink>
    </li>
    <li class="nav-item">
        <NavLink class="c4l-tab c4l-primary-tab" href="account/manage-email">Email</NavLink>
    </li>
    <li class="nav-item">
        <NavLink class="c4l-tab c4l-primary-tab" href="account/change-password">Password</NavLink>
    </li>
    @if (hasExternalLogins)
    {
        <li class="nav-item">
            <NavLink class="c4l-tab c4l-primary-tab" href="Account/Manage/ExternalLogins">External logins</NavLink>
        </li>
    }
    <li class="nav-item">
        <NavLink class="c4l-tab c4l-primary-tab" href="account/manage-2fa">Two-factor authentication
        </NavLink>
    </li>
    <li class="nav-item">
        <NavLink class="c4l-tab c4l-primary-tab" href="account/personal-data">Personal data</NavLink>
    </li>
</ul>

@code {
    private bool hasExternalLogins;

    protected override async Task OnInitializedAsync()
    {
        hasExternalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).Any();
    }
}
