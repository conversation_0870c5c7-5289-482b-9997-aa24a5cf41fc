using Compass.LAP.Resources.SmartAssessment.Criterion;

namespace Compass.LAP.Resources.SmartAssessment.Diagnostic
{
    public abstract class DiagnosticAssessment : CriterionAssessment
    {
        public const int DIAG_MIN_AGE = 30;
        public const int DIAG_WARN_AGE = 36;

        public override int GetNumberOfItemsForBasal()
        {
            return 3;
        }

        public override int? GetNoBasalItemRetreat()
        {
            return GetNumberOfItemsForBasal() * -1;
        }
    }
}
