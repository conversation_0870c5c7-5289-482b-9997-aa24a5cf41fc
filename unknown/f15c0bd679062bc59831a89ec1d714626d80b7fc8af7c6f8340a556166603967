using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_cross_domain_lookup")]
    public class LAPCrossDomainLookup
    {
        [Key]
        [Column("StaticID")]
        public long StaticId { get; set; }

        [Required]
        [Column("SourceStaticId")]
        public long SourceStaticId { get; set; }

        [Required]
        [Column("SourceSubscaleId")]
        [StringLength(5)]
        public string SourceSubscaleId { get; set; } = string.Empty;

        [Required]
        [Column("SourceItemSequence")]
        public short SourceItemSequence { get; set; }

        [Required]
        [Column("CrossScoreStaticId")]
        public long CrossScoreStaticId { get; set; }

        [Required]
        [Column("CrossScoreSubscaleId")]
        [StringLength(5)]
        public string CrossScoreSubscaleId { get; set; } = string.Empty;

        [Required]
        [Column("CrossScoreItemSequence")]
        public short CrossScoreItemSequence { get; set; }

        [Required]
        [Column("CrossScoreSubscaleStaticId")]
        public long CrossScoreSubscaleStaticId { get; set; }

        [Required]
        [Column("Instrument")]
        public int Instrument { get; set; }
    }
}