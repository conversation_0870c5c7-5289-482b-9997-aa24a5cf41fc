﻿using Compass.Common.Data;
using Compass.Common.DTOs.Student;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Runtime.CompilerServices;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class StudentGroupRosterRepository : IStudentGroupRosterRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public StudentGroupRosterRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<StudentGroupRoster> CreateStudentGroupRosterAsync(StudentGroupRoster? roster)
        {
            // Get the authentication state
            var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            roster.ModId = userId;
            roster.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.StudentGroupRosters.AddAsync(roster);
                await _dbContext.SaveChangesAsync();
            }

            return roster;
        }

        public async Task<StudentGroupRoster?> UpdateStudentGroupRosterAsync(long? id, StudentGroupRoster? roster)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (roster is null)
            {
                throw new ArgumentNullException(nameof(roster));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                StudentGroupRoster? existingStudentGroupRoster = await _dbContext.StudentGroupRosters.FindAsync(id);

                if (existingStudentGroupRoster is null)
                {
                    return null;
                }

                // Get the authentication state
                var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                var user = authState.User;
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                existingStudentGroupRoster.ModId = userId;
                existingStudentGroupRoster.ModTs = DateTime.Now;

                existingStudentGroupRoster.LinkStatus = roster.LinkStatus;

                _dbContext.StudentGroupRosters.Update(existingStudentGroupRoster);
                await _dbContext.SaveChangesAsync();

                return existingStudentGroupRoster;
            }
        }

        public async Task<StudentGroupRoster?> GetStudentGroupRosterAsync(long? organizationId, long? schoolYearId, long? studentGroupId, long? studentId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.StudentGroupRosters.FirstOrDefaultAsync(o => o.OrganizationId == organizationId
                                                                                    && o.SchoolYearId == schoolYearId
                                                                                    && o.StudentGroupId == studentGroupId
                                                                                    && o.StudentId == studentId);
            }
        }

        private string GetRosterSql()
        {
            string sqlQuery = @"SELECT s.id, s.first_name AS FirstName, s.last_name AS LastName, s.birth_date AS BirthDate, s.school_id AS SchoolId 
                                FROM cmn_students AS s
                                INNER JOIN cmn_student_group_roster AS r
                                    ON r.student_id = s.id
                                        AND r.organization_id = s.organization_id
                                INNER JOIN cmn_school_years AS sy
                                    ON sy.site_id = r.site_id
                                        AND sy.organization_id = r.organization_id
                                        AND sy.id = r.school_year_id
                                WHERE sy.[status] = 'current'
                                    AND s.organization_id = {0}
                                    AND r.student_group_id = {1} 
                                    AND
                                    (
                                       s.first_name LIKE {2} 
                                       OR s.last_name LIKE {2}
                                       OR s.first_name + ' ' + s.last_name LIKE {2}
                                       OR s.school_id LIKE {2}
                                    ) 
                                ORDER BY s.id OFFSET {3} ROWS FETCH NEXT {4} ROWS ONLY";

            return sqlQuery;
        }

        public async Task<List<StudentDisplayDto>> GetRosterList(StudentGroupRosterListAction action)
        {
            string sqlQuery = GetRosterSql();

            PageQuery pageQuery = action.PageQuery;
            string searchText = "%" + pageQuery.QueryText + "%";
            int pageOffset = pageQuery.GetOffset();
            int pageSize = pageQuery.PageSize;

            long? organizationId = action.OrganizationId;
            if (organizationId is null)
            {
                organizationId = -1;
            }

            long? studentGroupId = action.StudentGroupId;
            if (studentGroupId is null)
            {
                studentGroupId = -1;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<StudentDisplayDto> resultList = await _dbContext.StudentDisplayDtos
                .FromSqlRaw(sqlQuery, organizationId, studentGroupId, searchText, pageOffset, pageSize)
                .Select(s => new StudentDisplayDto
                {
                    Id = s.Id,
                    FirstName = s.FirstName,
                    LastName = s.LastName,
                    BirthDate = s.BirthDate,
                    SchoolId = s.SchoolId
                })
                .ToListAsync();

                return resultList;
            }
        }

        public async Task<int> GetRosterCount(long? organizationId, long? studentGroupId)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            if (studentGroupId is null)
            {
                studentGroupId = -1;
            }

            string sqlQuery = @"SELECT COUNT(r.student_id) AS Value
                                FROM cmn_student_group_roster AS r
                                INNER JOIN cmn_school_years AS sy
                                    ON sy.site_id = r.site_id
                                        AND sy.organization_id = r.organization_id
                                        AND sy.id = r.school_year_id
                                WHERE sy.[status] = 'current'
                                    AND r.organization_id = {0}
                                    AND r.student_group_id = {1}";

            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, studentGroupId);
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();

                return count;
            }
        }

        public async Task<bool> RemoveStudentGroupRosterAsync(long? studentId, long? organizationId, long? studentGroupId)
        {
            if (studentId is null)
            {
                throw new ArgumentNullException(nameof(studentId));
            }

            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (studentGroupId is null)
            {
                throw new ArgumentNullException(nameof(studentGroupId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                StudentGroupRoster? roster = await _dbContext.StudentGroupRosters.FirstOrDefaultAsync(
                    r => r.StudentId == studentId &&
                         r.OrganizationId == organizationId &&
                         r.StudentGroupId == studentGroupId);

                if (roster == null)
                {
                    return false; // roster not found
                }

                _dbContext.StudentGroupRosters.Remove(roster);
                await _dbContext.SaveChangesAsync();
                return true; // Successfully deleted
            }
        }
    }
}
