.messagebox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 200;
}

.messagebox {
  background-color: var(--white);
  padding: 1.25rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: min(100% - 2rem, 500px);
}

.messagebox-actions {
  margin-block-start: 1.25rem;
}

.messagebox-actions button {
  &:hover {
    opacity: 0.9;
  }
}

@media (min-width: 64rem) {
  .messagebox {
    transform: translateX(137.5px);
  }
}
