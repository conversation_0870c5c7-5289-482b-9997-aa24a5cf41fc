﻿@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="dialog-overlay" style="@(IsVisible ? "display: flex;" : "display: none;")">
    <div class="dialog-box invite-modal-wrapper">
        <h3>Reschedule</h3>

        <EditForm Model="Input" FormName="formReschedule" OnValidSubmit="OnRescheduleSubmit" class="c4l-form modal-form">
            <DataAnnotationsValidator></DataAnnotationsValidator>
            <ValidationSummary></ValidationSummary>

            <FieldComponent Label="Reschedule to">
                <Control>
                    <InputDate @bind-Value="Input.NewStartDate" class="form-control" min="@OriginalDate.AddDays(1).ToString("yyyy-MM-dd")" />
                </Control>
            </FieldComponent>

            <div class="form-submit-buttons-wrapper">
                <button class="c4l-button c4l-form-button c4l-primary-button" type="submit">Reschedule</button>
                <button class="c4l-button c4l-form-button c4l-tertiary-button" @onclick="OnCancelClick" type="button">Cancel</button>
            </div>
        </EditForm>
    </div>
</div>