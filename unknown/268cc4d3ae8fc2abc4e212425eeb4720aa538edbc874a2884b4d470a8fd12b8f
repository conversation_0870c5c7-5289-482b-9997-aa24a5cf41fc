@page "/c4l-lessons-week-preparations"

@using Compass.C4L.DTOs
@using Compass.C4L.Interfaces.Services
@using Compass.C4L.Models
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Compass.Common.Services

@inject IC4LLessonPreparationService LessonPreparationService
@inject IC4LLessonPreparationCompletedService LessonPreparationCompletedService
@inject IC4LClassroomService C4LClassroomService
@inject UserSessionService UserSessionService
@inject UserAccessor UserAccessor
@inject IC4LSessionStateService SessionStateService

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="preparations-container">
    <div class="header-container">
        <h2>Lesson Week Preparations</h2>
        <button class="c4l-button c4l-primary-button" type="button" @onclick="ReturnToLessons">
            Done
        </button>
    </div>

    <div class="week-navigation">
        <button class="btn btn-link" type="button" @onclick="NavigateToPreviousWeek">
            Previous Week
        </button>
        <h3>Week @CurrentCalendarUnitWeeks</h3>
        <button class="btn btn-link" type="button" @onclick="NavigateToNextWeek">
            Next Week
        </button>
    </div>

    <div class="preparations-list">
        @if (preparations.Any())
        {
            <table class="preparations-table">
                <tbody>
                    @foreach (var preparation in preparations)
                    {
                        <tr>
                            <td class="preparation-title" style="width: 20%">
                                <div class="completed-by">
                                    COMPLETE BY <span>@(CurrentMondayDate.AddDays(preparation.Day - 1).ToString("MM/dd/yyyy"))</span>
                                </div>
                                <div class="lesson-title">@preparation.Title</div>
                            </td>
                            <td class="preparation-completed" style="width: 10%">
                                <div class="completed-checkbox">
                                    <input type="checkbox"
                                           id="<EMAIL>"
                                           checked="@preparation.IsCompleted"
                                           @onchange="@(e => OnCompletedChanged(preparation, (bool)e.Value!))" />
                                    <label for="<EMAIL>">Completed</label>
                                </div>
                            </td>
                            <td class="preparation-tasks" style="width: 70%">
                                @((MarkupString)preparation.PreparationTasks)
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

            <div class="save-button-container">
                <button class="c4l-button c4l-primary-button" type="button" @onclick="SaveCompletionStatus">
                    Save
                </button>
            </div>
        }
        else
        {
            <div class="no-preparations">
                No preparations found for this week.
            </div>
        }
    </div>
</div>

<style>
    .preparations-container {
        padding: 20px;
        width: 100%;
    }

    .header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .week-navigation {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 20px;
    }

    .preparations-table {
        width: 100%;
        border-collapse: collapse;
    }

    .preparations-table td {
        padding: 15px;
        vertical-align: top;
        border: none;
    }

    .preparation-title {
        font-weight: bold;
    }

    .preparation-completed {
        text-align: center;
    }

    .completed-checkbox {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .completed-checkbox input[type="checkbox"] {
        width: 20px;
        height: 20px;
        margin-bottom: 5px;
    }

    .completed-by {
        font-weight: bold;
        margin-bottom: 10px;
    }

    .completed-by span {
        font-weight: normal;
    }

    .lesson-title {
        font-weight: normal;
    }

    .preparation-tasks {
        word-wrap: break-word;
    }

    .no-preparations {
        padding: 20px;
        text-align: center;
        font-style: italic;
        color: #666;
    }

    .save-button-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
</style>
