﻿@using Compass.Common.DTOs.OrganizationHierarchies
@using Compass.Common.Data
@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Interfaces.Services
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Pages.Prompts.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject IOrganizationHierarchyRepository OrganizationHierarchyRepository
@inject IOrganizationHierarchyService OrganizationHierarchyService;
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject UserAccessor UserAccessor

<EditForm Model="Input" FormName="formOrganization" OnValidSubmit="OnHierarchySubmit" class="c4l-form organization-hierarchies-form">
    <h3 class="c4l-form-heading">Organization Hierarchies</h3>

    <DataAnnotationsValidator></DataAnnotationsValidator>
    <ValidationSummary></ValidationSummary>

    <div class="mb-3">
        <label class="form-label form-checkbox-label d-flex" for="entity1-checkbox">
            <InputCheckbox 
                @bind-Value="Input.IsEntity1Enabled" 
                class="darker-border-checkbox form-check-input" 
                id="entity1-checkbox"
            />
            Enable Entity 1
        </label>
    </div>

    @if (Input.IsEntity1Enabled)
    {
        <div class="mb-3">
            <label class="col-form-label" for="entity1-entity-name">
                Entity 1 Entity Name
                <InputText 
                    @bind-Value="Input.HierarchyEntity1EntityName" 
                    class="form-control mt-2"
                    id="entity1-entity-name" 
                />
            </label>
        </div>

        <div class="mb-3">
            <label class="form-label form-checkbox-label d-flex" for="entity2-checkbox">
                <InputCheckbox 
                    @bind-Value="Input.IsEntity2Enabled" 
                    class="darker-border-checkbox form-check-input" 
                    id="entity2-checkbox" 
                />
                Enable Entity 2
            </label>
        </div>

        @if (Input.IsEntity2Enabled)
        {
            <div class="mb-3">
                <label class="col-form-label" for="entity2-entity-name">
                    Entity 2 Entity Name
                    <InputText 
                        @bind-Value="Input.HierarchyEntity2EntityName" 
                        class="form-control mt-2"
                        id="entity2-entity-name" 
                    />
                </label>
            </div>

            <div class="mb-3">
                <label class="form-label form-checkbox-label d-flex" for="entity3-checkbox">
                    <InputCheckbox 
                        @bind-Value="Input.IsEntity3Enabled" 
                        class="darker-border-checkbox form-check-input" 
                        id="entity3-checkbox"
                    />
                    Enable Entity 3
                </label>
            </div>

            @if (Input.IsEntity3Enabled)
            {
                <div class="mb-2">
                    <label class="col-form-label" for="entity3-entity-name">
                        Entity 3 Entity Name
                        <InputText 
                            @bind-Value="Input.HierarchyEntity3EntityName" 
                            id="entity3-entity-name" 
                            class="form-control mt-2"
                        />
                    </label>
                </div>
            }
        }
    }

    <div class="mb-2">
        <label class="col-form-label" for="site-entity-name">
            Site Entity Name
            <InputText 
                @bind-Value="Input.HierarchySiteEntityName" 
                class="form-control"
                id="site-entity-name"
            />
        </label>
    </div>

    <div class="mb-2">
        <label class="col-form-label" for="studentgroup-entity-name">
            Student Group Entity Name
            <InputText 
                @bind-Value="Input.HierarchyStudentGroupEntityName" 
                class="form-control" 
                id="studentgroup-entity-name"
            />
        </label>
    </div>

    <div>
        <label class="col-form-label" for="student-entity-name">
            Student Entity Name
            <InputText 
                @bind-Value="Input.HierarchyStudentEntityName" 
                class="form-control" 
                id="student-entity-name"
            />
        </label>
    </div>

    <div class="form-submit-buttons-wrapper">
        <button class="c4l-button c4l-form-button c4l-secondary-button" type="submit">Update</button>
    </div>

    @if (!string.IsNullOrEmpty(successMessage))
    {
        <div class="alert alert-success mt-4" role="alert">
            <p class="text-center font-weight-600 mb-0">@successMessage</p>
        </div>
    }
</EditForm>

<DialogBox 
    Title="Attention" 
    Message=@validationMessage
    IsVisible="@showValidationDialogBox" 
    DialogResult="HandleHierarchyValidation" 
/>

<MessageBox 
    Title="Attention"
    Message=@errorMessage
    IsVisible="@showError"
    IsLocalized=true
    OnClose="HideErrorMessage" 
/>
