﻿@page "/Account/ConfirmEmail"

@using System.Text
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities

@inject UserManager<ApplicationUser> UserManager
@inject IdentityRedirectManager RedirectManager

<PageTitle>Confirm Email | C4L</PageTitle>

<h1 class="page-title">Confirm email</h1>

<StatusMessage Message="@statusMessage" AlertType="@alertType" />

@code {
    private string? statusMessage;
    private string? alertType;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromQuery]
    private string? UserId { get; set; }

    [SupplyParameterFromQuery]
    private string? Code { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (UserId is null || Code is null)
        {
            RedirectManager.RedirectTo("/");
        }

        var user = await UserManager.FindByIdAsync(UserId);
        if (user is null)
        {
            HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
            alertType = "danger";
            statusMessage = $"Error loading user with id {UserId}";
        }
        else
        {
            var code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(Code));
            var result = await UserManager.ConfirmEmailAsync(user, code);
            alertType = result.Succeeded ? "success" : "danger";
            statusMessage = result.Succeeded ? "Thank you for confirming your email." : "Error confirming your email.";
        }
    }
}
