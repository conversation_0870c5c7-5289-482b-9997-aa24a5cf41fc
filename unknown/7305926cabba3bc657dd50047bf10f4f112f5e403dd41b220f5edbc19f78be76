﻿@page "/forgot-password"

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities

@inject UserManager<ApplicationUser> User<PERSON>anager
@inject IEmailSender<ApplicationUser> EmailSender
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager

<PageTitle>Forgot Password | C4L</PageTitle>

<h1 class="page-title block-end-margin">Forgot your password?</h1>

<div class="c4l-form c4l-forgot-password-form">
    <EditForm Model="Input" FormName="forgot-password" OnValidSubmit="OnValidSubmitAsync" method="post">
        <DataAnnotationsValidator />
        <ValidationSummary class="text-danger" role="alert" />

        <h3 class="c4l-form-heading">Enter your email address to reset your password</h3>

        <div class="form-floating mb-3">
            <InputText @bind-Value="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="<EMAIL>" />
            <label for="email" class="form-label">Email</label>
            <ValidationMessage For="() => Input.Email" class="text-danger" />
        </div>

        <button type="submit" class="c4l-button c4l-primary-button c4l-form-button">Reset password</button>
    </EditForm>
</div>

@code {
    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    private async Task OnValidSubmitAsync()
    {
        var user = await UserManager.FindByEmailAsync(Input.Email);
        if (user is null || !(await UserManager.IsEmailConfirmedAsync(user)))
        {
            // Don't reveal that the user does not exist or is not confirmed
            RedirectManager.RedirectTo("account/forgot-password-confirmation");
        }

        // For more information on how to enable account confirmation and password reset please
        // visit https://go.microsoft.com/fwlink/?LinkID=532713
        var code = await UserManager.GeneratePasswordResetTokenAsync(user);
        code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
        var callbackUrl = NavigationManager.GetUriWithQueryParameters(
            NavigationManager.ToAbsoluteUri("account/reset-password").AbsoluteUri,
            new Dictionary<string, object?> { ["code"] = code });

        await EmailSender.SendPasswordResetLinkAsync(user, Input.Email, HtmlEncoder.Default.Encode(callbackUrl));

        RedirectManager.RedirectTo("account/forgot-password-confirmation");
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";
    }
}
