﻿namespace Compass.Common.SessionHandlers
{
    public class CurrentCultureObserver : Observer
    {
        private string currentCulture = "en-US";

        public string GetCurrentCulture()
        {
            return currentCulture;
        }   
        public void SetCurrentCulture(string culture) 
        {
            currentCulture = culture;
            BroadcastStateChange();
        }
    }
}
