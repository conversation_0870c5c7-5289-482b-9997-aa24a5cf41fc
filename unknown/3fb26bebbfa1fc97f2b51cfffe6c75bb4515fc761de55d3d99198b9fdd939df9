﻿using Compass.Common.Data;
using Compass.Common.DTOs.Site;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Runtime.CompilerServices;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class SiteRepository : ISiteRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public SiteRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        async Task<Site> ISiteRepository.CreateSiteAsync(Site site)
        {
            // Get the authentication state
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            site.ModId = userId;
            site.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.Sites.AddAsync(site);
                await _dbContext.SaveChangesAsync();
            }

            return site;
        }

        async Task<List<Site>?> ISiteRepository.GetSitesAsync(long organizationId, long entity3Id)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Sites.Where(o => o.OrganizationId == organizationId && o.Entity3Id == entity3Id).ToListAsync();
            }
        }

        async Task<Site?> ISiteRepository.GetSiteAsync(long? id)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Sites.FirstOrDefaultAsync(o => o.Id == id);
            }
        }

        async Task<Site?> ISiteRepository.UpdateSiteAsync(long? id, Site site)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (site is null)
            {
                return null;
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                var existingSite = await _dbContext.Sites.FindAsync(id);

                if (existingSite is null)
                {
                    return null;
                }

                // Get the authentication state
                var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                var user = authState.User;
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                existingSite.ModId = userId;
                existingSite.ModTs = DateTime.Now;

                existingSite.Name = site.Name;
                existingSite.Address1 = site.Address1;
                existingSite.Address2 = site.Address2;
                existingSite.City = site.City;
                existingSite.State = site.State;
                existingSite.ZipCode = site.ZipCode;

                existingSite.ContactEmail = site.ContactEmail;
                existingSite.ContactFirstName = site.ContactFirstName;
                existingSite.ContactLastName = site.ContactLastName;
                existingSite.ContactFax = site.ContactFax;
                existingSite.ContactPhone = site.ContactPhone;
                existingSite.Fax = site.Fax;

                _dbContext.Sites.Update(existingSite);
                await _dbContext.SaveChangesAsync();

                return existingSite;
            }
        }

        public async Task<List<SiteListDisplayDto>> GetSiteList(SiteListAction action)
        {
            var results = new List<SiteListDisplayDto>();
            PageQuery pageQuery = action.pageQuery;
            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", action.userId),
                new SqlParameter("@OrganizationId", action.organizationId),
                new SqlParameter("@SearchCriteria", pageQuery.QueryText),
                new SqlParameter("@PageOffset", pageQuery.GetOffset()),
                new SqlParameter("@PageSize", pageQuery.PageSize)
            };

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                // Use ADO.NET directly for more control
                using var command = _dbContext.Database.GetDbConnection().CreateCommand();
                command.CommandText = "cmn_accessible_site_get_list";
                command.CommandType = CommandType.StoredProcedure;

                foreach (var param in parameters)
                {
                    command.Parameters.Add(param);
                }

                // Ensure connection is open
                if (command.Connection.State != ConnectionState.Open)
                {
                    await command.Connection.OpenAsync();
                }

                try
                {
                    using var reader = await command.ExecuteReaderAsync();

                    // Check if we have any rows
                    if (!reader.HasRows)
                    {
                        return results; // Return empty list
                    }

                    while (await reader.ReadAsync())
                    {
                        try
                        {
                            var site = new SiteListDisplayDto
                            {
                                // Use GetOrdinal to find column positions and safely get values
                                Id = reader.GetInt64(reader.GetOrdinal("id")),
                                Name = reader.IsDBNull(reader.GetOrdinal("name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("name")),
                                ContactName = reader.IsDBNull(reader.GetOrdinal("ContactName"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactName")),
                                ContactEmail = reader.IsDBNull(reader.GetOrdinal("ContactEmail"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactEmail")),
                                ContactPhone = reader.IsDBNull(reader.GetOrdinal("ContactPhone"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactPhone")),
                                Entity1Name = reader.IsDBNull(reader.GetOrdinal("Entity1Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity1Name")),
                                Entity1Id = reader.GetInt64(reader.GetOrdinal("Entity1Id")),
                                Entity2Name = reader.IsDBNull(reader.GetOrdinal("Entity2Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity2Name")),
                                Entity2Id = reader.GetInt64(reader.GetOrdinal("Entity2Id")),
                                Entity3Name = reader.IsDBNull(reader.GetOrdinal("Entity3Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity2Name")),
                                Entity3Id = reader.GetInt64(reader.GetOrdinal("Entity3Id"))
                            };

                            results.Add(site);
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue processing other rows
                            System.Diagnostics.Debug.WriteLine($"Error processing row: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error executing stored procedure: {ex.Message}");
                    throw; // Rethrow the exception after logging
                }
            }

            return results;
        }

        public async Task<int> GetSiteCount(long? organizationId, long? entity1Id, long? entity2Id, long? entity3Id, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

            SqlParameter[] parameters;
            string sqlQuery;
            if (entity3Id != null)
            {
                parameters = new SqlParameter[]
               {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OrganizationId", organizationId),
                    new SqlParameter("@Entity3Id", entity3Id),
                    new SqlParameter("@SearchCriteria", queryText)
               };

                sqlQuery = "EXEC [cmn_accessible_site_get_count] @UserId, @OrganizationId, NULL, NULL, @Entity3Id, @SearchCriteria";
            }
            else if (entity2Id != null)
            {
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OrganizationId", organizationId),
                    new SqlParameter("@Entity2Id", entity2Id),
                    new SqlParameter("@SearchCriteria", queryText)
                };

                sqlQuery = "EXEC [cmn_accessible_site_get_count] @UserId, @OrganizationId, NULL, @Entity2Id, NULL, , @SearchCriteria";
            }
            else if (entity1Id != null)
            {
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OrganizationId", organizationId),
                    new SqlParameter("@Entity1Id", entity1Id),
                    new SqlParameter("@SearchCriteria", queryText)
                };

                sqlQuery = "EXEC [cmn_accessible_site_get_count] @UserId, @OrganizationId, @Entity1Id, NULL, NULL, @SearchCriteria";
            }
            else
            {
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OrganizationId", organizationId),
                    new SqlParameter("@SearchCriteria", queryText)
                };

                sqlQuery = "EXEC [cmn_accessible_site_get_count] @UserId, @OrganizationId, NULL, NULL, NULL, @SearchCriteria";
            }

            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, parameters);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<int> results = await _dbContext.Database
                                .SqlQuery<int>(formattedQuery)
                                .ToListAsync();

                int count = results.FirstOrDefault();

                return count;
            }
        }

        public async Task<bool> DeleteSite(long? id)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                Site? existingSite = await _dbContext.Sites.FindAsync(id);

                if (existingSite is null)
                {
                    throw new Exception("Site not found");
                }

                // Get the authentication state
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                if (userId != null)
                {
                    existingSite.ModId = userId;
                    existingSite.ModTs = DateTime.Now;

                    existingSite.IsDeleted = "Y";

                    _dbContext.Sites.Update(existingSite);
                    await _dbContext.SaveChangesAsync();

                    return true;
                }
                else
                {
                    throw new Exception("UserID not found");
                }
            }
        }

        public async Task<int> GetActiveStudentCount(long? organizationId, long? siteId)
        {
            if (siteId is null)
            {
                throw new ArgumentNullException(nameof(siteId));
            }

            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            string sqlQuery = @"SELECT COUNT(DISTINCT r.student_id) AS Value
                                FROM cmn_student_group_roster AS r
                                WHERE r.organization_id = {0}
	                                AND r.site_id = {1}";

            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, organizationId, siteId);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                int count = await _dbContext.Database.SqlQuery<int>(formattedQuery).FirstAsync();

                return count;
            }
        }
    }
}
