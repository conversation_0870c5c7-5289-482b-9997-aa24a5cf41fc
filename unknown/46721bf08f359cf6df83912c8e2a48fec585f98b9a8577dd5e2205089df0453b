﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Compass.Common.Models
{
    [Table("cmn_organization_hierarchies")]
    public class OrganizationHierarchy
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long OrganizationId { get; set; }

        [Column("mod_id")]
        public string ModId { get; set; }
        
        [Column("mod_ts")]
        public DateTime ModTs { get; set; }
        
        [Column("hierarchy_entity_1_entity_name")]
        public string HierarchyEntity1EntityName { get; set; } = string.Empty;
        
        [Column("hierarchy_entity_2_entity_name")]
        public string HierarchyEntity2EntityName { get; set; } = string.Empty;
        
        [Column("hierarchy_entity_3_entity_name")]
        public string HierarchyEntity3EntityName { get; set; } = string.Empty;
        
        [Column("hierarchy_site_entity_name")]
        public string HierarchySiteEntityName { get; set; } = string.Empty;
        
        [Column("hierarchy_student_group_entity_name")]
        public string HierarchyStudentGroupEntityName { get; set; } = string.Empty;
        
        [Column("hierarchy_student_entity_name")]
        public string HierarchyStudentEntityName { get; set; } = string.Empty;
    }
}
