.summary-section-wrapper {
  border-radius: 0.25rem;
  margin-block: 3rem;
  margin-inline: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: fit-content;
  min-width: 600px;
}

.summary-title {
  background: var(--c4l-primary-purple);
  padding: 1.25rem;
  border-radius: 0.25rem 0.25rem 0 0;
}

.summary-title-underline {
  display: block;
  width: 50px;
  height: 3px;
  background: var(--c4l-secondary-teal);
  margin-block-start: 0.25rem;
}

.summary-wrapper {
  grid-template-columns: 1fr;
  gap: 2rem;
  padding: 1.25rem;
}

.summary-detail {
  font-size: 1rem;
  display: flex;
  justify-content: space-between;
  margin-block-end: 0.5rem;
  border-block-end: 1px solid var(--c4l-primary-purple);
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #333;

  & svg {
    transform: translateY(-2px);
  }
}

@media (min-width: 40rem) {
  .summary-wrapper {
    grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
  }
}
