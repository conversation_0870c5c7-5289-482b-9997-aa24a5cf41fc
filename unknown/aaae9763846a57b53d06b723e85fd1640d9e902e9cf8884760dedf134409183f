﻿@page "/account/manage-email"

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities

@inject UserManager<ApplicationUser> UserManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject IdentityUserAccessor UserAccessor
@inject NavigationManager NavigationManager

<PageTitle>Manage Email | C4L</PageTitle>

<StatusMessage Message="@message" AlertType="@alertType" />

<form @onsubmit="OnSendEmailVerificationAsync" @formname="send-verification" id="send-verification-form" method="post">
    <AntiforgeryToken />
</form>

<EditForm Model="Input" FormName="change-email" OnValidSubmit="OnValidSubmitAsync" method="post" class="c4l-form">
    <h3 class="text-center c4l-form-heading">Manage Email</h3>

    <DataAnnotationsValidator />
    <ValidationSummary class="text-danger" role="alert" />

    @if (isEmailConfirmed)
    {
        <div>
            <div class="form-floating mb-3 input-group">
                <input type="text" value="@email" class="form-control" id="current-email" placeholder="Please enter your email." disabled />
                <div class="input-group-append d-flex manage-email-checkbox-wrapper">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#00bc85" class="bi bi-check2-circle" viewBox="0 0 16 16">
                        <path d="M2.5 8a5.5 5.5 0 0 1 8.25-4.764.5.5 0 0 0 .5-.866A6.5 6.5 0 1 0 14.5 8a.5.5 0 0 0-1 0 5.5 5.5 0 1 1-11 0"/>
                        <path d="M15.354 3.354a.5.5 0 0 0-.708-.708L8 9.293 5.354 6.646a.5.5 0 1 0-.708.708l3 3a.5.5 0 0 0 .708 0z"/>
                    </svg>
                </div>
                <label for="current-email" class="form-label">Current email</label>
            </div>
        </div>
    }
    else
    {
        <div class="form-floating mb-3">
            <input type="text" value="@email" class="form-control" id="current-email" placeholder="Please enter your email." disabled />
            <label for="current-email" class="form-label">Email</label>
            <button type="submit" class="btn btn-link" form="send-verification-form">Send verification email</button>
        </div>
    }
    <div class="form-floating mb-3">
        <InputText @bind-Value="Input.NewEmail" class="form-control" id="new-email" autocomplete="email" aria-required="true" placeholder="Please enter new email." />
        <label for="new-email" class="form-label" id="new-email">New email</label>
        <ValidationMessage For="() => Input.NewEmail" class="text-danger" />
    </div>

    <div class="c4l-form-submit-buttons-wrapper">
        <button type="submit" class="c4l-button c4l-form-button c4l-primary-button">Change email</button>
    </div>
</EditForm>

@code {
    private string? message;
    private string? alertType = "info";
    private ApplicationUser user = default!;
    private string? email;
    private bool isEmailConfirmed;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm(FormName = "change-email")]
    private InputModel Input { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);
        email = await UserManager.GetEmailAsync(user);
        isEmailConfirmed = await UserManager.IsEmailConfirmedAsync(user);

        Input.NewEmail ??= email;
    }

    private async Task OnValidSubmitAsync()
    {
        if (Input.NewEmail is null || Input.NewEmail == email)
        {
            message = "Your email is unchanged.";
            return;
        }

        var userId = await UserManager.GetUserIdAsync(user);
        var code = await UserManager.GenerateChangeEmailTokenAsync(user, Input.NewEmail);
        code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
        var callbackUrl = NavigationManager.GetUriWithQueryParameters(
            NavigationManager.ToAbsoluteUri("account/confirm-email-change").AbsoluteUri,
            new Dictionary<string, object?> { ["userId"] = userId, ["email"] = Input.NewEmail, ["code"] = code });

        await EmailSender.SendConfirmationLinkAsync(user, Input.NewEmail, HtmlEncoder.Default.Encode(callbackUrl));

        alertType = "success";
        message = "Confirmation link to change email sent. Please check your email.";
    }

    private async Task OnSendEmailVerificationAsync()
    {
        if (email is null)
        {
            return;
        }

        var userId = await UserManager.GetUserIdAsync(user);
        var code = await UserManager.GenerateEmailConfirmationTokenAsync(user);
        code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
        var callbackUrl = NavigationManager.GetUriWithQueryParameters(
            NavigationManager.ToAbsoluteUri("Account/ConfirmEmail").AbsoluteUri,
            new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code });

        await EmailSender.SendConfirmationLinkAsync(user, email, HtmlEncoder.Default.Encode(callbackUrl));

        alertType = "success";
        message = "Verification email sent. Please check your email.";
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        [Display(Name = "New email")]
        public string? NewEmail { get; set; }
    }
}
