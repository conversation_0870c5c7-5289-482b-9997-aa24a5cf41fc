﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.StudentGroup;
using Compass.Common.Helpers;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.StudentGroup
{
    public partial class StudentGroupList
    {
        private List<StudentGroupListDisplayDto> studentGroupResults = new();
        private string studentGroupHierarchy = string.Empty;
        private string siteHierarchy = string.Empty;
        private string entity3Hierarchy = string.Empty;
        private string entity2Hierarchy = string.Empty;
        private string entity1Hierarchy = string.Empty;

        private int maxPages;
        private int currentPage;

        private long? currentOrganizationId;
        private long? currentSiteId;

        private bool noSearchResults = false;
        private bool isLoading = true;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
                if (commonSessionData != null)
                {
                    currentOrganizationId = commonSessionData.CurrentOrganizationId;
                }
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            searchText = string.Empty;
            currentSearchText = string.Empty;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                studentGroupHierarchy = commonSessionData.StudentGroupHierarchy;
                siteHierarchy = commonSessionData.SiteHierarchy;
                entity3Hierarchy = commonSessionData.Entity3Hierarchy;
                entity2Hierarchy = commonSessionData.Entity2Hierarchy;
                entity1Hierarchy = commonSessionData.Entity1Hierarchy;

                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                currentSiteId = commonSessionData.CurrentSiteId;

                currentPage = 1;
                maxPages = 0;
                await GetStudentGroupPage();
            }
        }

        private async Task GetStudentGroupPage()
        {
            isLoading = true;
            StudentGroupListAction action = new();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            if (currentOrganizationId is not null)
            {
                action.PageQuery = pageQuery;

                action.OrganizationId = currentOrganizationId;
                action.UserId = _currentUserId;
                action.SiteId = currentSiteId;

                KaplanPageable<StudentGroupListDisplayDto> currentPage = await StudentGroupService.GetStudentGroupPage(action);

                studentGroupResults = currentPage.PageContent;
                maxPages = currentPage.MaxPages;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && studentGroupResults.Count == 0;
                isLoading = false;

                StateHasChanged();
            }
        }

        protected async Task OnSiteSelected(StudentGroupListDisplayDto studentGroup)
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                commonSessionData.CurrentStudentGroupId = studentGroup.Id;
                commonSessionData.CurrentSiteId = studentGroup.SiteId;

                if (studentGroup.Name != null)
                {
                    commonSessionData.SelectedEntityName = studentGroup.Name;
                }
                else
                {
                    commonSessionData.SelectedEntityName = string.Empty;
                }

                if (_currentUser != null)
                {
                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();

                    NavigationManager.NavigateTo($"/studentgroup");
                }
            }
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetStudentGroupPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetStudentGroupPage();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetStudentGroupPage();
        }
    }
}
