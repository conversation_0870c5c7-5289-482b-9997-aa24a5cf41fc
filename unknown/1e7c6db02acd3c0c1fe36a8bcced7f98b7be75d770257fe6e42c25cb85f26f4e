﻿@page "/Account/Manage"

@using System.ComponentModel.DataAnnotations
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity

@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityUserAccessor UserAccessor

<PageTitle>Manage Profile | C4L</PageTitle>
<StatusMessage Message="@_statusMessageText" AlertType="@alertType" />

<EditForm Model="Input" FormName="profile" OnValidSubmit="OnValidSubmitAsync" method="post" class="c4l-form">
    <h2 class="h3 text-center c4l-form-heading">Manage Profile</h2>

    <DataAnnotationsValidator />
    <ValidationSummary class="text-danger" role="alert" />
    
    <div class="form-floating mb-3">
        <InputText @bind-Value="Input.UserName" class="form-control" id="username" placeholder="Please enter your username." />
        <label for="username" class="form-label">Username</label>
        <ValidationMessage For="() => Input.UserName" class="text-danger" />
    </div>

    <div class="form-floating mb-3">
        <InputText @bind-Value="Input.FirstName" class="form-control" id="first-name" placeholder="Please enter your first name." />
        <label for="first-name" class="form-label">First Name</label>
        <ValidationMessage For="() => Input.FirstName" class="text-danger" />
    </div>

    <div class="form-floating mb-3">
        <InputText @bind-Value="Input.LastName" class="form-control" id="last-name" placeholder="Please enter your last name." />
        <label for="last-name" class="form-label">Last Name</label>
        <ValidationMessage For="() => Input.LastName" class="text-danger" />
    </div>

    <div class="form-floating mb-3">
        <InputText @bind-Value="Input.PhoneNumber" class="form-control" id="phone-number" placeholder="Please enter your phone number." />
        <label for="phone-number" class="form-label">Phone number</label>
        <ValidationMessage For="() => Input.PhoneNumber" class="text-danger" />
    </div>

    <div class="form-submit-buttons-wrapper">
        <button type="submit" class="c4l-button c4l-primary-button c4l-form-button">Update</button>
    </div>
</EditForm>
