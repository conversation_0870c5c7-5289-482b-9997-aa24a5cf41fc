using Compass.C4L.Models;

namespace Compass.C4L.Interfaces.Repositories
{
    public interface IC4LNonContactDayRepository
    {
        Task<IEnumerable<C4LNonContactDay>> GetByClassroomIdAsync(long classroomId);
        Task<C4LNonContactDay> CreateAsync(C4LNonContactDay nonContactDay);
        Task<C4LNonContactDay> UpdateAsync(C4LNonContactDay nonContactDay);
        Task DeleteAsync(long id);
        Task<bool> HasOverlappingDates(long classroomId, DateTime startDate, DateTime endDate, long? excludeId = null);
        Task<C4LNonContactDay?> GetByIdAsync(long id);
        Task<List<C4LNonContactDay>> GetNonContactDaysWithinDateRange(long classroomId, DateTime startDate, DateTime endDate);
    }
}
