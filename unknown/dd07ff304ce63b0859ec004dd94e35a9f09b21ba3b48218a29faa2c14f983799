﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.Models;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Admin.Site.Manage
{
    public partial class SiteSchoolYearListComponent
    {
        [Parameter]
        public EventCallback OnReturn { get; set; }

        private List<SchoolYear> schoolYearPage = new();
        private SchoolYear? currentSchoolYear = null;

        private bool isSchoolYearBoxVisible = false;
        private bool noSearchResults = false;

        private long? currentOrganizationId;
        private long? currentSiteId;

        private int maxPages;
        private int currentPage;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            searchText = string.Empty;
            currentSearchText = string.Empty;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                currentSiteId = commonSessionData.CurrentSiteId;

                currentPage = 1;
                maxPages = 0;
                await GetSchoolYearPage();
                await GetCurrentSchoolYear();
            }
        }

        private async Task GetCurrentSchoolYear()
        {
            currentSchoolYear = await SiteService.GetCurrentSchoolYear(currentOrganizationId, currentSiteId);
            StateHasChanged();
        }

        private async Task GetSchoolYearPage()
        {
            SchoolYearListAction action = new SchoolYearListAction();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            action.PageQuery = pageQuery;
            if (currentOrganizationId != null && currentSiteId != null)
            {
                action.OrganizationId = currentOrganizationId;
                action.SiteId = currentSiteId;

                KaplanPageable<SchoolYear> currentPage = await SiteService.GetSchoolYearPage(action);

                schoolYearPage = currentPage.PageContent;
                maxPages = currentPage.MaxPages;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && schoolYearPage.Count == 0;

                StateHasChanged();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetSchoolYearPage();
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetSchoolYearPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetSchoolYearPage();
            }
        }

        protected void OnCreateSchoolYear()
        {
            isSchoolYearBoxVisible = true;
        }

        protected async Task OnSchoolYearResult(bool result)
        {
            this.currentPage = 1;
            currentSearchText = searchText;
            isSchoolYearBoxVisible = false;
            await GetSchoolYearPage();
        }

        protected async Task SetToCurrentSchoolYear(SchoolYear schoolYear)
        {
            await SiteService.SetCurrentSchoolYear(schoolYear);
            await GetCurrentSchoolYear();
        }
    }
}
