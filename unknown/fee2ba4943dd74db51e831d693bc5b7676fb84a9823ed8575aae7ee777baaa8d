using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.LicensePool;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Services
{
    public interface ILicensePoolService
    {
        public Task<LicensePool?> UpdateLicensePoolAsync(long? id, LicensePool licensePool);
        public Task<LicensePool?> GetLicensePoolAsync(long? licensePoolId);
        public Task<LicensePool> AddLicensePoolAsync(LicensePool licensePool);
        public Task<KaplanPageable<LicensePoolListDisplayDto>> GetLicensePoolDisplayPages(PageQuery pageQuery, long? organizationId);
        public Task<KaplanPageable<LicensePool>> GetLicensePoolSummaryPages(PageQuery pageQuery, long? organizationId);
        public Task<bool> CheckActiveLicense(long? organizationId, string? product);
        Task<long?> GetAvailableLicensePoolId(long? organizationId, string? product);
        Task<bool> IncrementLicense(long? organizationId, long? entityID, long? licensePoolId, ILicenseRepository licenseRepository);
    }
}