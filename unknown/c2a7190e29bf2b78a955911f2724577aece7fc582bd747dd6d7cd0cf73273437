using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_subscales_lookup")]
    public class LAPSubScalesLookup
    {
        [Key]
        [Column("StaticID")]
        public long StaticId { get; set; }

        [Required]
        [Column("Domain")]
        [StringLength(50)]
        public string Domain { get; set; } = string.Empty;

        [Required]
        [Column("Subscale")]
        [StringLength(50)]
        public string Subscale { get; set; } = string.Empty;

        [Required]
        [Column("SubscaleID")]
        [StringLength(5)]
        public string SubscaleId { get; set; } = string.Empty;

        [Required]
        [Column("DomainSequence")]
        public int DomainSequence { get; set; }

        [Required]
        [Column("SubscaleSequence")]
        public int SubscaleSequence { get; set; }

        [Required]
        [Column("Instrument")]
        public int Instrument { get; set; }

        [Required]
        [Column("Language")]
        public int Language { get; set; }

        [Required]
        [Column("Potential")]
        public byte Potential { get; set; }
    }
}