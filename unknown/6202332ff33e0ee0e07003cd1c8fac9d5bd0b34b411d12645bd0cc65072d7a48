﻿@page "/license-add"
@page "/license-edit/{LicensePoolId:long?}"
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject NavigationManager NavigationManager
@inject UserSessionService UserSessionService
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject UserAccessor UserAccessor
@inject ILicensePoolService LicensePoolService

<PageTitle>@(LicensePoolId is not null ? "Edit" : "Add") License Pool</PageTitle>

<h1 class="page-title mb-5">Add/Edit License Pools</h1>

@if(isCurrentUserSuperAdmin)
{
    <EditForm Model="Input" FormName="formLicense" OnValidSubmit="OnValidSubmit" class="c4l-form license-pool-addedit-form">
        <DataAnnotationsValidator />
        <ValidationSummary />

        <h2 class="c4l-form-heading h3">@(LicensePoolId is not null ? "Edit" : "Add") License Pool</h2>

        <div class="form-label-input-wrapper">
            <label class="col-form-label" for="license-pool-product">Product Type:</label>
            <InputSelect id="license-pool-product" @bind-Value="Input.Product" class="form-control">
                <option value="" disabled>-- Select Product --</option>
                @foreach (string product in CompassResource.KaplanProducts)
                {
                    <option value="@product">@product</option>
                }
            </InputSelect>
        </div>

        @if (Input.Product == CompassResource.LAP)
        {
            <FieldComponent Label="Account Type">
                <Control>
                    <InputRadioGroup @bind-Value="Input.AccountingType">
                        <div class="form-check">
                            <InputRadio Value="@("Primary")" class="form-check-input" id="primary-account" />
                            <label class="form-check-label" for="primary-account">Primary</label>
                        </div>
                        <div class="form-check">
                            <InputRadio Value="@("Secondary")" class="form-check-input" id="secondary-account" />
                            <label class="form-check-label" for="secondary-account">Secondary</label>
                        </div>
                    </InputRadioGroup>
                </Control>
            </FieldComponent>
        }

        <FieldComponent Label="License Pool Name" LabelFor="license-pool-name">
            <Control>
                <InputText Id="license-pool-name" class="form-control" @bind-Value="Input.Name" Required="true" />
            </Control>
        </FieldComponent>

        <FieldComponent Label="Purchased Licenses" LabelFor="purchased-licenses">
            <Control>
                <InputNumber Id="purchased-licenses" class="form-control" @bind-Value="Input.PurchasedLicenses" Required="true" min="0" />
            </Control>
        </FieldComponent>

        <FieldComponent Label="Purchased Archived Licenses" LabelFor="purchased-archived-licenses">
            <Control>
                <InputNumber Id="purchased-archived-licenses" class="form-control" @bind-Value="Input.PurchasedArchivedLicenses" Required="true" min="0" />
            </Control>
        </FieldComponent>

        <FieldComponent Label="Start Date" LabelFor="license-start-date">
            <Control>
                <InputDate Id="license-start-date" class="form-control" @bind-Value="Input.BeginTs" />
            </Control>
        </FieldComponent>

        <FieldComponent Label="End Date" LabelFor="license-end-date">
            <Control>
                <InputDate Id="license-end-date" class="form-control" @bind-Value="Input.EndTs" />
            </Control>
        </FieldComponent>

        <FieldComponent Label="Notes" LabelFor="license-addedit-notes">
            <Control>
                <InputTextArea Id="license-addedit-notes" class="form-control" @bind-Value="Input.Notes" />
            </Control>
        </FieldComponent>

        <FieldComponent Label="Status">
            <Control>
                <InputRadioGroup @bind-Value="Input.Status">
                    <div class="form-check">
                        <InputRadio Value="@("Active")" class="form-check-input" id="license-active" />
                        <label class="form-check-label" for="license-active">Active</label>
                    </div>
                    <div class="form-check">
                        <InputRadio Value="@("Inactive")" class="form-check-input" id="license-inactive" />
                        <label class="form-check-label" for="license-inactive">Inactive</label>
                    </div>
                </InputRadioGroup>
            </Control>
        </FieldComponent>

        <div class="form-submit-buttons-wrapper">
            <button class="c4l-button c4l-form-button c4l-primary-button" type="submit">@(LicensePoolId is not null ? "Update" : "Add")</button>
            <button class="c4l-button c4l-form-button c4l-secondary-button" type="button" @onclick="() => OnCancelClick()">Cancel</button>

            @if (showSuccessMessage)
            {
                <div class="alert alert-success fade show" role="alert">
                    @successMessage
                </div>
            }
        </div>
    </EditForm>
}
