﻿using Compass.C4L.Helpers;
using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;

namespace Compass.C4L.Services
{
    public class GameTokenService : IGameTokenService
    {
        private readonly IGameTokenRepository _gameTokenRepository;

        public GameTokenService(IGameTokenRepository gameTokenRepository)
        {
            _gameTokenRepository = gameTokenRepository;
        }

        public async Task<C4LGameToken> CreateOneTimeToken(GameParameters parameters)
        {
            C4LGameToken gameToken = await _gameTokenRepository.CreateOneTimeToken(parameters);
            return gameToken;
        }
    }
}
