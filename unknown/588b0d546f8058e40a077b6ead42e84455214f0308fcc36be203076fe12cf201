﻿@page "/account/reset-password"

@using System.ComponentModel.DataAnnotations
@using System.Text
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities

@inject IdentityRedirectManager RedirectManager
@inject UserManager<ApplicationUser> UserManager

<PageTitle>Reset Password | C4L</PageTitle>

<h1 class="page-title mb-4">Reset password</h1>

<StatusMessage Message="@Message" AlertType="@alertType" />

<EditForm Model="Input" FormName="reset-password" OnValidSubmit="OnValidSubmitAsync" method="post" class="c4l-form">
    <DataAnnotationsValidator />
    <ValidationSummary class="text-danger" role="alert" />

    <h3 class="c4l-form-heading">Reset your password.</h3>

    <input type="hidden" name="Input.Code" value="@Input.Code" />
    <div class="form-floating mb-3">
        <InputText @bind-Value="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="<EMAIL>" />
        <label for="email" class="form-label">Email</label>
        <ValidationMessage For="() => Input.Email" class="text-danger" />
    </div>
    <div class="form-floating mb-3">
        <InputText type="password" @bind-Value="Input.Password" class="form-control" autocomplete="new-password" aria-required="true" placeholder="Please enter your password." />
        <label for="password" class="form-label">Password</label>
        <ValidationMessage For="() => Input.Password" class="text-danger" />
    </div>
    <div class="form-floating mb-3">
        <InputText type="password" @bind-Value="Input.ConfirmPassword" class="form-control" autocomplete="new-password" aria-required="true" placeholder="Please confirm your password." />
        <label for="confirm-password" class="form-label">Confirm password</label>
        <ValidationMessage For="() => Input.ConfirmPassword" class="text-danger" />
    </div>

    <button type="submit" class="c4l-button c4l-form-button c4l-tertiary-button">Reset password</button>
</EditForm>

@code {
    private string? alertType = "danger";
    private IEnumerable<IdentityError>? identityErrors;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? Code { get; set; }

    private string? Message => identityErrors is null ? null : $"Error: {string.Join(", ", identityErrors.Select(error => error.Description))}";

    protected override void OnInitialized()
    {
        if (Code is null)
        {
            RedirectManager.RedirectTo("account/invalid-password-reset");
        }

        Input.Code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(Code));
    }

    private async Task OnValidSubmitAsync()
    {
        var user = await UserManager.FindByEmailAsync(Input.Email);
        if (user is null)
        {
            // Don't reveal that the user does not exist
            RedirectManager.RedirectTo("account/reset-password-confirmation");
        }

        var result = await UserManager.ResetPasswordAsync(user, Input.Code, Input.Password);
        if (result.Succeeded)
        {
            RedirectManager.RedirectTo("account/reset-password-confirmation");
        }

        identityErrors = result.Errors;
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";

        [Required]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [DataType(DataType.Password)]
        [Display(Name = "Confirm password")]
        [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; } = "";

        [Required]
        public string Code { get; set; } = "";
    }
}
