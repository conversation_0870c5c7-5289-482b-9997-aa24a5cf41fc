﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.StudentGroup;
using Compass.Common.Helpers;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Prompts.Generic
{
    public partial class StudentAssignBox
    {
        [Parameter]
        public EventCallback<StudentGroupListDisplayDto?> AssignResult { get; set; }

        [Parameter]
        public bool IsVisible { get; set; }

        public required long? selectedStudentId { get; set; }

        private long? currentOrganizationId;
        private long? currentSiteId;

        private List<StudentGroupListDisplayDto> unAssignedStudentGroupResults = new();
        private string studentGroupHierarchy = string.Empty;
        private string siteHierarchy = string.Empty;
        private string entity3Hierarchy = string.Empty;
        private string entity2Hierarchy = string.Empty;
        private string entity1Hierarchy = string.Empty;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        private int maxPages;
        private int currentPage;

        private bool isLoading = false;
        private bool noSearchResults = false;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            await GetCommonSessionData();

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                studentGroupHierarchy = commonSessionData.StudentGroupHierarchy;
                siteHierarchy = commonSessionData.SiteHierarchy;
                entity3Hierarchy = commonSessionData.Entity3Hierarchy;
                entity2Hierarchy = commonSessionData.Entity2Hierarchy;
                entity1Hierarchy = commonSessionData.Entity1Hierarchy;

                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                currentSiteId = commonSessionData.CurrentSiteId;
                selectedStudentId = commonSessionData.CurrentStudentId;

                currentPage = 1;
                maxPages = 0;
                await GetStudentGroupPage();
            }
        }

        private async Task GetStudentGroupPage()
        {
            isLoading = true;
            AssignStudentGroupListAction action = new();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            if (currentOrganizationId is not null)
            {
                action.PageQuery = pageQuery;

                action.OrganizationId = currentOrganizationId;
                action.UserId = _currentUserId;
                action.SiteId = currentSiteId;
                action.StudentId = selectedStudentId;

                KaplanPageable<StudentGroupListDisplayDto> currentPage = await StudentService.GetUnAssignedStudentGroupPage(action);

                unAssignedStudentGroupResults = currentPage.PageContent;
                maxPages = currentPage.MaxPages;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && unAssignedStudentGroupResults.Count == 0;
                isLoading = false;

                StateHasChanged();
            }
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetStudentGroupPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetStudentGroupPage();
            }
        }

        protected async Task OnSearch()
        {
            this.currentPage = 1;
            currentSearchText = searchText;
            noSearchResults = false;
            await GetStudentGroupPage();
        }

        protected async Task OnStudentGroupSelected(StudentGroupListDisplayDto studentGroup)
        {
            long? studentGroupId = studentGroup.Id;
            await StudentService.AssignToStudentGroup(selectedStudentId, currentOrganizationId, studentGroupId);
            await AssignResult.InvokeAsync(studentGroup);
        }

        protected async Task OnCancelClick()
        {
            await AssignResult.InvokeAsync(null);
        }
    }
}
