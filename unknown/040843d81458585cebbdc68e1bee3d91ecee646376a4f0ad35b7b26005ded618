﻿using Compass.Common.Data;
using Compass.Common.DTOs.StudentGroup;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Data.Common;
using System.Runtime.CompilerServices;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class StudentGroupRepository : IStudentGroupRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public StudentGroupRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<StudentGroup> CreateStudentGroupAsync(StudentGroup studentGroup)
        {
            // Get the authentication state
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            studentGroup.ModId = userId;
            studentGroup.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.StudentGroups.AddAsync(studentGroup);
                await _dbContext.SaveChangesAsync();
            }

            return studentGroup;
        }

        public async Task<StudentGroup?> GetStudentGroupAsync(long? id)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                StudentGroup? studentGroup = await _dbContext.StudentGroups.FirstOrDefaultAsync(o => o.Id == id && o.IsDeleted == "N");
                return studentGroup;
            }
        }

        public async Task<List<StudentGroup>?> GetStudentGroupsAsync(long? organizationId, long? siteId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (siteId is null)
            {
                throw new ArgumentNullException(nameof(siteId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.StudentGroups.Where(o => o.OrganizationId == organizationId && o.SiteId == siteId).ToListAsync();
            }
        }

        public async Task<StudentGroup?> UpdateStudentGroupAsync(long? id, StudentGroup? studentGroup)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            if (studentGroup is null)
            {
                throw new ArgumentNullException(nameof(studentGroup));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                StudentGroup? existingStudentGroup = await _dbContext.StudentGroups.FindAsync(id);

                if (existingStudentGroup is null)
                {
                    return null;
                }

                // Get the authentication state
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                existingStudentGroup.ModId = userId;
                existingStudentGroup.ModTs = DateTime.Now;

                existingStudentGroup.Name = studentGroup.Name;

                _dbContext.StudentGroups.Update(existingStudentGroup);
                await _dbContext.SaveChangesAsync();

                return existingStudentGroup;
            }
        }

        public async Task<List<StudentGroupListDisplayDto>> GetStudentGroupList(StudentGroupListAction action)
        {
            List<StudentGroupListDisplayDto> results = new List<StudentGroupListDisplayDto>();
            PageQuery pageQuery = action.PageQuery;
            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", action.UserId),
                new SqlParameter("@OrganizationId", action.OrganizationId),
                new SqlParameter("@SearchCriteria", pageQuery.QueryText),
                new SqlParameter("@PageOffset", pageQuery.GetOffset()),
                new SqlParameter("@PageSize", pageQuery.PageSize)
            };

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                // Use ADO.NET directly for more control
                using DbCommand command = _dbContext.Database.GetDbConnection().CreateCommand();
                command.CommandText = "cmn_accessible_student_group_get_list";
                command.CommandType = CommandType.StoredProcedure;

                foreach (SqlParameter param in parameters)
                {
                    command.Parameters.Add(param);
                }

                // Ensure connection is open
                if (command.Connection.State != ConnectionState.Open)
                {
                    await command.Connection.OpenAsync();
                }

                try
                {
                    using DbDataReader reader = await command.ExecuteReaderAsync();

                    // Check if we have any rows
                    if (!reader.HasRows)
                    {
                        return results; // Return empty list
                    }

                    while (await reader.ReadAsync())
                    {
                        try
                        {
                            StudentGroupListDisplayDto studentGroup = new StudentGroupListDisplayDto
                            {
                                // Use GetOrdinal to find column positions and safely get values
                                Id = reader.GetInt64(reader.GetOrdinal("id")),
                                Name = reader.IsDBNull(reader.GetOrdinal("name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("name")),
                                Entity1Name = reader.IsDBNull(reader.GetOrdinal("Entity1Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity1Name")),
                                Entity1Id = reader.GetInt64(reader.GetOrdinal("Entity1Id")),
                                Entity2Name = reader.IsDBNull(reader.GetOrdinal("Entity2Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity2Name")),
                                Entity2Id = reader.GetInt64(reader.GetOrdinal("Entity2Id")),
                                Entity3Name = reader.IsDBNull(reader.GetOrdinal("Entity3Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity3Name")),
                                Entity3Id = reader.GetInt64(reader.GetOrdinal("Entity3Id")),
                                SiteName = reader.IsDBNull(reader.GetOrdinal("SiteName"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("SiteName")),
                                SiteId = reader.GetInt64(reader.GetOrdinal("SiteId"))
                            };

                            results.Add(studentGroup);
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue processing other rows
                            System.Diagnostics.Debug.WriteLine($"Error processing row: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error executing stored procedure: {ex.Message}");
                    throw; // Rethrow the exception after logging
                }
            }

            return results;
        }

        public async Task<int> GetStudentGroupCount(long? organizationId, long? siteId, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);


            SqlParameter[] parameters;
            string sqlQuery;
            if (siteId != null)
            {
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OrganizationId", organizationId),
                    new SqlParameter("@SiteId", siteId),
                    new SqlParameter("@SearchCriteria", queryText)
                };

                sqlQuery = "EXEC [cmn_accessible_student_group_get_count] @UserId, @OrganizationId, @SiteId, @SearchCriteria";
            }
            else
            {
                parameters = new SqlParameter[]
                {
                    new SqlParameter("@UserId", userId),
                    new SqlParameter("@OrganizationId", organizationId),
                    new SqlParameter("@SearchCriteria", queryText)
                };

                sqlQuery = "EXEC [cmn_accessible_student_group_get_count] @UserId, @OrganizationId, NULL, @SearchCriteria";
            }

            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, parameters);
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<int> results = await _dbContext.Database
                                .SqlQuery<int>(formattedQuery)
                                .ToListAsync();

                int count = results.FirstOrDefault();

                return count;
            }
        }

        public async Task<bool> DeleteStudentGroup(long? id)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                StudentGroup? existingStudentGroup = await _dbContext.StudentGroups.FindAsync(id);

                if (existingStudentGroup is null)
                {
                    throw new Exception("Student group not found");
                }

                // Get the authentication state
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                if (userId != null)
                {
                    existingStudentGroup.ModId = userId;
                    existingStudentGroup.ModTs = DateTime.Now;

                    existingStudentGroup.IsDeleted = "Y";

                    _dbContext.StudentGroups.Update(existingStudentGroup);
                    await _dbContext.SaveChangesAsync();

                    return true;
                }
                else
                {
                    throw new Exception("UserID not found");
                }
            }
        }

        public async Task<List<StudentGroupListDisplayDto>> GetAssignedStudentGroupList(AssignStudentGroupListAction action)
        {
            List<StudentGroupListDisplayDto> results = new List<StudentGroupListDisplayDto>();
            PageQuery pageQuery = action.PageQuery;
            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", action.UserId),
                new SqlParameter("@OrganizationId", action.OrganizationId),
                new SqlParameter("@SearchCriteria", pageQuery.QueryText),
                new SqlParameter("@StudentId", action.StudentId),
                new SqlParameter("@PageOffset", pageQuery.GetOffset()),
                new SqlParameter("@PageSize", pageQuery.PageSize)
            };

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                // Use ADO.NET directly for more control
                using DbCommand command = _dbContext.Database.GetDbConnection().CreateCommand();
                command.CommandText = "cmn_accessible_student_group_assigned_get_list";
                command.CommandType = CommandType.StoredProcedure;

                foreach (SqlParameter param in parameters)
                {
                    command.Parameters.Add(param);
                }

                // Ensure connection is open
                if (command.Connection.State != ConnectionState.Open)
                {
                    await command.Connection.OpenAsync();
                }

                try
                {
                    using DbDataReader reader = await command.ExecuteReaderAsync();

                    // Check if we have any rows
                    if (!reader.HasRows)
                    {
                        return results; // Return empty list
                    }

                    while (await reader.ReadAsync())
                    {
                        try
                        {
                            StudentGroupListDisplayDto studentGroup = new StudentGroupListDisplayDto
                            {
                                // Use GetOrdinal to find column positions and safely get values
                                Id = reader.GetInt64(reader.GetOrdinal("id")),
                                Name = reader.IsDBNull(reader.GetOrdinal("name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("name")),
                                Entity1Name = reader.IsDBNull(reader.GetOrdinal("Entity1Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity1Name")),
                                Entity1Id = reader.GetInt64(reader.GetOrdinal("Entity1Id")),
                                Entity2Name = reader.IsDBNull(reader.GetOrdinal("Entity2Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity2Name")),
                                Entity2Id = reader.GetInt64(reader.GetOrdinal("Entity2Id")),
                                Entity3Name = reader.IsDBNull(reader.GetOrdinal("Entity3Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity3Name")),
                                Entity3Id = reader.GetInt64(reader.GetOrdinal("Entity3Id")),
                                SiteName = reader.IsDBNull(reader.GetOrdinal("SiteName"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("SiteName")),
                                SiteId = reader.GetInt64(reader.GetOrdinal("SiteId"))
                            };

                            results.Add(studentGroup);
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue processing other rows
                            System.Diagnostics.Debug.WriteLine($"Error processing row: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error executing stored procedure: {ex.Message}");
                    throw; // Rethrow the exception after logging
                }
            }

            return results;
        }

        public async Task<int> GetAssignedStudentGroupCount(long? organizationId, long? studentId, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            if (studentId is null)
            {
                studentId = -1;
            }

            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", userId),
                new SqlParameter("@OrganizationId", organizationId),
                new SqlParameter("@StudentId", studentId),
                new SqlParameter("@SearchCriteria", queryText)
            };

            string sqlQuery = "EXEC [cmn_accessible_student_group_assigned_get_count] @UserId, @OrganizationId, @StudentId, @SearchCriteria";

            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, parameters);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<int> results = await _dbContext.Database
                                .SqlQuery<int>(formattedQuery)
                                .ToListAsync();

                int count = results.FirstOrDefault();

                return count;
            }
        }

        public async Task<List<StudentGroupListDisplayDto>> GetUnAssignedStudentGroupList(AssignStudentGroupListAction action)
        {
            List<StudentGroupListDisplayDto> results = new List<StudentGroupListDisplayDto>();
            PageQuery pageQuery = action.PageQuery;
            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", action.UserId),
                new SqlParameter("@OrganizationId", action.OrganizationId),
                new SqlParameter("@SearchCriteria", pageQuery.QueryText),
                new SqlParameter("@StudentId", action.StudentId),
                new SqlParameter("@PageOffset", pageQuery.GetOffset()),
                new SqlParameter("@PageSize", pageQuery.PageSize)
            };

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                // Use ADO.NET directly for more control
                using DbCommand command = _dbContext.Database.GetDbConnection().CreateCommand();
                command.CommandText = "cmn_accessible_student_group_unassigned_get_list";
                command.CommandType = CommandType.StoredProcedure;

                foreach (SqlParameter param in parameters)
                {
                    command.Parameters.Add(param);
                }

                // Ensure connection is open
                if (command.Connection.State != ConnectionState.Open)
                {
                    await command.Connection.OpenAsync();
                }

                try
                {
                    using DbDataReader reader = await command.ExecuteReaderAsync();

                    // Check if we have any rows
                    if (!reader.HasRows)
                    {
                        return results; // Return empty list
                    }

                    while (await reader.ReadAsync())
                    {
                        try
                        {
                            StudentGroupListDisplayDto studentGroup = new StudentGroupListDisplayDto
                            {
                                // Use GetOrdinal to find column positions and safely get values
                                Id = reader.GetInt64(reader.GetOrdinal("id")),
                                Name = reader.IsDBNull(reader.GetOrdinal("name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("name")),
                                Entity1Name = reader.IsDBNull(reader.GetOrdinal("Entity1Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity1Name")),
                                Entity1Id = reader.GetInt64(reader.GetOrdinal("Entity1Id")),
                                Entity2Name = reader.IsDBNull(reader.GetOrdinal("Entity2Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity2Name")),
                                Entity2Id = reader.GetInt64(reader.GetOrdinal("Entity2Id")),
                                Entity3Name = reader.IsDBNull(reader.GetOrdinal("Entity3Name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("Entity3Name")),
                                Entity3Id = reader.GetInt64(reader.GetOrdinal("Entity3Id")),
                                SiteName = reader.IsDBNull(reader.GetOrdinal("SiteName"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("SiteName")),
                                SiteId = reader.GetInt64(reader.GetOrdinal("SiteId"))
                            };

                            results.Add(studentGroup);
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue processing other rows
                            System.Diagnostics.Debug.WriteLine($"Error processing row: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error executing stored procedure: {ex.Message}");
                    throw; // Rethrow the exception after logging
                }
            }

            return results;
        }

        public async Task<int> GetUnAssignedStudentGroupCount(long? organizationId, long? studentId, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            if (studentId is null)
            {
                studentId = -1;
            }

            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", userId),
                new SqlParameter("@OrganizationId", organizationId),
                new SqlParameter("@StudentId", studentId),
                new SqlParameter("@SearchCriteria", queryText)
            };

            string sqlQuery = "EXEC [cmn_accessible_student_group_unassigned_get_count] @UserId, @OrganizationId, @StudentId, @SearchCriteria";

            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, parameters);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<int> results = await _dbContext.Database
                                .SqlQuery<int>(formattedQuery)
                                .ToListAsync();

                int count = results.FirstOrDefault();

                return count;
            }
        }
    }
}
