using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_headstart_map_2015")]
    public class LAPHeadstartMap2015
    {
        [Key]
        [Column("StaticID")]
        public long StaticId { get; set; }

        [Column("RuleID")]
        public int? RuleId { get; set; }

        [Required]
        [Column("HSDomainSequence")]
        public int HSDomainSequence { get; set; }

        [Required]
        [Column("HSDomainName")]
        [StringLength(50)]
        public string HSDomainName { get; set; } = string.Empty;

        [Required]
        [Column("HSSubSequence")]
        public int HSSubSequence { get; set; }

        [Required]
        [Column("HSSubName")]
        [StringLength(50)]
        public string HSSubName { get; set; } = string.Empty;

        [Required]
        [Column("GoalSequence")]
        public int GoalSequence { get; set; }

        [Required]
        [Column("GoalDesc")]
        [StringLength(150)]
        public string GoalDesc { get; set; } = string.Empty;

        [Required]
        [Column("StartAge")]
        public int StartAge { get; set; }

        [Required]
        [Column("EndAge")]
        public int EndAge { get; set; }

        [Required]
        [Column("Instrument")]
        public int Instrument { get; set; }

        [Required]
        [Column("AgeGroupSequence")]
        public int AgeGroupSequence { get; set; }

        [Required]
        [Column("LapDomainSequence")]
        public int LapDomainSequence { get; set; }

        [Required]
        [Column("LapSubscaleSequence")]
        public int LapSubscaleSequence { get; set; }

        [Required]
        [Column("LapItemSequence")]
        public int LapItemSequence { get; set; }
    }
}
