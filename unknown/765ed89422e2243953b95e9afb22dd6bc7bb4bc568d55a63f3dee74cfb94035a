﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class UserEntity3LinkRepository : IUserEntity3LinkRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public UserEntity3LinkRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<UserEntity3Link> AddUserEntity3LinkAsync(UserEntity3Link userEntity3Link)
        {
            if (userEntity3Link is null)
            {
                throw new ArgumentNullException(nameof(userEntity3Link));
            }

            // Get the authentication state 
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            userEntity3Link.ModId = userId;
            userEntity3Link.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.UserEntity3Links.AddAsync(userEntity3Link);
                await _dbContext.SaveChangesAsync();
            }

            return userEntity3Link;
        }

        public async Task<UserEntity3Link?> GetUserEntity3LinkAsync(long? organizationId, string? userId, long? accessId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (userId is null)
            {
                throw new ArgumentNullException(nameof(userId));
            }

            if (accessId is null)
            {
                throw new ArgumentNullException(nameof(accessId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.UserEntity3Links.FirstOrDefaultAsync(o => o.OrganizationId == organizationId && o.UserId == userId && o.Entity3UserAccessId == accessId);
            }
        }

        public async Task<bool> RemoveUserEntity3LinkAsync(long? linkId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                UserEntity3Link? link = await _dbContext.UserEntity3Links.FirstOrDefaultAsync(o => o.Id == linkId);

                if (link == null)
                {
                    return false; // link not found
                }

                _dbContext.UserEntity3Links.Remove(link);
                await _dbContext.SaveChangesAsync();
                return true; // Successfully deleted
            }
        }
    }
}
