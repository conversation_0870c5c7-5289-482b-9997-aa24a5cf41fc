﻿@using Compass.Common.DTOs.Generic
@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Services
@using Compass.Common.Pages.Prompts.Generic
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject NavigationManager NavigationManager
@inject ISiteService SiteService;
@inject UserAccessor UserAccessor
@inject CommonSessionDataObserver CommonSessionDataObserver

<div>
    @if (ShowMenu)
    {
        <button type="button" class="c4l-button c4l-danger-button" @onclick="() => OnDeleteSelected()">Delete @(SiteName ?? "this " + SiteHierarchy)?</button>
        <button type="button" class="c4l-button c4l-ghost-secondary" @onclick="() => OnManageSchoolYears()">Manage School Years</button>
    }
    else
    {
        if (CurrentTabComponent is not null)
        {
            <div class="component-content-wrapper organization-manage-component-wrapper">
                <DynamicComponent Type="CurrentTabComponent" Parameters="GetDynamicParameters()" />
            </div>
        }
    }
</div>

<DialogBox 
    Title="Attention"
    Message="@DialogMessage"
    IsVisible="@IsDeleteDialogVisible" 
    DialogResult="OnDeleteDialogResult" 
/>

<MessageBox 
    Title="Error"
    Message="@($"Cannot delete {SiteName} without unassigning all users first.")"
    IsVisible="@IsDisplayMessageVisible"
    IsLocalized=true
    OnClose="OnDisplayMessageResult" 
/>
