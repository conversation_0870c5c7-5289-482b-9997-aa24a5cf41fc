﻿@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]

<div class="dropdown">
    <button class="btn btn-secondary dropdown-toggle" type="button" @onclick="ToggleDropdown" aria-expanded="false">
        @Title
    </button>
    <ul class="dropdown-menu @(isOpen ? "show" : "")">
        @foreach (DropMenuItem item in MenuItemList)
        {
            <li>
                <a class="dropdown-item" @onclick="() => OnItemSelected(item)">
                    @item.Name
                </a>
            </li>
        }
    </ul>
</div>