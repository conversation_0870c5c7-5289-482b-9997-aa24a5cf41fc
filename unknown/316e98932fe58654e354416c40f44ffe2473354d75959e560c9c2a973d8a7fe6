.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 99;
  padding-inline: 1rem;
}

.dialog-box {
  width: min(100% - 2rem, 550px);
  padding: clamp(1.25rem, 0.961rem + 1.233vw, 1.75rem);
  background: var(--white);
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;

  &.assign-modal-wrapper {
    width: min(100%, 800px);
    margin-inline: auto;

    &.organization-assign-modal {
      & .c4l-table-wrapper {
        min-width: 1200px;
      }
    }

    &.entity1-assign-modal,
    &.entity2-assign-modal,
    &.entity3-assign-modal,
    &.sitelist-assign-modal,
    &.grouplist-assign-modal {
      & .c4l-table-wrapper {
        min-width: 1350px;
      }
    }
  }
}

.modal-scroll-wrapper {
  overflow: auto;
}

.dialog-box-heading {
  margin-block-end: 1rem;
}

.dialog-actions {
  flex-direction: column;
  gap: 1rem;
  margin-block-start: 1.25rem;

  & .c4l-button {
    width: 100%;
  }
}

.buttons-wrapper {
  &.assign-modal-buttons-wrapper {
    flex-direction: column;
  }
}

@media (min-width: 48rem) {
  .dialog-actions {
    flex-direction: row;
    justify-content: center;

    & .c4l-button {
      width: fit-content;
    }
  }

  .buttons-wrapper {
    &.assign-modal-buttons-wrapper {
      flex-direction: row;
    }
  }
}

@media (min-width: 64rem) {
  .dialog-box {
    transform: translateX(137.5px);

    &.assign-modal-wrapper {
      transform: translateX(0);
    }
  }
}

@media (min-width: 100rem) {
  .dialog-box {
    &.assign-modal-wrapper {
      transform: translateX(142px);
    }
  }
}
