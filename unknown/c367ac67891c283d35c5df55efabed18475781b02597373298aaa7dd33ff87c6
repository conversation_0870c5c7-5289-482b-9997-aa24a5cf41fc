﻿using Compass.Common.DTOs.Entity2;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Services
{
    public interface IEntity2Service
    {
        public Task<Entity2> CreateEntity2Async(Entity2 entity2);
        public Task<KaplanPageable<Entity2ListDisplayDto>> GetEntity2Page(Entity2ListAction action);
        public Task<DeleteReturnDto> DeleteEntity2(long? organizationId, long? entity1Id, long? entity2Id);
        public Task<UserEntity2Link> AssignEntity2User(CreateUserLinkAction action);
        public Task<bool> UnAssignEntity2User(CreateUserLinkAction action);
        public Task<int> GetEntity2Count(long orgId);

        public Task<List<Entity2>> GetEntities2Async(long? organizationId, long? entity1Id);
    }
}
