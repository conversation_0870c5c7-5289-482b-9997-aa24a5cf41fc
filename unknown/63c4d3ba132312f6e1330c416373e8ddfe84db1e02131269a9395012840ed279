﻿using Compass.Common.Data;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using System.Collections.Generic;

namespace Compass.Components.Account.Pages.Manage
{
    public partial class Index
    {
        private ApplicationUser user = default!;
        private string? userName;
        private string? phoneNumber;
        private string? firstName;
        private string? lastName;
        
        private string? alertType;
        private string? _statusMessageText;

        [CascadingParameter]
        private HttpContext HttpContext { get; set; } = default!;

        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        protected override async Task OnInitializedAsync()
        {
            user = await UserAccessor.GetRequiredUserAsync(HttpContext);
            userName = await UserManager.GetUserNameAsync(user);
            phoneNumber = await UserManager.GetPhoneNumberAsync(user);
            firstName = user.FirstName;
            lastName = user.LastName;

            Input.UserName ??= userName;
            Input.FirstName ??= firstName;
            Input.LastName ??= lastName;
            Input.PhoneNumber ??= phoneNumber;
        }

        protected async Task OnValidSubmitAsync()
        {
            _statusMessageText = null;
            alertType = null;

            bool profileDetailsModified = false;
            bool updateOperationsSucceeded = true;
            List<string> errorMessages = new List<string>();

            if (Input.FirstName != firstName || Input.LastName != lastName)
            {
                user.FirstName = Input.FirstName;
                user.LastName = Input.LastName;
                IdentityResult saveResult = await UserManager.UpdateAsync(user);

                if (!saveResult.Succeeded)
                {
                    updateOperationsSucceeded = false;
                    errorMessages.AddRange(saveResult.Errors.Select(e => e.Description));
                }
                else
                {
                    profileDetailsModified = true;
                }
            }

            if (updateOperationsSucceeded && Input.PhoneNumber != phoneNumber)
            {
                IdentityResult setPhoneResult = await UserManager.SetPhoneNumberAsync(user, Input.PhoneNumber);

                if (!setPhoneResult.Succeeded)
                {
                    updateOperationsSucceeded = false;
                    errorMessages.AddRange(setPhoneResult.Errors.Select(e => e.Description));
                }
                else
                {
                    profileDetailsModified = true;
                }
            }

            if (updateOperationsSucceeded && Input.UserName != userName)
            {
                IdentityResult setUserNameResult = await UserManager.SetUserNameAsync(user, Input.UserName);

                if (!setUserNameResult.Succeeded)
                {
                    updateOperationsSucceeded = false;
                    errorMessages.AddRange(setUserNameResult.Errors.Select(e => e.Description));
                }
                else
                {
                    profileDetailsModified = true;
                }
            }

            if (!updateOperationsSucceeded)
            {
                alertType = "danger";
                _statusMessageText = "Error: Could not update profile. " + string.Join(" ", errorMessages);
            }
            else if (profileDetailsModified)
            {
                await SignInManager.RefreshSignInAsync(user);
                alertType = "success";
                _statusMessageText = "Your profile has been updated successfully.";

                this.firstName = Input.FirstName;
                this.lastName = Input.LastName;
                this.phoneNumber = Input.PhoneNumber;
                this.userName = Input.UserName;
            }
            else 
            {
                alertType = "info";
                _statusMessageText = "No changes were made to your account.";
            }
        }

        public sealed class InputModel
        {
            [Required]
            [Display(Name = "UserName")]
            public string? UserName { get; set; }

            [Display(Name = "Phone number")]
            [CustomValidation(typeof(InputModel), nameof(ValidatePhone))]
            public string? PhoneNumber { get; set; }

            [Display(Name = "FirstName")]
            public string? FirstName { get; set; }

            [Display(Name = "LastName")]
            public string? LastName { get; set; }

            public static ValidationResult? ValidatePhone(string? phone, ValidationContext context)
            {
                if (string.IsNullOrEmpty(phone))
                {
                    return ValidationResult.Success;
                }

                string phonePattern = @"^\+?[1-9][\d-]{1,14}$";
                if (System.Text.RegularExpressions.Regex.IsMatch(phone, phonePattern))
                {
                    return ValidationResult.Success;
                }

                return new ValidationResult("Phone number format should be ************.");
            }
        }
    }
}
