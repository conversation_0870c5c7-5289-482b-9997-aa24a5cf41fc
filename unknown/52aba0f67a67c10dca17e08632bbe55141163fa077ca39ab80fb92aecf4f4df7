﻿using Compass.Common.DTOs.Organization;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IOrganizationRepository
    {
        public Task<Organization?> GetOrganizationAsync(long? organizationId);
        public Task<Organization> AddOrganizationAsync(Organization organization);
        public Task<Organization?> UpdateOrganizationAsync(long? id, Organization organization);
        Task<List<OrganizationListDisplayDto>> GetOrganizations(PageQuery pageQuery);
        Task<int> GetOrganizationCount(string queryText);

    }
}
