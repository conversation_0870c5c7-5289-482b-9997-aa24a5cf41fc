using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Data;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.C4L.Pages
{
    public partial class C4L_NonContactDayList
    {
        [Inject]
        public required IC4LNonContactDayService NonContactDayService { get; set; }

        [Inject]
        public required IC4LClassroomService C4LClassroomService { get; set; }

        private long? c4l_classroomId { get; set; }

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private IEnumerable<C4LNonContactDay> nonContactDays = new List<C4LNonContactDay>();

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            (ApplicationUser? user, string? userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                if (commonSessionData.CurrentStudentGroupId.HasValue)
                {
                    long studentGroupId = commonSessionData.CurrentStudentGroupId.Value;
                    C4LClassroom? c4lc = await C4LClassroomService.GetByStudentGroupIdAsync(studentGroupId);
                    if (c4lc is not null)
                    {
                        this.c4l_classroomId = c4lc.Id;
                    }
                }
            }
            if (this.c4l_classroomId.HasValue)
            {
                await LoadNonContactDays();
            }
        }

        private async Task LoadNonContactDays()
        {
            try
            {
                if (this.c4l_classroomId.HasValue)
                {
                    nonContactDays = await NonContactDayService.GetNonContactDaysAsync(this.c4l_classroomId.Value);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading non-contact days: {ex.Message}");
            }
        }
    }
}