﻿using Compass.Common.Data;
using Compass.Common.DTOs.Entity1;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Entity1
{
    public partial class Entity1List : IDisposable
    {
        private List<Entity1ListDisplayDto> entity1Results = new();
        private string entity1Hierarchy = "";

        private int maxPages;
        private int currentPage;

        private long? currentOrganizationId;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        private bool noSearchResults = false;
        private bool isLoading = true;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private List<string>? _currentUserRoles;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null && _currentUserId != null)
            {
                _currentUserRoles = await UserAccessor.GetUserRolesAsync();

                bool isCurrentUserSuperAdmin = false;
                if (_currentUserRoles != null)
                {
                    isCurrentUserSuperAdmin = _currentUserRoles.Contains(UserAccessor.USER_ROLE_SUPER_ADMIN);
                }

                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
                if (commonSessionData != null)
                {
                    currentOrganizationId = commonSessionData.CurrentOrganizationId;
                }
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            searchText = string.Empty;
            currentSearchText = string.Empty;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                entity1Hierarchy = commonSessionData.Entity1Hierarchy;

                currentPage = 1;
                maxPages = 0;
                await GetEntity1Page();
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task GetEntity1Page()
        {
            isLoading = true;
            Entity1ListAction action = new();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            action.pageQuery = pageQuery;
            action.userRoles = _currentUserRoles;

            action.organizationId = currentOrganizationId;
            action.userId = _currentUserId;

            KaplanPageable<Entity1ListDisplayDto> currentPage = await Entity1Service.GetEntity1Page(action);

            entity1Results = currentPage.PageContent;
            maxPages = currentPage.MaxPages;
            noSearchResults = !string.IsNullOrEmpty(currentSearchText) && entity1Results.Count == 0;
            isLoading = false;

            StateHasChanged();
        }

        protected async Task OnEntity1Selected(Entity1ListDisplayDto entity)
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                commonSessionData.CurrentEntity1Id = entity.Id;

                if (entity.Name != null)
                {
                    commonSessionData.SelectedEntityName = entity.Name;
                }
                else
                {
                    commonSessionData.SelectedEntityName = string.Empty;
                }

                if (_currentUser != null)
                {
                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();

                    NavigationManager.NavigateTo($"/entity1");
                }
            }
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetEntity1Page();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetEntity1Page();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetEntity1Page();
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
