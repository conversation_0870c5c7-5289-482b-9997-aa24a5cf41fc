:root {
  --button-border-radius: 0.25rem;
  --button-border-radius: 0.25rem;
}

a,
.btn-link {
  color: var(--c4l-primary-purple);
}

.btn-primary {
  color: var(--white);
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.btn:focus,
.btn:active:focus,
.btn-link.nav-link:focus,
.form-control:focus,
.form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

.buttons-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;

  &.centered-buttons {
    justify-content: center;
  }
}

.c4l-button {
  font-weight: 500;
  padding: 0.5rem 0.75rem 0.25rem;
  border-radius: var(--button-border-radius);
  border: 1px solid transparent;
  transition: 
    background-color var(--transition-speed) ease,
    color var(--transition-speed) ease,
    border var(--transition-speed) ease;
  min-height: 2.375rem;
  text-decoration: none;
  display: inline-block;

  &.c4l-primary-button {
    background-color: var(--c4l-primary-purple);
    color: var(--white);

    &:active,
    &:hover,
    &:focus {
      background-color: var(--white);
      color: var(--c4l-primary-purple);
      border: 1px solid var(--c4l-primary-purple);
    }
  }

  &.c4l-secondary-button {
    background-color: var(--c4l-secondary-teal);
    color: var(--white);
    border: 1px solid transparent;
  
    &:active,
    &:hover,
    &:focus {
      background-color: var(--white);
      color: var(--c4l-secondary-teal);
      border: 1px solid var(--c4l-secondary-teal);
    }
  }

  &.c4l-tertiary-button {
    background-color: hsl(32 100% 92.4%);
    color: hsl(31.9, 100%, 18.4%);

    &:active,
    &:hover,
    &:focus {
      background-color: hsl(32 100% 92.4%);
      color: hsl(31.9, 100%, 18.4%);
    }
  }

  &.c4l-danger-button {
    background-color: var(--c4l-danger);
    color: var(--white);

    &:active,
    &:hover,
    &:focus {
      background-color: hsl(350.1, 100%, 40%);
    }
  }

  &.c4l-ghost-primary {
    border: 1px solid var(--c4l-primary-purple);
    color: var(--c4l-primary-purple);
    background-color: var(--white);

    &:hover {
      color: var(--white);
      background-color: var(--c4l-primary-purple);
    }
  }

  &.c4l-ghost-secondary {
    border: 1px solid  var(--c4l-secondary-teal);
    color: var(--c4l-secondary-teal);
    background-color: var(--white);

    &:hover {
      color: var(--white);
      background-color: var(--c4l-secondary-teal);
    }
  }

  &.c4l-form-button {
    width: 100%;
    padding-block: 0.65rem 0.35rem;
  }
}

.nav-tabs-wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
  list-style: none;
  padding-inline-start: 0;
  margin-block: 2rem 5rem;
}

.c4l-tab {
  font-weight: 500;
  border-radius: var(--button-border-radius);
  border: 1px solid transparent;
  text-decoration: none;
  padding: 0.5rem 0.75rem 0.25rem;
  transition:
    background-color var(--transition-speed) ease,
    color var(--transition-speed) ease,
    border var(--transition-speed) ease;
  display: block;
  min-height: 2.5rem;

  &.c4l-primary-tab {
    color: var(--c4l-primary-purple);
    background-color: var(--white);
    border: 1px solid var(--c4l-primary-purple);

    &.active {
      background: var(--c4l-primary-purple);
      color: var(--white);
    }
  
    &:hover {
      background: var(--c4l-primary-purple);
      color: var(--white);
    }
  }
}

.c4l-pagination-buttons-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  & .c4l-button {
    width: 100%;
  }
}

.c4l-pagination-button {
  &[disabled] {
    pointer-events: none;
    background-color: hsl(0 0% 92.2%);
    color: hsl(278.4, 98.4%, 20%);
    border-color: hsl(278 37.3% 87.5%);

    &:active,
    &:hover,
    &:focus {
      background-color: hsl(0 0% 92.2%);
      color: hsl(278.4, 98.4%, 24.9%);
      border-color: hsl(0 0% 92.2%);
    }
  }
}

@media (min-width: 48rem) {
  .c4l-pagination-buttons-wrapper {
    flex-direction: row;
    gap: 0;
    justify-content: space-between;
  
    & .c4l-button {
      width: fit-content;
    }
  }
}
