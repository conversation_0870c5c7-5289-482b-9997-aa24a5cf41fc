﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Student;
using Compass.Common.Helpers;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Admin.Student
{
    public partial class GeneralStudentList : IDisposable
    {
        [Inject]
        public required CommonSessionDataObserver CommonSessionDataObserver { get; set; }

        private List<StudentDisplayDto> studentResults = new();

        private int maxPages;
        private int currentPage;

        private bool isLoading = true;
        private bool noSearchResults = false;

        private long? currentOrganizationId;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;
        private string? organizationName = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            searchText = string.Empty;
            currentSearchText = string.Empty;
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                organizationName = commonSessionData.CurrentOrganizationName;
                currentPage = 1;
                maxPages = 0;
                await GetStudentPage();
            }
        }

        private async Task GetStudentPage()
        {
            StudentListAction action = new StudentListAction();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            action.PageQuery = pageQuery;

            if (currentOrganizationId != null)
            {
                action.UserId = _currentUserId;
                action.OrganizationId = currentOrganizationId;

                KaplanPageable<StudentDisplayDto> currentPage = await StudentService.GetStudentPage(action);

                studentResults = currentPage.PageContent;
                maxPages = currentPage.MaxPages;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && studentResults.Count == 0;
                StateHasChanged();
            }
            isLoading = false;
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetStudentPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetStudentPage();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetStudentPage();
        }

        protected async Task OnStudentClick(StudentDisplayDto student)
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData != null)
            {
                commonSessionData.CurrentStudentId = student.Id;

                string studentName = student.FirstName + " " + student.LastName;
                commonSessionData.SelectedEntityName = studentName;

                if (_currentUser != null)
                {
                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();

                    NavigationManager.NavigateTo($"/student");
                }
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);
            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
