using Compass.C4L.Interfaces.Services;
using Compass.C4L.Pages.Student;
using Compass.Common.Data;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using static Compass.Common.Controls.Generic.DropMenu;

namespace Compass.Common.Pages.Admin.Student
{
    public partial class StudentTabs
    {
        [Inject]
        public required IC4LClassroomService C4LClassroomService { get; set; }

        private static readonly int SUMMARY_INDEX = 1;
        private static readonly int EDIT_INDEX = 2;
        private static readonly int GAMES_INDEX = 3;

        private int currentTab = SUMMARY_INDEX;
        private Type? currentTabComponent;
        private string currentStudentName = string.Empty;

        List<DropMenuItem> C4LMenuItemList = new();

        private long? studentId;
        private long? organizationId;
        private bool hasC4LAccess = false;

        private readonly Dictionary<int, Type> tabComponents = new()
        {
            { SUMMARY_INDEX, typeof(StudentSummaryComponent) },
            { EDIT_INDEX, typeof(StudentEditComponent) },
            { GAMES_INDEX, typeof(GameCenterComponent) }
        };

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            //This needs to be replaced with logic to look for c4l licenses
            CommonSessionDataObserver.AddStateChangeAsyncListeners(UpdateStudentName);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                currentStudentName = commonSessionData.SelectedEntityName;
                currentTab = SUMMARY_INDEX;
                // Initialize with the first tab's component
                currentTabComponent = tabComponents[currentTab];

                this.studentId = commonSessionData.CurrentStudentId;
                this.organizationId = commonSessionData.CurrentOrganizationId;

                hasC4LAccess = await C4LClassroomService.GetStudentC4LAccessAsync(this.organizationId, this.studentId);

                FillC4LMenuItems();
            }
        }

        private void FillC4LMenuItems()
        {
            if (hasC4LAccess)
            {
                string c4lGameValue = GAMES_INDEX.ToString();

                C4LMenuItemList = new()
                {
                    new DropMenuItem { Name="C4L Games", Value=c4lGameValue }
                };
            }
            else
            {
                C4LMenuItemList = new();
            }
        }

        private async Task UpdateStudentName()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.currentStudentName = commonSessionData.SelectedEntityName;
                StateHasChanged();
            }
        }

        protected void OnC4LSelectionResult(string result)
        {
            int value = int.Parse(result);
            ChangeTab(value);
        }

        protected void ChangeTab(int tabIndex)
        {
            currentTab = tabIndex;
            currentTabComponent = tabComponents[currentTab];
        }

        public void Dispose()
        {
            CommonSessionDataObserver.RemoveStateChangeAsyncListeners(UpdateStudentName);
        }
    }
}
