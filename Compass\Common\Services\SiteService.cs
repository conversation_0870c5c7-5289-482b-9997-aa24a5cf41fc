﻿using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Site;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Compass.Common.Models;
using Compass.Common.Resources;

namespace Compass.Common.Services
{
    public class SiteService : ISiteService
    {
        private readonly ISiteRepository _siteRepository;
        private readonly ISchoolYearRepository _schoolYearRepository;
        private readonly IStudentGroupRepository _studentGroupRepository;
        private readonly IUserSiteAccessRepository _userSiteAccessRepository;
        private readonly IUserSiteLinkRepository _userSiteLinkRepository;
        private readonly IUserRepository _userRepository;

        public SiteService(ISiteRepository siteRepository,
                            ISchoolYearRepository schoolYearRepository,
                            IStudentGroupRepository studentGroupRepository,
                            IUserSiteAccessRepository userSiteAccessRepository,
                            IUserSiteLinkRepository userSiteLinkRepository,
                            IUserRepository userRepository)
        {
            _siteRepository = siteRepository;
            _schoolYearRepository = schoolYearRepository;
            _studentGroupRepository = studentGroupRepository;
            _userSiteAccessRepository = userSiteAccessRepository;
            _userSiteLinkRepository = userSiteLinkRepository;
            _userRepository = userRepository;
        }

        public async Task<Site> CreateSiteAsync(Site site, SchoolYear schoolYear)
        {
            Site createdSite = await _siteRepository.CreateSiteAsync(site);

            UserSiteAccess siteUserAccess = new UserSiteAccess();
            siteUserAccess.OrganizationId = site.OrganizationId;
            siteUserAccess.SiteId = site.Id;
            siteUserAccess.CanAdd = "Y";
            siteUserAccess.CanUpdate = "Y";
            siteUserAccess.CanDelete = "Y";
            siteUserAccess.CanView = "Y";
            siteUserAccess.CanAssign = "Y";

            await _userSiteAccessRepository.AddUserSiteAccessAsync(siteUserAccess);

            schoolYear.SiteId = createdSite.Id;
            schoolYear.Status = "Current";
            await CreateSchoolYearAsync(schoolYear);

            return createdSite;
        }

        public async Task<KaplanPageable<SiteListDisplayDto>> GetSitePage(SiteListAction action)
        {
            List<SiteListDisplayDto> siteList = await _siteRepository.GetSiteList(action);

            long? orgId = action.organizationId;
            long? entity1Id = action.entity1Id;
            long? entity2Id = action.entity2Id;
            long? entity3Id = action.entity3Id;

            PageQuery pageQuery = action.pageQuery;
            int pageSize = pageQuery.PageSize;

            int siteCount = await _siteRepository.GetSiteCount(orgId, entity1Id, entity2Id, entity3Id, pageQuery.QueryText);

            int maxPages = (int)Math.Ceiling((double)siteCount / pageSize);

            KaplanPageable<SiteListDisplayDto> pageable = new KaplanPageable<SiteListDisplayDto>();
            pageable.PageContent = siteList;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        private async Task<bool> ValidateHasUsers(long? organizationId, long? SiteId)
        {
            int userCount = await _userRepository.GetSiteUserCount(organizationId, SiteId, string.Empty);

            if (userCount > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task<DeleteReturnDto> DeleteSite(long? organizationId, long? siteId)
        {
            DeleteReturnDto ret = new();
            ret.Success = true;

            // validate student groups
            int studentGroupCount = await _studentGroupRepository.GetStudentGroupCount(organizationId, siteId, string.Empty);

            if (studentGroupCount > 0)
            {
                ret.Success = false;
                ret.Message = nameof(CommonResource.err_SiteHasActiveClassrooms);
            }

            // validate users
            bool hasUsers = await ValidateHasUsers(organizationId, siteId);
            if (hasUsers)
            {
                ret.Success = false;
                ret.Message = nameof(CommonResource.err_UsersAssigned);
            }

            //If valid then delete
            if (ret.Success)
            {
                bool success = await _siteRepository.DeleteSite(siteId);
                ret.Success = success;
            }

            return ret;
        }

        public async Task<UserSiteLink> AssignSiteUser(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            long? siteId = action.EntityId;

            UserSiteAccess? userSiteAccess = await _userSiteAccessRepository.GetUserSiteAccessAsync(organizationId, siteId);

            if (userSiteAccess is null)
            {
                throw new Exception("No Site Access Found");
            }

            UserSiteLink createdLink = new UserSiteLink();
            createdLink.OrganizationId = organizationId;
            createdLink.SiteUserAccessId = userSiteAccess.Id;
            createdLink.UserId = action.UserId;
            createdLink.LinkStatus = CompassResource.LinkStatus_Active;
            createdLink.UserRole = "TEST"; // TODO need to figure out user roles

            UserSiteLink newLink = await _userSiteLinkRepository.AddUserSiteLinkAsync(createdLink);

            return newLink;
        }

        public async Task<bool> UnAssignSiteUser(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            long? siteId = action.EntityId;
            UserSiteAccess? userSiteAccess = await _userSiteAccessRepository.GetUserSiteAccessAsync(organizationId, siteId);

            if (userSiteAccess is null)
            {
                throw new Exception("No Site Access Found");
            }

            string? userId = action.UserId;
            long? accessId = userSiteAccess.Id;

            UserSiteLink? removeLink = await _userSiteLinkRepository.GetUserSiteLinkAsync(organizationId, userId, accessId);

            if (removeLink is null)
            {
                throw new Exception("No Link Found");
            }

            long? linkId = removeLink.Id;
            bool result = await _userSiteLinkRepository.RemoveUserSiteLinkAsync(linkId);

            return result;
        }

        public async Task<SchoolYear?> CreateSchoolYearAsync(SchoolYear schoolYear)
        {
            int? year = schoolYear.SchoolYearValue;
            long? siteId = schoolYear.SiteId;
            long? organizationId = schoolYear.OrganizationId;

            if (year is null)
            {
                throw new Exception("School Year Value is not set");
            }

            if (siteId is null)
            {
                throw new Exception("Site Id is not set");
            }

            if (organizationId is null)
            {
                throw new Exception("Organization Id is not set");
            }

            SchoolYear? existingSchoolYear = await _schoolYearRepository.GetExistingSchoolYearAsync((int)year, (long)siteId, (long)organizationId);

            SchoolYear? savedSchoolYear;
            if (existingSchoolYear == null)
            {
                savedSchoolYear = await _schoolYearRepository.CreateSchoolYearAsync(schoolYear);
            }
            else
            {
                long id = existingSchoolYear.Id;
                schoolYear.Id = id;
                savedSchoolYear = await _schoolYearRepository.UpdateSchoolYearAsync(id, schoolYear);
            }

            return savedSchoolYear;
        }

        public async Task<KaplanPageable<SchoolYear>> GetSchoolYearPage(SchoolYearListAction action)
        {
            List<SchoolYear> schoolYearList = await _schoolYearRepository.GetSchoolYearList(action);

            long? organizationId = action.OrganizationId;
            long? siteId = action.SiteId;

            int siteCount = await _schoolYearRepository.GetSchoolYearCount(organizationId, siteId);

            PageQuery pageQuery = action.PageQuery;
            int pageSize = pageQuery.PageSize;

            int maxPages = (int)Math.Ceiling((double)siteCount / pageSize);

            KaplanPageable<SchoolYear> pageable = new KaplanPageable<SchoolYear>();
            pageable.PageContent = schoolYearList;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<SchoolYear?> GetCurrentSchoolYear(long? organizationId, long? siteId)
        {
            SchoolYear? currentSchoolYear = await _schoolYearRepository.GetCurrentSchoolYear(organizationId, siteId);
            return currentSchoolYear;
        }

        public async Task SetCurrentSchoolYear(SchoolYear schoolYear)
        {
            long? organizationId = schoolYear.OrganizationId;
            long? siteId = schoolYear.SiteId;
            await _schoolYearRepository.CloseSchoolYears(organizationId, siteId);

            long id = schoolYear.Id;
            schoolYear.Status = "Current";
            await _schoolYearRepository.UpdateSchoolYearAsync(id, schoolYear);
        }

        public async Task<SiteSummaryDto> GetSiteSummary(long? organizationId, long? siteId)
        {
            Site? site = await _siteRepository.GetSiteAsync(siteId);

            if (organizationId == null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (site == null)
            {
                throw new ArgumentNullException(nameof(site));
            }

            SiteSummaryDto ret = new SiteSummaryDto();
            ret.Id = site.Id;
            ret.SiteName = site.Name;
            ret.ContactEmail = site.ContactEmail;
            ret.ContactPhone = site.ContactPhone;

            string firstName = site.ContactFirstName;
            string lastName = site.ContactLastName;
            ret.ContactName = firstName + " " + lastName;

            ret.StudentGroupCount = await _studentGroupRepository.GetStudentGroupCount(organizationId, siteId, string.Empty);
            ret.ActiveStudentCount = await _siteRepository.GetActiveStudentCount(organizationId, siteId);
            //TODO Archive and Pending must be implemented after licenses are implemented
            ret.ArchiveStudentCount = 0;
            ret.PendingStudentCount = 0;

            return ret;
        }
    }
}
