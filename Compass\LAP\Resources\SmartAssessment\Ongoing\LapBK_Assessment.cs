using Compass.LAP.Resources.Instruments;
using Compass.LAP.Resources.SmartAssessment.Criterion;
using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Resources.SmartAssessment.Ongoing
{
    public class LapBK_Assessment : OngoingAssessment
    {
        public const int ITEMS_IN_EF = 10;

        public LapBK_Assessment()
        {
            Instrument = AssessmentLevel.LAP_BK;
        }

        public override int? GetTotalSubscales()
        {
            return 7;
        }

        public override int GetNumberOfItemsForBasal()
        {
            return 3;
        }

        protected override BasalCeilingCalculator CreateBasalCeilingCalculator(AssessmentItem item, int? lastItemSequence)
        {
            if (item.SubscaleSequence == Subscale.LAP_BK_EF_SEQUENCE)
            {
                return new LapBK_EfBasalCeilingCalculator(GetAssessmentItemsBySubscale(item.SubscaleID));
            }

            int basalItemNumber = GetNumberOfItemsForBasal();
            List<AssessmentItem> items = GetAssessmentItemsBySubscale(item.SubscaleID);
            return new BasalCeilingCalculator(basalItemNumber, items, lastItemSequence);
        }
    }
}
