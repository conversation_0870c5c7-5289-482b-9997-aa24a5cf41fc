﻿using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Site;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Services
{
    public interface ISiteService
    {
        public Task<Site> CreateSiteAsync(Site site, SchoolYear schoolYear);
        public Task<KaplanPageable<SiteListDisplayDto>> GetSitePage(SiteListAction action);
        public Task<DeleteReturnDto> DeleteSite(long? organizationId, long? siteId);
        public Task<UserSiteLink> AssignSiteUser(CreateUserLinkAction action);
        public Task<bool> UnAssignSiteUser(CreateUserLinkAction action);
        public Task<SchoolYear?> CreateSchoolYearAsync(SchoolYear schoolYear);
        public Task<KaplanPageable<SchoolYear>> GetSchoolYearPage(SchoolYearListAction action);
        public Task<SchoolYear?> GetCurrentSchoolYear(long? organizationId, long? siteId);
        public Task SetCurrentSchoolYear(SchoolYear schoolYear);
        public Task<SiteSummaryDto> GetSiteSummary(long? organizationId, long? siteId);
    }
}
