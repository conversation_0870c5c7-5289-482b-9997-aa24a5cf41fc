﻿@page "/deca-student-ratings"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Compass.DECA.DTOs

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<h3>DECA Rating Summary</h3>

@if (isLoading)
{
    <div class="d-flex justify-content-center">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
}
else if (studentRatings != null && studentRatings.Any())
{
    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>Student Name</th>
                    <th>Date of Birth</th>
                    <th>Rating Date</th>
                    <th>Rating Type</th>
                    <th>Checkpoint</th>
                    <th>Rater Type</th>
                    <th>Descr</th>
                    <th>IN</th>
                    <th>SC</th>
                    <th>AT</th>
                    <th>TPF</th>
                    <th>BC</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var rating in studentRatings)
                {
                    <tr>
                        <td>@($"{rating.FirstName} {rating.LastName}")</td>
                        <td>@(rating.BirthDate?.ToString("MM/dd/yyyy") ?? "")</td>
                        <td>@(rating.RatingDate?.ToString("MM/dd/yyyy") ?? "")</td>
                        <td>@(rating.RecordForm ?? "")</td>
                        <td>@(rating.RatingPeriod ?? "")</td>
                        <td>@(rating.RaterType ?? "")</td>
                        <td>Raw Score</td>
                        <td>@(rating.InRaw?.ToString() ?? "")</td>
                        <td>@(rating.ScRaw?.ToString() ?? "")</td>
                        <td>@(rating.ArRaw?.ToString() ?? "")</td>
                        <td>@(rating.TpfRaw?.ToString() ?? "")</td>
                        <td>@(rating.BcRaw?.ToString() ?? "")</td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
}
else
{
    <div class="alert alert-info">
        <i class="bi bi-info-circle"></i>
        No student ratings found for this student.
    </div>
}
