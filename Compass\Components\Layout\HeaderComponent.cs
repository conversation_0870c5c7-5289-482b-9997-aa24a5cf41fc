﻿using Compass.Common.Data;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using System.Security.Claims;
using System.Linq;

namespace Compass.Components.Layout
{
    public partial class HeaderComponent
    {
        [Inject]
        private ILogger<HeaderComponent> Logger { get; set; } = default!;

        private string culture = "en-US";
        private string switchLanguageText = "Español";

        private bool isUserLoggedIn = false;
        private bool authResolved = false;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private ClaimsPrincipal? user;
        private string? UserFirstLetterAvatar { get; set; }

        private string? currentEntityName = string.Empty;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CommonSessionDataObserver.AddStateChangeAsyncListeners(entityNamesAsync);
            await entityNamesAsync();
            await base.OnInitializedAsync();

            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            user = authState.User;
            isUserLoggedIn = authState.User.Identity?.IsAuthenticated ?? false;
            authResolved = true;

            if (user?.Identity is not null && user.Identity.IsAuthenticated && !string.IsNullOrEmpty(user.Identity.Name))
            {
                UserFirstLetterAvatar = char.ToUpperInvariant(user.Identity.Name[0]).ToString();
            }
        }

        private async Task entityNamesAsync()
        {
            try
            {
                CommonSessionData? commonSessionData = await GetCommonSessionData();
                if (_currentUser != null && commonSessionData != null)
                {
                    if (this.currentEntityName != commonSessionData.SelectedEntityName)
                    {
                        this.currentEntityName = commonSessionData.SelectedEntityName;
                        await InvokeAsync(StateHasChanged);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }
        }

        protected void SetSpanishCulture()
        {
            if (culture == "es-ES")
            {
                culture = "en-US";
                switchLanguageText = "Español";
            }
            else
            {
                culture = "es-ES";
                switchLanguageText = "English";
            }

            try
            {
                Logger.LogWarning("Changing UI culture to {Culture}", culture);

                CultureService.SetCulture(culture);
                CurrentLanguageObserver.SetCurrentCulture(culture);

                InvokeAsync(StateHasChanged); // Re-render the component with the new culture

                Logger.LogInformation("UI culture set successfully to {Culture}", culture);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Failed to set UI culture to {Culture}", culture);
                Console.WriteLine(ex.Message);
            }
        }
    }
}
