using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.C4L.Models
{
    [Table("c4l_license_student_groups")]
    public class C4LLicenseStudentGroup
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long? OrganizationId { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime? ModTs { get; set; }

        [Column("license_pool_id")]
        public long? LicensePoolId { get; set; }

        [Column("classroom_id")]
        public long? C4L_ClassroomId { get; set; }
    }
}
