﻿using Compass.Common.Data;
using Compass.Common.Models;
using Compass.Common.Resources;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;

namespace Compass.Common.Pages.Admin.Site
{
    public partial class SiteAddComponent : IDisposable
    {
        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        private string siteHierarchy = string.Empty;
        private string successMessage = string.Empty;

        private long? organizationId;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                this.siteHierarchy = commonSessionData.SiteHierarchy;
                this.organizationId = commonSessionData.CurrentOrganizationId;
            }

            Input = new();

            int currentYear = SchoolYearResource.GetDefaultYear();
            Input.SchoolYear = currentYear;

            string description = SchoolYearResource.GetDefaultDescription();
            Input.Description = description;
        }

        private Compass.Common.Models.Site GetSiteInput()
        {
            Compass.Common.Models.Site site = new();

            site.Name = Input.SiteName;
            site.ContactEmail = Input.ContactEmail;
            site.Address1 = Input.Address1;
            site.Address2 = Input.Address2;
            site.City = Input.City;
            site.ZipCode = Input.ZipCode;
            site.State = Input.State;
            site.ContactFirstName = Input.ContactFirstName;
            site.ContactLastName = Input.ContactLastName;
            site.ContactPhone = Input.ContactPhone;
            site.ContactFax = Input.ContactFax;
            site.Fax = Input.Fax;

            return site;
        }

        private SchoolYear GetSchoolYearInput()
        {
            SchoolYear schoolYear = new();
            schoolYear.OrganizationId = this.organizationId;
            schoolYear.SchoolYearValue = Input.SchoolYear;
            schoolYear.Description = Input.Description;
            schoolYear.Status = "Current";

            return schoolYear;
        }

        protected async Task SubmitAsync()
        {
            Compass.Common.Models.Site site = GetSiteInput();
            SchoolYear schoolYear = GetSchoolYearInput();

            successMessage = string.Empty;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                if (commonSessionData.CurrentOrganizationId != null)
                {
                    site.OrganizationId = this.organizationId;
                    site.Entity1Id = commonSessionData.CurrentEntity1Id;
                }

                if (site.Entity1Id is null || site.Entity1Id == 0)
                {
                    List<Compass.Common.Models.Entity1> entityList = await Entity1Service.GetEntities1Async(this.organizationId);
                    site.Entity1Id = entityList.FirstOrDefault()?.Id;
                }

                site.Entity2Id = commonSessionData.CurrentEntity2Id;
                if (site.Entity2Id is null || site.Entity2Id == 0)
                {
                    List<Compass.Common.Models.Entity2> entityList = await Entity2Service.GetEntities2Async(this.organizationId, site.Entity1Id);
                    site.Entity2Id = entityList.FirstOrDefault()?.Id;
                }

                site.Entity3Id = commonSessionData.CurrentEntity3Id;
                if (site.Entity3Id is null || site.Entity3Id == 0)
                {
                    List<Compass.Common.Models.Entity3> entityList = await Entity3Service.GetEntities3Async(this.organizationId, site.Entity2Id);
                    site.Entity3Id = entityList.FirstOrDefault()?.Id;
                }

                site = await SiteService.CreateSiteAsync(site, schoolYear);
                successMessage = "Information saved successfully!";
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }

        public sealed class InputModel
        {
            [Required]
            [Display(Name = "Site Name")]
            public string SiteName { get; set; } = string.Empty;

            [Display(Name = "Address 1")]
            public string Address1 { get; set; } = string.Empty;

            [Display(Name = "Address 2")]
            public string Address2 { get; set; } = string.Empty;

            [Display(Name = "City")]
            public string City { get; set; } = string.Empty;

            [Display(Name = "Zip Code")]
            public string ZipCode { get; set; } = string.Empty;

            [Display(Name = "State")]
            public string State { get; set; } = string.Empty;

            [Required]
            [Display(Name = "Contact First Name")]
            public string ContactFirstName { get; set; } = string.Empty;

            [Required]
            [Display(Name = "Contact Last Name")]
            public string ContactLastName { get; set; } = string.Empty;

            [Required]
            [Display(Name = "Contact Phone")]
            public string ContactPhone { get; set; } = string.Empty;

            [Required]
            [Display(Name = "Contact Email")]
            public string ContactEmail { get; set; } = string.Empty;

            [Display(Name = "Contact Fax")]
            public string ContactFax { get; set; } = string.Empty;

            [Display(Name = "Fax")]
            public string Fax { get; set; } = string.Empty;

            [Required]
            [CustomValidation(typeof(InputModel), nameof(ValidateSchoolYear))]
            public int? SchoolYear { get; set; }

            [Required]
            [CustomValidation(typeof(InputModel), nameof(ValidateDescription))]
            public string? Description { get; set; }

            public static ValidationResult? ValidateSchoolYear(int? schoolYear, ValidationContext context)
            {
                return SchoolYearResource.ValidateSchoolYear(schoolYear, context);
            }

            public static ValidationResult? ValidateDescription(string? description, ValidationContext context)
            {
                return SchoolYearResource.ValidateDescription(description, context);
            }
        }
    }
}
