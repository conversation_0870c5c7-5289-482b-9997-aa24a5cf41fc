﻿@page "/user-details/{selectedUserId}"
@using Compass.Common.DTOs.User
@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Pages.Prompts.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserSessionService UserSessionService
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject NavigationManager NavigationManager
@inject UserAccessor UserAccessor
@inject UserManager<ApplicationUser> UserManager
@inject IUserService UserService

<h1 class="page-title">User Details</h1>

@if (selectedUser is not null)
{
    
    <h2 class="component-heading user-info-heading">User Information</h2>
    <section class="border p-3 rounded mb-4 user-summary-section d-grid">
        @if (!string.IsNullOrWhiteSpace(selectedUser.UserName))
        {
            <!-- @Localizer["lbl_Username"] -->
            <div class="user-details-wrapper d-flex">
                <p class="summary-info font-weight-700 mb-0">Username:</p>
                <p class="summary-info mb-0">@selectedUser.UserName</p>
            </div>
        }

        @if (!string.IsNullOrWhiteSpace(selectedUser.FirstName))
        {
            <div class="user-details-wrapper d-flex">
                <p class="summary-info font-weight-700 mb-0">First Name:</p>
                <p class="summary-info mb-0">@selectedUser.FirstName</p>
            </div>
        }

        @if (!string.IsNullOrWhiteSpace(selectedUser.LastName))
        {
            <div class="user-details-wrapper d-flex">
                <p class="summary-info font-weight-700 mb-0">Last Name:</p>
                <p class="summary-info mb-0">@selectedUser.LastName</p>
            </div>
        }

        @if (!string.IsNullOrWhiteSpace(selectedUser.Email))
        {
            <div class="user-details-wrapper d-flex">
                <p class="summary-info font-weight-700 mb-0">Email:</p>
                <p class="summary-info mb-0">@selectedUser.Email</p>
            </div>
        }

        @if (!string.IsNullOrWhiteSpace(selectedUser.PhoneNumber))
        {
            <div class="user-details-wrapper d-flex">
                <p class="summary-info font-weight-700 mb-0">Phone:</p>
                <p class="summary-info mb-0">@selectedUser.PhoneNumber</p>
            </div>
        }
    </section>
    
    @if (userAssignmentDtoList.Count > 0)
    {
        <div class="c4l-table-scroll-wrapper">
            <div class="c4l-table-wrapper users-wrapper">
                <div class="c4l-table-headings-wrapper organization-heading-wrapper">
                    <h6 class="c4l-table-heading">Entity Type</h6>
                    <h6 class="c4l-table-heading">Entity Name</h6>
                </div>

                @foreach (UserAssignmentDto assignment in userAssignmentDtoList)
                {
                    <div class="c4l-table-result-wrapper user-result-username">
                        <p class="c4l-table-result-item">@assignment.AccessLevel</p>
                        <p class="c4l-table-result-item">@assignment.EntityName</p>
                    </div>
                }
            </div>
        </div>
    }
    else
    {
        <button type="button" class="c4l-button c4l-danger-button" @onclick="() => OnDeleteClick()">Delete User</button>
    }
}

<DialogBox 
    Title="Attention"
    Message="@DialogMessage"
    IsVisible="@IsDeleteDialogVisible" 
    DialogResult="OnDeleteDialogResult" 
/>
