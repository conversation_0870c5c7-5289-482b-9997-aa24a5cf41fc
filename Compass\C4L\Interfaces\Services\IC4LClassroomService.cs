using Compass.C4L.DTOs;
using Compass.C4L.Models;

namespace Compass.C4L.Interfaces.Services
{
    public interface IC4LClassroomService
    {
        Task<C4LClassroom?> GetC4LClassroomByIdAsync(long id);
        Task<C4LClassroom?> GetByStudentGroupIdAsync(long classroomId);
        Task<IEnumerable<C4LClassroom>> GetC4LClassroomByOrganizationIdAsync(long organizationId);
        Task<C4LClassroom?> UpdateC4LClassroomAsync(long? classroomId, C4LClassroom? classroom);
        Task<C4LClassroomCreateResultDto> CreateC4LClassroomAsync(C4LClassroom? classroom);
        Task<C4LClassroomAccessDto> GetCurrentByStudentGroupIdAsync(long? organizationId, long? studentGroupId);
        Task<List<C4LNonContactDay>> GetNonContactDaysByClassroomIdAsync(long classroomId);
        Task<bool> GetStudentC4LAccessAsync(long? organizationId, long? studentId);
        Task<List<C4LClassroomSelectionDisplayDto>> GetStudentC4LClassroomsAsync(long? organizationId, long? studentId);
    }
}