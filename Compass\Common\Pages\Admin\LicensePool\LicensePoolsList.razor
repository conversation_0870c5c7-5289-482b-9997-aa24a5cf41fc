﻿@page "/license-pool-list"
@using Compass.Common.DTOs.LicensePool
@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Compass.Common.Pages.Prompts.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject NavigationManager NavigationManager
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject UserAccessor UserAccessor
@inject ILicensePoolService LicensePoolService

<PageTitle>License Pools | C4L</PageTitle>

<h1 class="page-title horizontal-line">License Pools List</h1>

<div class="c4l-search-table-wrapper">
    <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

    <LoaderComponent IsLoading="isLoading">
        <div class="c4l-table-scroll-wrapper">
            <div class="c4l-table-wrapper has-links license-pool-wrapper">
                <div class="c4l-table-headings-wrapper license-pool-heading-wrapper">
                    <h6 class="c4l-table-heading">Name</h6>
                    <h6 class="c4l-table-heading">Status</h6>
                    <h6 class="c4l-table-heading">Product</h6>
                    <h6 class="c4l-table-heading">PurchasedLicenses</h6>
                </div>

                @foreach (LicensePoolListDisplayDto licensePool in licensePoolResults)
                {
                    <button type="button" title="Select @licensePool.Name" @onclick="() => OnLicensePoolDetailsClick(licensePool.Id)">
                        <div class="c4l-table-result-wrapper license-pool-result-wrapper">
                            <p class="c4l-table-result-item">@licensePool.Name</p>
                            <p class="c4l-table-result-item">@licensePool.Status</p>
                            <p class="c4l-table-result-item">@licensePool.Product</p>
                            <p class="c4l-table-result-item">@licensePool.PurchasedLicenses</p>
                        </div>
                    </button>
                }
            </div>
        </div>

        <div class="c4l-pagination-wrapper">
            <div class="c4l-pagination-buttons-wrapper">
                <div class="buttons-wrapper">
                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnPreviousClicked()"
                        disabled="@(currentPage <= 1)"
                    >
                        Previous
                    </button>

                    <button 
                        class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                        @onclick="() => OnNextClicked()"
                        disabled="@(currentPage >= maxPages)"
                    >
                        Next
                    </button>
                </div>

                <div class="add-license-pool-button-wrapper">
                    <button class="c4l-button c4l-secondary-button add-license-pool-button" type="button" @onclick="() => OnCreateLicensePool()">Add License Pool</button>
                </div>
            </div>

            <div class="page-count-wrapper font-weight-500">
                <span class="current-page-number">@currentPage</span> of @maxPages
            </div>
        </div>
    </LoaderComponent>
</div>
