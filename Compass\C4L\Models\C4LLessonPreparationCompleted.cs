﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.C4L.Models
{
    [Table("c4l_lesson_preparations_completed")]
    public class C4LLessonPreparationCompleted
    {
        [Key]
        [Column("id")]
        public long Id { get; set; }

        [Column("organization_id")]
        public long OrganizationId { get; set; }

        [Column("mod_id")]
        public string? ModId { get; set; }

        [Column("mod_ts")]
        public DateTime ModTs { get; set; }

        [Column("lesson_preparation_id")]
        public long LessonPreparationId { get; set; }

        [Column("schoolyear")]
        public int Schoolyear { get; set; }

        [Column("date_completed")]
        public DateTime? DateCompleted { get; set; }
    }
}
