using Compass.LAP.Resources.SmartAssessment.Primary;

namespace Compass.LAP.Resources.SmartAssessment.Criterion
{
    public class BasalCeilingCalculator
    {
        protected readonly List<AssessmentItem> items;
        private readonly int numberOfItemsForBasal;
        private int basal;
        private int ceiling;
        protected int rawscore;
        private readonly int? lastItemSequence;
        private readonly List<int?> scores = new List<int?>();

        public BasalCeilingCalculator(
            int numberOfItemsForBasal,
            List<AssessmentItem> assessmentItems,
            int? lastItemSequence)
        {
            this.numberOfItemsForBasal = numberOfItemsForBasal;
            items = assessmentItems;
            this.lastItemSequence = lastItemSequence;
        }

        public virtual int? Basal => basal;

        public virtual int? Ceiling => ceiling;

        public virtual int? RawScore => rawscore;

        public virtual void FindBasal()
        {
            int counter = 0;
            int previousSeq = items[0].Sequence - 1 ?? 0;

            foreach (AssessmentItem item in items)
            {
                if (item.Sequence == 1)
                {
                    basal = 1;
                    break;
                }

                // If item is out of order, reset count
                if (item.Sequence - 1 != previousSeq)
                {
                    counter = 0;
                }

                // If mastered, add it.
                if (item.Value == Skill.MASTERED)
                {
                    counter++;
                    if (counter == numberOfItemsForBasal)
                    {
                        basal = (item.Sequence ?? 0) - (numberOfItemsForBasal - 1);
                        break;
                    }
                }
                else
                {
                    counter = 0;
                }
                previousSeq = item.Sequence ?? 0;
            }
        }

        public virtual void FindCeiling()
        {
            ceiling = 0;
            rawscore = 0;
            if (basal == 0)
            {
                FindBasal();
            }
            if (basal == 0)
            {
                return;
            }

            int counter = 0;
            int previousSeq = items[0].Sequence - 1 ?? 0;

            foreach (AssessmentItem item in items)
            {
                // Start at the basal
                if (item.Sequence < basal)
                {
                    previousSeq = item.Sequence ?? 0;
                    continue;
                }

                // If item is not in the correct sequence, start over.
                if (item.Sequence - 1 != previousSeq && previousSeq >= basal)
                {
                    counter = 0;
                    scores.Clear();
                    break;
                }

                if (scores.Count == CriterionAssessment.MAX_ITEMS_IN_CEILING)
                {
                    scores.RemoveAt(0);
                }

                // If it's emerging, keep track so we can use it to figure out rawscore.
                // && the item is scored (its a new/modified item for this assessment)
                if (item.Value == Skill.EMERGING && item.Scored)
                {
                    counter++;
                }
                scores.Add(item.Value);

                previousSeq = item.Sequence ?? 0;

                // Tally up the scores and figure out the ceiling using the algorithm.
                int tally = 0;
                foreach (int? score in scores)
                {
                    tally += score ?? 0;
                }

                // Replaced old log. Older version did not always take basal into account as it should have
                if (basal != 0 && (item.Sequence == lastItemSequence ||
                    scores.Count == CriterionAssessment.MAX_ITEMS_IN_CEILING && tally == -1 ||
                    scores.Count == CriterionAssessment.MIN_ITEMS_IN_CEILING && tally == -3 ||
                    scores.Count == CriterionAssessment.MAX_ITEMS_IN_CEILING - 1 && tally == -2))
                {
                    ceiling = item.Sequence ?? 0;
                    rawscore = ceiling - counter;
                    break;
                }
            }
        }

        public virtual bool IsBasalFound()
        {
            return basal != 0;
        }

        public virtual bool IsCeilingFound()
        {
            return ceiling != 0;
        }

        public virtual bool IsRawScoreFound()
        {
            return rawscore != 0;
        }

        public virtual void FindRawScore()
        {
            rawscore = 0;
            foreach (AssessmentItem item in items)
            {
                if (item.Value == Skill.MASTERED)
                {
                    rawscore++;
                }
            }
        }
    }
}
