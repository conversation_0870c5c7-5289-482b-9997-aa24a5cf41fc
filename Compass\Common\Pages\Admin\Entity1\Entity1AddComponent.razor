﻿@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject IEntity1Service Entity1Service
@inject UserAccessor UserAccessor
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

@attribute [Authorize]

@if (entity1 is not null)
{
    <EditForm Model="entity1" FormName="formEntity1" OnValidSubmit="SubmitAsync" class="c4l-form entity1-add-form">
        <h3 class="c4l-form-heading">@Localizer["lbl_Add"] @entity1Name</h3>

        <DataAnnotationsValidator></DataAnnotationsValidator>
        <ValidationSummary></ValidationSummary>

        <div>
            <label class="col-form-label" for="entity1-name">
                @Localizer["lbl_Name"]
                <InputText 
                    @bind-Value="entity1.Name" 
                    id="entity1-name" 
                    class="form-control mt-2"
                    aria-required="true"
                />
            </label>
            <ValidationMessage For="() => entity1.Name" class="mt-2 text-danger" />
        </div>

        <div>
            <label class="col-form-label" for="entity1-address1">
                @Localizer["lbl_Address1"]
                <InputText 
                    @bind-Value="entity1.Address1" 
                    class="form-control" 
                    id="entity1-address1"
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity1-address2">
                @Localizer["lbl_Address2"]
                <InputText 
                    @bind-Value="entity1.Address2" 
                    class="form-control" 
                    id="entity1-address2"
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity1-city">
                @Localizer["lbl_City"]
                <InputText 
                    @bind-Value="entity1.City" 
                    class="form-control" 
                    id="entity1-city"
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity1-state">
                @Localizer["lbl_State"]
                <InputSelect 
                    @bind-Value="entity1.State"
                    id="entity1-state" 
                >
                    <option disabled value="">-- @Localizer["lbl_State"] --</option>

                    @foreach (var stateCode in CompassResource.UsStates)
                    {
                        <option value="@stateCode">@stateCode</option>
                    }
                </InputSelect>
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity1-zipcode">
                @Localizer["lbl_ZipCode"]
                <InputText 
                    @bind-Value="entity1.ZipCode" 
                    class="form-control" 
                    id="entity1-zipcode"
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity1-firstname">
                @Localizer["lbl_ContactFirstName"]
                <InputText 
                    @bind-Value="entity1.ContactFirstName" 
                    class="form-control" 
                    id="entity1-firstname"
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity1-lastname">
                @Localizer["lbl_ContactLastName"]
                <InputText 
                    @bind-Value="entity1.ContactLastName" 
                    class="form-control" 
                    id="entity1-lastname"
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity1-contact-email">
                @Localizer["lbl_ContactEmail"]
                <InputText 
                    @bind-Value="entity1.ContactEmail" 
                    class="form-control" 
                    id="entity1-contact-email"
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity1-contact-phone">
                @Localizer["lbl_ContactPhone"]
                <InputText 
                    @bind-Value="entity1.ContactPhone" 
                    class="form-control" 
                    id="entity1-contact-phone"
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity1-contact-fax">
                @Localizer["lbl_ContactFax"]
                <InputText 
                    @bind-Value="entity1.ContactFax" 
                    class="form-control" 
                    id="entity1-contact-fax"
                />
            </label>
        </div>

        <div>
            <label class="col-form-label" for="entity1-fax">
                @Localizer["lbl_Fax"]
                <InputText 
                    @bind-Value="entity1.Fax" 
                    class="form-control" 
                    id="entity1-fax"
                />
            </label>
        </div>

        <div class="form-submit-buttons-wrapper">
            <button class="c4l-button c4l-secondary-button c4l-form-button" type="submit">@Localizer["lbl_Save"]</button>
        </div>

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success mt-4" role="alert">
                <p class="text-center font-weight-600 mb-0">@successMessage</p>
            </div>
        }
    </EditForm>
}
