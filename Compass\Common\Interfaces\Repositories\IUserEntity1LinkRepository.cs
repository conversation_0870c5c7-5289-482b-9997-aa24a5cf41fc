﻿using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IUserEntity1LinkRepository
    {
        public Task<UserEntity1Link> AddUserEntity1LinkAsync(UserEntity1Link userEntity1Link);
        public Task<UserEntity1Link?> GetUserEntity1LinkAsync(long? organizationId, string? userId, long? accessId);
        public Task<bool> RemoveUserEntity1LinkAsync(long? linkId);
    }
}
