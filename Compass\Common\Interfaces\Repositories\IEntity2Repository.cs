﻿using Compass.Common.DTOs.Entity2;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IEntity2Repository
    {
        Task<List<Entity2>> GetEntities2Async(long? organizationId, long? entityId);
        Task<Entity2> CreateEntity2Async(Entity2 entity);
        Task<Entity2?> GetEntity2Async(long? id);
        Task<Entity2?> UpdateEntity2Async(long? id, Entity2 entity);
        Task<List<Entity2ListDisplayDto>> GetEntity2List(Entity2ListAction action);
        Task<int> GetEntity2Count(long? organizationId, long? entity1Id, string queryText);
        Task<bool> DeleteEntity2(long? id);
    }
}
