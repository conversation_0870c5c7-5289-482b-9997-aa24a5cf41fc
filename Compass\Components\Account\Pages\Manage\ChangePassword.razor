﻿@page "/account/change-password"

@using System.ComponentModel.DataAnnotations
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity

@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager
@inject ILogger<ChangePassword> Logger

<PageTitle>Change Password | C4L</PageTitle>

<StatusMessage Message="@message" AlertType="@alertType" />

<EditForm Model="Input" FormName="change-password" OnValidSubmit="OnValidSubmitAsync" method="post" class="c4l-form">
    <h2 class="text-center c4l-form-heading">Change Password</h2>

    <DataAnnotationsValidator />
    <ValidationSummary class="text-danger" role="alert" />

    <div class="form-floating mb-3">
        <InputText type="password" @bind-Value="Input.OldPassword" class="form-control" id="old-password" autocomplete="current-password" placeholder="Please enter your old password." />
        <label for="old-password" class="form-label">Old password</label>
        <ValidationMessage For="() => Input.OldPassword" class="text-danger" />
    </div>

    <div class="form-floating mb-3">
        <InputText type="password" @bind-Value="Input.NewPassword" class="form-control" id="new-password" autocomplete="new-password" placeholder="Please enter your new password." />
        <label for="new-password" class="form-label">New password</label>
        <ValidationMessage For="() => Input.NewPassword" class="text-danger" />
    </div>

    <div class="form-floating mb-3">
        <InputText type="password" @bind-Value="Input.ConfirmPassword" class="form-control" id="confirm-password" autocomplete="new-password" placeholder="Please confirm your new password." />
        <label for="confirm-password" class="form-label">Confirm password</label>
        <ValidationMessage For="() => Input.ConfirmPassword" class="text-danger" />
    </div>
    
    <button type="submit" class="c4l-button c4l-form-button c4l-primary-button">Update password</button>
</EditForm>

@code {
    private string? message;
    private string? alertType = "danger";
    private ApplicationUser user = default!;
    private bool hasPassword;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);
        hasPassword = await UserManager.HasPasswordAsync(user);
        if (!hasPassword)
        {
            RedirectManager.RedirectTo("account/set-password");
        }
    }

    private async Task OnValidSubmitAsync()
    {
        var changePasswordResult = await UserManager.ChangePasswordAsync(user, Input.OldPassword, Input.NewPassword);
        if (!changePasswordResult.Succeeded)
        {
            message = $"Error: {string.Join(",", changePasswordResult.Errors.Select(error => error.Description))}";
            return;
        }

        await SignInManager.RefreshSignInAsync(user);
        Logger.LogInformation("User changed their password successfully.");

        RedirectManager.RedirectToCurrentPageWithStatus("Your password has been changed", HttpContext);
    }

    private sealed class InputModel
    {
        [Required(ErrorMessage = "Current password is required.")]
        [DataType(DataType.Password)]
        [Display(Name = "Current password")]
        public string OldPassword { get; set; } = "";

        [Required(ErrorMessage = "New password is required.")]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "New password")]
        public string NewPassword { get; set; } = "";

        [Required(ErrorMessage = "Confirm new password is required.")]
        [DataType(DataType.Password)]
        [Display(Name = "Confirm new password")]
        [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; } = "";
    }
}
