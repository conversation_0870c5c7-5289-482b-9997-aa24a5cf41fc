﻿@page "/account/manage-2fa"

@using Compass.Common.Data
@using Microsoft.AspNetCore.Http.Features
@using Microsoft.AspNetCore.Identity

@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager

<PageTitle>Two-Factor Authentication (2FA) | C4L</PageTitle>
<StatusMessage AlertType="@alertType" />

<h2 class="text-center mb-3">Manage two-factor authentication (2FA)</h2>

@if (canTrack)
{
    if (is2faEnabled)
    {
        if (recoveryCodesLeft == 0)
        {
            <div class="alert alert-danger">
                <strong>You have no recovery codes left.</strong>
                <p class="mb-0">You must <a class="font-weight-500" style="color:var(--c4l-alert-danger);" href="account/generate-recovery-codes">generate a new set of recovery codes</a> before you can log in with a recovery code.</p>
            </div>
        }
        else if (recoveryCodesLeft == 1)
        {
            <div class="alert alert-danger">
                <strong>You have 1 recovery code left.</strong>
                <p class="mb-0">You can <a class="font-weight-500" style="color:var(--c4l-alert-danger);" href="account/generate-recovery-codes">generate a new set of recovery codes</a>.</p>
            </div>
        }
        else if (recoveryCodesLeft <= 3)
        {
            <div class="alert alert-warning">
                <strong>You have @recoveryCodesLeft recovery codes left.</strong>
                <p class="mb-0">You should <a class="font-weight-500" style="color:var(--c4l-alert-danger);" href="account/generate-recovery-codes">generate a new set of recovery codes</a>.</p>
            </div>
        }

        if (isMachineRemembered)
        {
            <form @formname="forget-browser" @onsubmit="OnSubmitForgetBrowserAsync" method="post" class="c4l-form">
                <AntiforgeryToken />
                <h3 class="h4 c4l-form-heading mb-3">Clear 2FA settings from this browser</h3>
                <p>Clicking this button will remove the stored 2FA settings for this browser only. The next time you login, you will need to enter a 2FA code.</p>
                <button type="submit" class="c4l-button c4l-tertiary-button">Forget this browser</button>
            </form>
        }

    <div class="buttons-wraper centered-buttons">
        <a href="account/disable-2fa" class="c4l-button c4l-danger-button">Disable 2FA</a>
        <a href="account/generate-recovery-codes" class="c4l-button c4l-secondary-button">Reset recovery codes</a>
    </div>
    }

    <div class="buttons-wrapper centered-buttons mt-4">
        @if (!hasAuthenticator)
        {
            <a href="Account/Manage/EnableAuthenticator" class="c4l-button c4l-primary-button">Add authenticator app</a>
        }
        else
        {
            <a href="Account/Manage/EnableAuthenticator" class="c4l-button c4l-primary-button">Set up authenticator app</a>
            <a href="account/reset-authenticator-key" class="c4l-button c4l-secondary-button">Reset authenticator app</a>
        }
    </div>
}
else
{
    <div class="alert alert-danger">
        <strong>Privacy and cookie policy have not been accepted.</strong>
        <p>You must accept the policy before you can enable two factor authentication.</p>
    </div>
}

@code {
    private bool canTrack;
    private bool hasAuthenticator;
    private int recoveryCodesLeft;
    private bool is2faEnabled;
    private bool isMachineRemembered;
    private string? alertType = "info";

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        var user = await UserAccessor.GetRequiredUserAsync(HttpContext);
        canTrack = HttpContext.Features.Get<ITrackingConsentFeature>()?.CanTrack ?? true;
        hasAuthenticator = await UserManager.GetAuthenticatorKeyAsync(user) is not null;
        is2faEnabled = await UserManager.GetTwoFactorEnabledAsync(user);
        isMachineRemembered = await SignInManager.IsTwoFactorClientRememberedAsync(user);
        recoveryCodesLeft = await UserManager.CountRecoveryCodesAsync(user);
    }

    private async Task OnSubmitForgetBrowserAsync()
    {
        await SignInManager.ForgetTwoFactorClientAsync();

        RedirectManager.RedirectToCurrentPageWithStatus(
            "The current browser has been forgotten. When you login again from this browser you will be prompted for your 2fa code.",
            HttpContext);
    }
}
