﻿using Compass.Common.Data;
using Compass.Common.DTOs.Entity1;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using LanguageExt;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Runtime.CompilerServices;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class Entity1Repository : IEntity1Repository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public Entity1Repository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<List<Entity1>> GetEntities1Async(long? organizationId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Entities1.Where(o => o.OrganizationId == organizationId).ToListAsync();
            }
        }

        async Task<Entity1> IEntity1Repository.CreateEntity1Async(Entity1 entity1)
        {
            // Get the authentication state
            var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            var user = authState.User;
            var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            entity1.ModId = userId;
            entity1.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.Entities1.AddAsync(entity1);
                await _dbContext.SaveChangesAsync();
            }

            return entity1;
        }

        async Task<Entity1?> IEntity1Repository.GetEntity1Async(long? id)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Entities1.FirstOrDefaultAsync(o => o.Id == id);
            }
        }

        async Task<Entity1?> IEntity1Repository.UpdateEntity1Async(long? id, Entity1 entity1)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                var existingEntity1Hierarchy = await _dbContext.Entities1.FindAsync(id);

                if (existingEntity1Hierarchy is null)
                {
                    return null;
                }

                // Get the authentication state
                var authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                var user = authState.User;
                var userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                existingEntity1Hierarchy.ModId = userId;
                existingEntity1Hierarchy.ModTs = DateTime.Now;

                existingEntity1Hierarchy.Name = entity1.Name;
                existingEntity1Hierarchy.Address1 = entity1.Address1;
                existingEntity1Hierarchy.Address2 = entity1.Address2;
                existingEntity1Hierarchy.City = entity1.City;
                existingEntity1Hierarchy.State = entity1.State;
                existingEntity1Hierarchy.ZipCode = entity1.ZipCode;
                existingEntity1Hierarchy.Country = entity1.Country;

                existingEntity1Hierarchy.ContactEmail = entity1.ContactEmail;
                existingEntity1Hierarchy.ContactFirstName = entity1.ContactFirstName;
                existingEntity1Hierarchy.ContactLastName = entity1.ContactLastName;
                existingEntity1Hierarchy.ContactFax = entity1.ContactFax;
                existingEntity1Hierarchy.ContactPhone = entity1.ContactPhone;
                existingEntity1Hierarchy.Fax = entity1.Fax;

                _dbContext.Entities1.Update(existingEntity1Hierarchy);
                await _dbContext.SaveChangesAsync();

                return existingEntity1Hierarchy;
            }
        }

        public async Task<KaplanPageable<Entity1ListDisplayDto>> GetEntity1Page(Entity1ListAction action)
        {
            List<Entity1ListDisplayDto> entity1List = await GetEntity1List(action);

            PageQuery pageQuery = action.pageQuery;
            int pageSize = pageQuery.PageSize;

            int entity1Count = await GetEntity1Count(action.organizationId, pageQuery.QueryText);

            int maxPages = (int)Math.Ceiling((double)entity1Count / pageSize);

            KaplanPageable<Entity1ListDisplayDto> pageable = new KaplanPageable<Entity1ListDisplayDto>();
            pageable.PageContent = entity1List;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<List<Entity1ListDisplayDto>> GetEntity1List(Entity1ListAction action)
        {
            var results = new List<Entity1ListDisplayDto>();
            PageQuery pageQuery = action.pageQuery;
            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", action.userId),
                new SqlParameter("@OrganizationId", action.organizationId),
                new SqlParameter("@SearchCriteria", pageQuery.QueryText),
                new SqlParameter("@PageOffset", pageQuery.GetOffset()),
                new SqlParameter("@PageSize", pageQuery.PageSize)
            };

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                // Use ADO.NET directly for more control
                using var command = _dbContext.Database.GetDbConnection().CreateCommand();
                command.CommandText = "cmn_accessible_entity1_get_list";
                command.CommandType = CommandType.StoredProcedure;

                foreach (var param in parameters)
                {
                    command.Parameters.Add(param);
                }

                // Ensure connection is open
                if (command.Connection.State != ConnectionState.Open)
                {
                    await command.Connection.OpenAsync();
                }

                try
                {
                    using var reader = await command.ExecuteReaderAsync();

                    // Check if we have any rows
                    if (!reader.HasRows)
                    {
                        return results; // Return empty list
                    }

                    while (await reader.ReadAsync())
                    {
                        try
                        {
                            var entity1 = new Entity1ListDisplayDto
                            {
                                // Use GetOrdinal to find column positions and safely get values
                                Id = reader.GetInt64(reader.GetOrdinal("id")),
                                Name = reader.IsDBNull(reader.GetOrdinal("name"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("name")),
                                ContactName = reader.IsDBNull(reader.GetOrdinal("ContactName"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactName")),
                                ContactEmail = reader.IsDBNull(reader.GetOrdinal("ContactEmail"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactEmail")),
                                ContactPhone = reader.IsDBNull(reader.GetOrdinal("ContactPhone"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("ContactPhone"))
                            };

                            results.Add(entity1);
                        }
                        catch (Exception ex)
                        {
                            // Log the error but continue processing other rows
                            System.Diagnostics.Debug.WriteLine($"Error processing row: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error executing stored procedure: {ex.Message}");
                    throw; // Rethrow the exception after logging
                }
            }

            return results;
        }
        public async Task<int> GetEntity1Count(long? organizationId, string queryText)
        {
            if (organizationId is null)
            {
                organizationId = -1;
            }

            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@UserId", userId),
                new SqlParameter("@OrganizationId", organizationId),
                new SqlParameter("@SearchCriteria", queryText)
            };

            string sqlQuery = "EXEC cmn_accessible_entity1_get_count @UserId, @OrganizationId, @SearchCriteria";

            FormattableString formattedQuery = FormattableStringFactory.Create(sqlQuery, parameters);

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                List<int> results = await _dbContext.Database
                                .SqlQuery<int>(formattedQuery)
                                .ToListAsync();

                int count = results.FirstOrDefault();

                return count;
            }
        }

        public async Task<bool> DeleteEntity1(long? id)
        {
            if (id is null)
            {
                throw new ArgumentNullException(nameof(id));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                Entity1? existingEntity1 = await _dbContext.Entities1.FindAsync(id);

                if (existingEntity1 is null)
                {
                    throw new Exception("Entity1 not found");
                }

                // Get the authentication state
                AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
                ClaimsPrincipal user = authState.User;
                string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);

                if (userId != null)
                {
                    existingEntity1.ModId = userId;
                    existingEntity1.ModTs = DateTime.Now;

                    existingEntity1.IsDeleted = "Y";

                    _dbContext.Entities1.Update(existingEntity1);
                    await _dbContext.SaveChangesAsync();

                    return true;
                }
                else
                {
                    throw new Exception("UserID not found");
                }
            }
        }
    }
}
