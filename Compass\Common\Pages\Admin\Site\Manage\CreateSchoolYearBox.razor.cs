﻿using Compass.Common.Models;
using Compass.Common.Resources;
using Microsoft.AspNetCore.Components;
using System.ComponentModel.DataAnnotations;

namespace Compass.Common.Pages.Admin.Site.Manage
{
    public partial class CreateSchoolYearBox
    {
        [Parameter]
        public EventCallback<bool> CreateSchoolYearResult { get; set; }

        [Parameter]
        public bool IsVisible { get; set; }

        [Parameter]
        public long? OrganizationId { get; set; }

        [Parameter]
        public long? SiteId { get; set; }

        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        protected override void OnParametersSet()
        {
            if (IsVisible)
            {
                Input = new();

                int currentYear = SchoolYearResource.GetDefaultYear();
                Input.SchoolYear = currentYear;

                string description = SchoolYearResource.GetDefaultDescription();
                Input.Description = description;
            }
        }

        protected async Task OnSubmitClick()
        {
            SchoolYear schoolYear = new();
            schoolYear.OrganizationId = OrganizationId;
            schoolYear.SiteId = SiteId;
            schoolYear.SchoolYearValue = Input.SchoolYear;
            schoolYear.Description = Input.Description;
            schoolYear.Status = "Closed";
            await SiteService.CreateSchoolYearAsync(schoolYear);

            IsVisible = false;
            await CreateSchoolYearResult.InvokeAsync(true);
        }

        protected async Task OnCancelClick()
        {
            IsVisible = false;
            await CreateSchoolYearResult.InvokeAsync(false);
        }

        public sealed class InputModel
        {
            [Required]
            [CustomValidation(typeof(InputModel), nameof(ValidateSchoolYear))]
            public int? SchoolYear { get; set; }

            [Required]
            [CustomValidation(typeof(InputModel), nameof(ValidateDescription))]
            public string? Description { get; set; }

            public static ValidationResult? ValidateSchoolYear(int? schoolYear, ValidationContext context)
            {
                return SchoolYearResource.ValidateSchoolYear(schoolYear, context);
            }

            public static ValidationResult? ValidateDescription(string? description, ValidationContext context)
            {
                return SchoolYearResource.ValidateDescription(description, context);
            }
        }
    }
}
