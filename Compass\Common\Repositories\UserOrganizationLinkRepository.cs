﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class UserOrganizationLinkRepository : IUserOrganizationLinkRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public UserOrganizationLinkRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<UserOrganizationLink> AddUserOrganizationLinkAsync(UserOrganizationLink userOrganizationLink)
        {
            if (userOrganizationLink is null)
            {
                throw new ArgumentNullException(nameof(userOrganizationLink));
            }

            // Get the authentication state 
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            userOrganizationLink.ModId = userId;
            userOrganizationLink.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.UserOrganizationLinks.AddAsync(userOrganizationLink);
                await _dbContext.SaveChangesAsync();
            }

            return userOrganizationLink;
        }

        public async Task<UserOrganizationLink?> GetUserOrganizationLinkAsync(long? organizationId, string? userId, long? accessId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (userId is null)
            {
                throw new ArgumentNullException(nameof(userId));
            }

            if (accessId is null)
            {
                throw new ArgumentNullException(nameof(accessId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.UserOrganizationLinks.FirstOrDefaultAsync(o => o.OrganizationId == organizationId && o.UserId == userId && o.OrganizationAccessId == accessId);
            }
        }

        public async Task<bool> RemoveUserOrganizationLinkAsync(long? linkId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                UserOrganizationLink? link = await _dbContext.UserOrganizationLinks.FirstOrDefaultAsync(o => o.Id == linkId);

                if (link == null)
                {
                    return false; // link not found
                }

                _dbContext.UserOrganizationLinks.Remove(link);
                await _dbContext.SaveChangesAsync();
                return true; // Successfully deleted
            }
        }
    }
}
