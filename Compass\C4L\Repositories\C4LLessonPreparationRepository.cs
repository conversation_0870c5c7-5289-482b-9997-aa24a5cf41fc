using Compass.C4L.DTOs;
using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Data.Common;

namespace Compass.C4L.Repositories
{
    public class C4LLessonPreparationRepository : IC4LLessonPreparationRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;

        public C4LLessonPreparationRepository(IDbContextFactory<ApplicationDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<List<C4LLessonPreparation>> GetPreparationsByLessonAsync(string language, int unit, int week, int day)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Set<C4LLessonPreparation>()
                    .Where(p => p.Language == language && p.Unit == unit && p.Week == week && p.Day == day)
                    .OrderBy(p => p.LessonTypeSequence)
                    .ThenBy(p => p.TitleSequence)
                    .ToListAsync();
            }
        }

        public async Task<C4LLessonPreparation?> GetPreparationAsync(string language, int unit, int week, int day, int lessonTypeSequence, int titleSequence)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.Set<C4LLessonPreparation>()
                    .Where(p => p.Language == language && p.Unit == unit && p.Week == week && p.Day == day
                        && p.LessonTypeSequence == lessonTypeSequence && p.TitleSequence == titleSequence)
                    .OrderBy(p => p.LessonTypeSequence)
                    .ThenBy(p => p.TitleSequence)
                    .FirstOrDefaultAsync();
            }
        }

        public async Task<List<LessonPreparationWithTitleDto>> RetrievePreparationsWithTitle(string language, int unit, int week, int day, long? c4l_classroomId = null)
        {
            List<LessonPreparationWithTitleDto> preparations = new List<LessonPreparationWithTitleDto>();

            string sql = @"
                SELECT p.id, p.lesson_id, p.language, p.unit, p.week, p.day,
                       p.lesson_type_sequence, p.title_sequence, p.preparation_tasks,
                       l.title, l.lesson_type,
                       c.id AS completed_id, c.date_completed
                FROM c4l_lesson_preparations_lookup p
                INNER JOIN c4L_lessons_lookup l ON
                    l.language = p.language AND
                    l.unit = p.unit AND
                    l.week = p.week AND
                    l.day = p.day AND
                    l.lesson_type_sequence = p.lesson_type_sequence AND
                    l.title_sequence = p.title_sequence
                LEFT JOIN c4l_lesson_preparations_completed c ON
                    c.lesson_preparation_id = p.id AND
                    c.schoolyear = (
                        SELECT TOP 1 sy.school_year
                        FROM cmn_school_years sy
                        INNER JOIN cmn_sites s ON s.id = sy.site_id
                        INNER JOIN cmn_student_groups sg ON sg.site_id = s.id
                        INNER JOIN c4l_classrooms cls ON cls.student_group_id = sg.id
                        WHERE sy.status = 'Current'
                        AND cls.id = @C4L_ClassroomId
                    )
                WHERE p.language = @Language AND p.unit = @Unit AND p.week = @Week AND p.day = @Day
                ORDER BY p.unit, p.week, p.day, p.lesson_type_sequence, p.title_sequence";

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                DbConnection connection = _dbContext.Database.GetDbConnection();
                DbCommand command = connection.CreateCommand();
                command.CommandText = sql;

                command.Parameters.Add(new SqlParameter("@Language", language));
                command.Parameters.Add(new SqlParameter("@Unit", unit));
                command.Parameters.Add(new SqlParameter("@Week", week));
                command.Parameters.Add(new SqlParameter("@Day", day));

                SqlParameter classroomIdParam = new SqlParameter("@C4L_ClassroomId", System.Data.SqlDbType.BigInt);
                if (c4l_classroomId.HasValue)
                    classroomIdParam.Value = c4l_classroomId.Value;
                else
                    classroomIdParam.Value = DBNull.Value;
                command.Parameters.Add(classroomIdParam);

                if (connection.State != ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }

                using (DbDataReader reader = await command.ExecuteReaderAsync())
                {
                    while (await reader.ReadAsync())
                    {
                        LessonPreparationWithTitleDto preparation = new LessonPreparationWithTitleDto
                        {
                            Id = reader.GetInt64(0),
                            LessonId = reader.GetInt32(1),
                            Language = reader.GetString(2),
                            Unit = reader.GetInt32(3),
                            Week = reader.GetInt32(4),
                            Day = reader.GetInt32(5),
                            LessonTypeSequence = reader.GetInt32(6),
                            TitleSequence = reader.GetInt32(7),
                            PreparationTasks = reader.GetString(8),
                            Title = reader.GetString(9),
                            LessonType = reader.GetString(10),
                            CompletedId = reader.IsDBNull(11) ? null : reader.GetInt64(11),
                            DateCompleted = reader.IsDBNull(12) ? null : reader.GetDateTime(12)
                        };

                        preparations.Add(preparation);
                    }
                }
            }

            return preparations;
        }
    }
}
