﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class OrganizationHierarchyRepository : IOrganizationHierarchyRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public OrganizationHierarchyRepository(IDbContextFactory<ApplicationDbContext> contextFactory
                                               , AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<OrganizationHierarchy> AddOrganizationHierarchyAsync(OrganizationHierarchy organizationHierarchy)
        {
            if (organizationHierarchy is null)
            {
                throw new ArgumentNullException(nameof(organizationHierarchy));
            }

            if (organizationHierarchy.OrganizationId == 0)
            {
                throw new ArgumentNullException(nameof(organizationHierarchy.OrganizationId));
            }

            // Get the authentication state
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            organizationHierarchy.ModId = userId;
            organizationHierarchy.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.OrganizationHierarchies.AddAsync(organizationHierarchy);
                await _dbContext.SaveChangesAsync();
            }

            return organizationHierarchy;
        }

        public async Task<OrganizationHierarchy?> GetOrganizationHierarchyAsync(long? organizationId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.OrganizationHierarchies.FirstOrDefaultAsync(o => o.OrganizationId == organizationId);
            }
        }

        public async Task<OrganizationHierarchy?> UpdateOrganizationHierarchyAsync(long? organizationId, OrganizationHierarchy organizationHierarchy)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (organizationHierarchy is null)
            {
                throw new ArgumentNullException(nameof(organizationHierarchy));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                OrganizationHierarchy existingOrganizationHierarchy = await _dbContext.OrganizationHierarchies.FirstOrDefaultAsync(o => o.OrganizationId == organizationId);

                if (existingOrganizationHierarchy is null)
                {
                    return null;
                }

                existingOrganizationHierarchy.ModTs = DateTime.Now;
                existingOrganizationHierarchy.OrganizationId = organizationHierarchy.OrganizationId;
                existingOrganizationHierarchy.HierarchyEntity1EntityName = organizationHierarchy.HierarchyEntity1EntityName;
                existingOrganizationHierarchy.HierarchyEntity2EntityName = organizationHierarchy.HierarchyEntity2EntityName;
                existingOrganizationHierarchy.HierarchyEntity3EntityName = organizationHierarchy.HierarchyEntity3EntityName;
                existingOrganizationHierarchy.HierarchySiteEntityName = organizationHierarchy.HierarchySiteEntityName;
                existingOrganizationHierarchy.HierarchyStudentGroupEntityName = organizationHierarchy.HierarchyStudentGroupEntityName;
                existingOrganizationHierarchy.HierarchyStudentEntityName = organizationHierarchy.HierarchyStudentEntityName;

                _dbContext.OrganizationHierarchies.Update(existingOrganizationHierarchy);
                await _dbContext.SaveChangesAsync();
                return existingOrganizationHierarchy;
            }
        }

        public async Task<List<VisibleEntity>> GetVisibleEntities(string? userId, long? organizationId)
        {
            List<VisibleEntity>? searchResults = null;

            string sqlQuery = string.Empty;
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                if (organizationId is null)
                {
                    sqlQuery = @"
                    SELECT co.id AS OrganizationId, 0 AS EntityLevel
                        , co.name AS EntityHierarchy, count(*) AS AccessCount
                    FROM cmn_organizations co
                    GROUP BY co.id, co.Name";

                    if (userId is not null)
                    {
                        searchResults = await _dbContext.VisibleEntities
                            .FromSqlRaw(sqlQuery)
                            .AsNoTracking()
                            .ToListAsync();
                    }

                }
                else
                {
                    sqlQuery = @"
                    SELECT coen.id AS OrganizationId, 0 AS EntityLevel
                        , coen.name AS EntityHierarchy, count(DISTINCT lnk.id) AS AccessCount
                    FROM cmn_organizations coen 
                    LEFT JOIN cmn_user_organization_accesses coua 
                        ON coen.id  = coua.organization_id
                    LEFT JOIN cmn_user_organization_links AS lnk
                        ON lnk.cmn_user_organization_access_id = coua.id
                            AND lnk.aspnetuser_id = {1}
                    WHERE coen.id = {0}
                    GROUP BY coen.id, coen.name
                    UNION 
                    SELECT coh.organization_id AS OrganizationId, 1 AS EntityLevel
                        , coh.hierarchy_entity_1_entity_name AS EntityHierarchy, count(DISTINCT lnk.id) AS AccessCount
                    FROM cmn_organization_hierarchies coh 
                    LEFT JOIN cmn_user_entity_1_accesses ceua 
                        ON coh.id  = ceua.organization_id
                    LEFT JOIN cmn_user_entity_1_links AS lnk
                        ON lnk.cmn_user_entity_1_access_id = ceua.id
                            AND lnk.aspnetuser_id = {1}
                    WHERE coh.organization_id = {0}
                    GROUP BY coh.organization_id, coh.hierarchy_entity_1_entity_name
                    UNION
                    SELECT coh.organization_id AS OrganizationId, 2 AS EntityLevel
                        , coh.hierarchy_entity_2_entity_name AS EntityHierarchy, count(DISTINCT lnk.id) AS AccessCount
                    FROM cmn_organization_hierarchies coh 
                    LEFT JOIN cmn_user_entity_2_accesses ceua 
                        ON coh.id  = ceua.organization_id
                    LEFT JOIN cmn_user_entity_2_links AS lnk
                        ON lnk.cmn_user_entity_2_access_id = ceua.id
                            AND lnk.aspnetuser_id = {1}
                    WHERE coh.organization_id = {0}
                    GROUP BY coh.organization_id, coh.hierarchy_entity_2_entity_name
                    UNION
                    SELECT coh.organization_id AS OrganizationId, 3 AS EntityLevel
                        , coh.hierarchy_entity_3_entity_name AS EntityHierarchy, count(DISTINCT lnk.id) AS AccessCount
                    FROM cmn_organization_hierarchies coh 
                    LEFT JOIN cmn_user_entity_3_accesses ceua 
                        ON coh.id  = ceua.organization_id
                    LEFT JOIN cmn_user_entity_3_links AS lnk
                        ON lnk.cmn_user_entity_3_access_id = ceua.id
                            AND lnk.aspnetuser_id = {1}
                    WHERE coh.organization_id = {0}
                    GROUP BY coh.organization_id, coh.hierarchy_entity_3_entity_name
                    UNION
                    SELECT coh.organization_id AS OrganizationId, 4 AS EntityLevel
                        , coh.hierarchy_site_entity_name AS EntityHierarchy, count(DISTINCT lnk.id) AS AccessCount
                    FROM cmn_organization_hierarchies coh 
                    LEFT JOIN cmn_user_site_accesses ceua 
                        ON coh.id  = ceua.organization_id
                    LEFT JOIN cmn_user_site_links AS lnk
                        ON lnk.cmn_user_site_access_id = ceua.id
                            AND lnk.aspnetuser_id = {1}
                    WHERE coh.organization_id = {0}
                    GROUP BY coh.organization_id, coh.hierarchy_site_entity_name
                    UNION
                    SELECT coh.organization_id AS OrganizationId, 5 AS EntityLevel
                        , coh.hierarchy_student_group_entity_name AS EntityHierarchy, count(DISTINCT lnk.id) AS AccessCount
                    FROM cmn_organization_hierarchies coh 
                    LEFT JOIN cmn_user_student_group_accesses ceua 
                        ON coh.id  = ceua.organization_id
                    LEFT JOIN cmn_user_student_group_links AS lnk
                        ON lnk.cmn_user_student_group_access_id = ceua.id
                            AND lnk.aspnetuser_id = {1}
                    WHERE coh.organization_id = {0}
                    GROUP BY coh.organization_id, coh.hierarchy_student_group_entity_name
                    UNION
                    SELECT coh.organization_id AS OrganizationId, 6 AS EntityLevel
                        , coh.hierarchy_student_entity_name AS EntityHierarchy, count(DISTINCT lnk.id) AS AccessCount
                    FROM cmn_organization_hierarchies coh 
                    LEFT JOIN cmn_user_student_group_accesses ceua 
                        ON coh.id  = ceua.organization_id
                    LEFT JOIN cmn_user_student_group_links AS lnk
                        ON lnk.cmn_user_student_group_access_id = ceua.id
                            AND lnk.aspnetuser_id = {1}
                    WHERE coh.organization_id = {0}
                    GROUP BY coh.organization_id, coh.hierarchy_student_entity_name";

                    if (userId is not null)
                    {
                        searchResults = await _dbContext.VisibleEntities
                            .FromSqlRaw(sqlQuery, organizationId, userId)
                            .AsNoTracking()
                            .ToListAsync();
                    }

                }
            }

            return searchResults ?? new List<VisibleEntity>();
        }
    }
}
