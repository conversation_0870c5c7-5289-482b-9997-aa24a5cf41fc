using Compass;
using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Interfaces.Services;
using Compass.C4L.Repositories;
using Compass.C4L.Services;
using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Compass.Common.Repositories;
using Compass.Common.Services;
using Compass.Common.SessionHandlers;
using Compass.Components;
using Compass.Components.Account;
using Compass.DECA.Interfaces.Repositories;
using Compass.DECA.Interfaces.Services;
using Compass.DECA.Repositories;
using Compass.DECA.Services;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.EntityFrameworkCore;
using Serilog;
using Serilog.Enrichers.WithCaller;
using Serilog.Events;
using System.Globalization;

var builder = WebApplication.CreateBuilder(args);

// Add configuration sources in order of precedence
builder.Configuration
    .SetBasePath(builder.Environment.ContentRootPath)
    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddUserSecrets<Program>(optional: true)
    .AddEnvironmentVariables();

builder.Services.AddIdentityCore<ApplicationUser>(options =>
{
    options.SignIn.RequireConfirmedAccount = false;
})
    .AddRoles<IdentityRole>()
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddSignInManager()
    .AddDefaultTokenProviders();

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();
builder.Services.AddControllers();

builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<IdentityUserAccessor>();
builder.Services.AddScoped<IdentityRedirectManager>();
builder.Services.AddScoped<AuthenticationStateProvider, IdentityRevalidatingAuthenticationStateProvider>();

builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = IdentityConstants.ApplicationScheme;
    options.DefaultSignInScheme = IdentityConstants.ExternalScheme;
    options.DefaultChallengeScheme = "OpenIddict"; // Use string literal instead of constant
})
.AddCookie(IdentityConstants.ApplicationScheme, options =>
{
    options.LoginPath = "/login";
    options.LogoutPath = "/Account/Logout";
})
.AddCookie(IdentityConstants.ExternalScheme)
.AddCookie(IdentityConstants.TwoFactorUserIdScheme)
.AddCookie(IdentityConstants.TwoFactorRememberMeScheme)
.AddMicrosoftAccount(microsoftOptions =>
{
    microsoftOptions.ClientId = builder.Configuration["Authentication:Microsoft:ClientId"];
    microsoftOptions.ClientSecret = builder.Configuration["Authentication:Microsoft:ClientSecret"];
    microsoftOptions.CallbackPath = "/signin-microsoft"; // This should match Azure's redirect URI

    microsoftOptions.AuthorizationEndpoint = "https://login.microsoftonline.com/common/oauth2/v2.0/authorize";
    microsoftOptions.TokenEndpoint = "https://login.microsoftonline.com/common/oauth2/v2.0/token";

    /*
    // Add these temporary debug lines
    var logger = builder.Services.BuildServiceProvider().GetRequiredService<ILogger<Program>>();
    logger.LogInformation($"ClientId: {microsoftOptions.ClientId}");
    logger.LogInformation($"CallbackPath: {microsoftOptions.CallbackPath}");
    logger.LogInformation($"AuthorizationEndpoint: {microsoftOptions.AuthorizationEndpoint}");

    Console.WriteLine("=== Microsoft Authentication Settings ===");
    Console.WriteLine($"ClientId: {microsoftOptions.ClientId}");
    Console.WriteLine($"CallbackPath: {microsoftOptions.CallbackPath}");
    Console.WriteLine($"AuthorizationEndpoint: {microsoftOptions.AuthorizationEndpoint}");
    Console.WriteLine("=======================================");
    */
})
.AddGoogle(googleOptions =>
{
    googleOptions.ClientId = builder.Configuration["Authentication:Google:ClientId"];
    googleOptions.ClientSecret = builder.Configuration["Authentication:Google:ClientSecret"];
    // Specify the callback path
    googleOptions.CallbackPath = "/signin-google";  // This is the default value
})/*
.AddOpenIdConnect("OpenIddict", options =>
{
    options.SignInScheme = IdentityConstants.ExternalScheme;
    options.Authority = "https://localhost:7298";
    options.ClientId = "compass-sso-client-id";
    options.ClientSecret = "My-Dev-Secret";
    options.SaveTokens = true;

    options.ResponseType = OpenIdConnectResponseType.Code;
    options.ResponseMode = OpenIdConnectResponseMode.FormPost;

    options.UsePkce = true;
    options.SaveTokens = true;

    options.Scope.Clear();
    options.Scope.Add("openid");
    options.Scope.Add("profile");
    options.Scope.Add("email");
    options.Scope.Add("roles");
})*/;

// Add authorization after authentication
builder.Services.AddAuthorization();

builder.Services.AddScoped<UserAccessor>();

var connectionString = builder.Configuration.GetConnectionString("DefaultConnection") ?? throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");

//builder.Services.AddDbContextFactory<ApplicationDbContext>(options =>
//    options.UseSqlServer(connectionString), ServiceLifetime.Scoped);
builder.Services.AddDbContextFactory<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString, sqlServerOptions =>
    {
        sqlServerOptions.EnableRetryOnFailure(
            maxRetryCount: 5, // Maximum retry attempts
            maxRetryDelay: TimeSpan.FromSeconds(10), // Delay between retries
            errorNumbersToAdd: null // Additional SQL error codes to consider transient
        );
    }),
    ServiceLifetime.Scoped);
builder.Services.AddDatabaseDeveloperPageExceptionFilter();

// builder.Services.AddSingleton<IEmailSender<ApplicationUser>, IdentityNoOpEmailSender>();
// Bind configuration section to MyServiceSettings
builder.Services.Configure<AuthMessageSenderOptions>(
    builder.Configuration.GetSection("SendGridSettings"));
builder.Services.Configure<GodotGameSettings>(
    builder.Configuration.GetSection("GodotGameSettings"));
builder.Services.AddSingleton<IEmailSender<ApplicationUser>, SendGridEmailSender>();

builder.Services.AddScoped<ILicensePoolRepository, LicensePoolRepository>();
builder.Services.AddScoped<IC4LLicenseStudentGroupRepository, C4LLicenseStudentGroupRepository>();

builder.Services.AddScoped<IOrganizationRepository, OrganizationRepository>();
builder.Services.AddScoped<IOrganizationHierarchyRepository, OrganizationHierarchyRepository>();
builder.Services.AddScoped<IEntity1Repository, Entity1Repository>();
builder.Services.AddScoped<IEntity2Repository, Entity2Repository>();
builder.Services.AddScoped<IEntity3Repository, Entity3Repository>();

builder.Services.AddScoped<IUserOrganizationAccessRepository, UserOrganizationAccessRepository>();
builder.Services.AddScoped<IUserEntity1AccessRepository, UserEntity1AccessRepository>();
builder.Services.AddScoped<IUserEntity2AccessRepository, UserEntity2AccessRepository>();
builder.Services.AddScoped<IUserEntity3AccessRepository, UserEntity3AccessRepository>();
builder.Services.AddScoped<IUserSiteAccessRepository, UserSiteAccessRepository>();
builder.Services.AddScoped<IUserStudentGroupAccessRepository, UserStudentGroupAccessRepository>();

builder.Services.AddScoped<ISiteRepository, SiteRepository>();
builder.Services.AddScoped<ISchoolYearRepository, SchoolYearRepository>();
builder.Services.AddScoped<IStudentGroupRepository, StudentGroupRepository>();
builder.Services.AddScoped<IStudentGroupRosterRepository, StudentGroupRosterRepository>();
builder.Services.AddScoped<IStudentRepository, StudentRepository>();
builder.Services.AddScoped<IGameTokenRepository, GameTokenRepository>();

builder.Services.AddScoped<IUserRepository, UserRepository>();
builder.Services.AddScoped<IUserOrganizationLinkRepository, UserOrganizationLinkRepository>();
builder.Services.AddScoped<IUserEntity1LinkRepository, UserEntity1LinkRepository>();
builder.Services.AddScoped<IUserEntity2LinkRepository, UserEntity2LinkRepository>();
builder.Services.AddScoped<IUserEntity3LinkRepository, UserEntity3LinkRepository>();
builder.Services.AddScoped<IUserSiteLinkRepository, UserSiteLinkRepository>();
builder.Services.AddScoped<IUserStudentGroupLinkRepository, UserStudentGroupLinkRepository>();

//Services
builder.Services.AddScoped<IOrganizationService, OrganizationService>();
builder.Services.AddScoped<ILicensePoolService, LicensePoolService>();
builder.Services.AddScoped<IOrganizationHierarchyService, OrganizationHierarchyService>();
builder.Services.AddScoped<IEntity1Service, Entity1Service>();
builder.Services.AddScoped<IEntity2Service, Entity2Service>();
builder.Services.AddScoped<IEntity3Service, Entity3Service>();
builder.Services.AddScoped<ISiteService, SiteService>();
builder.Services.AddScoped<IStudentGroupService, StudentGroupService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IStudentService, StudentService>();
builder.Services.AddScoped<IGameTokenService, GameTokenService>();

//Creating session data classes
builder.Services.AddScoped<CommonSessionData>();
builder.Services.AddSingleton<CurrentCultureObserver>();

// builder.Services.AddLocalization(options => options.ResourcesPath = "Resources");
builder.Services.AddLocalization();

var supportedCultures = new[] { "en-US", "es-ES" };

builder.Services.AddScoped<CultureService>();

var localizationOptions = new RequestLocalizationOptions()
    .SetDefaultCulture(supportedCultures[0])
    .AddSupportedCultures(supportedCultures)
    .AddSupportedUICultures(supportedCultures);

builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    options.DefaultRequestCulture = new RequestCulture(supportedCultures[0]);
    options.SupportedCultures = localizationOptions.SupportedCultures;
    options.SupportedUICultures = localizationOptions.SupportedUICultures;
});

// Configure Logging
builder.Logging.AddDebug(); // Add the Debug provider
// Optionally, add other providers like Console, File, etc.
builder.Logging.AddConsole();
// builder.Logging.AddFile("mylog.txt"); // Example for file logging

// Configure Serilog
// Add Serilog
builder.Host.UseSerilog((hostingContext, loggerConfiguration) =>
{
    loggerConfiguration
        .ReadFrom.Configuration(hostingContext.Configuration)
        .Enrich.FromLogContext()
        .Enrich.WithCaller() // Adds caller information
        .WriteTo.AzureBlobStorage(
            connectionString: hostingContext.Configuration["Serilog:WriteTo:0:Args:ConnectionString"],
            storageContainerName: hostingContext.Configuration["Serilog:WriteTo:0:Args:StorageContainerName"],
            storageFileName: hostingContext.Configuration["Serilog:WriteTo:0:Args:StorageFileName"],
            restrictedToMinimumLevel: LogEventLevel.Warning,
            batchPostingLimit: 500,
            period: TimeSpan.FromDays(1), // Rotate logs daily
            retainedBlobCountLimit: 30 // Retain only the 30 most recent log files
        );
});

// Configure Redis Cache
builder.Services.AddStackExchangeRedisCache(options =>
{
    options.Configuration = builder.Configuration.GetConnectionString("RedisConnection");
    // options.InstanceName = "Kap-redis"; // Prefix for Redis keys
});

// Register the UserSessionService
builder.Services.AddScoped<UserSessionService>();
builder.Services.AddScoped<CommonSessionDataObserver>();

// Configure SignalR to use Azure SignalR service
builder.Services.AddSignalR().AddAzureSignalR(builder.Configuration.GetConnectionString("AzureSignalR"));

// Register HttpClient for C4L CMS API
builder.Services.AddHttpClient("C4LCmsApi");

// C4L
builder.Services.AddScoped<ILessonRepository, LessonRepository>();
builder.Services.AddScoped<ILessonService, LessonService>();
builder.Services.AddScoped<IC4LLearningObjectiveRepository, C4LLearningObjectiveRepository>();
builder.Services.AddScoped<IC4LLearningObjectiveService, C4LLearningObjectiveService>();
builder.Services.AddScoped<IC4LNonContactDayRepository, C4LNonContactDayRepository>();
builder.Services.AddScoped<IC4LNonContactDayService, C4LNonContactDayService>();
builder.Services.AddScoped<IC4LClassroomRepository, C4LClassroomRepository>();
builder.Services.AddScoped<IC4LClassroomService, C4LClassroomService>();
builder.Services.AddScoped<IC4LLearningCenterRepository, C4LLearningCenterRepository>();
builder.Services.AddScoped<IC4LLearningCenterService, C4LLearningCenterService>();
builder.Services.AddScoped<IC4LLessonPreparationRepository, C4LLessonPreparationRepository>();
builder.Services.AddScoped<IC4LLessonPreparationService, C4LLessonPreparationService>();
builder.Services.AddScoped<IC4LLessonPreparationCompletedRepository, C4LLessonPreparationCompletedRepository>();
builder.Services.AddScoped<IC4LLessonPreparationCompletedService, C4LLessonPreparationCompletedService>();
builder.Services.AddScoped<IC4LSessionStateService, DistributedC4LSessionService>();
builder.Services.AddScoped<IC4LCmsApiService, C4LCmsApiService>();

// DECA
builder.Services.AddScoped<IDecaStudentRatingRepository, DecaStudentRatingRepository>();
builder.Services.AddScoped<IDecaStudentRatingService, DecaStudentRatingService>();
builder.Services.AddScoped<IDecaStudentRatingScoreRepository, DecaStudentRatingScoreRepository>();
builder.Services.AddScoped<IDecaStudentRatingScoreService, DecaStudentRatingScoreService>();
builder.Services.AddScoped<IDecaQuestionRepository, DecaQuestionRepository>();
builder.Services.AddScoped<IDecaQuestionService, DecaQuestionService>();
builder.Services.AddScoped<IDECASessionStateService, DistributedDECASessionService>();
var app = builder.Build();

var supportedCultureInfos = new[]
{
    new CultureInfo("en-US"),
    new CultureInfo("es-ES")
};

app.UseRequestLocalization(new RequestLocalizationOptions
{
    DefaultRequestCulture = new RequestCulture("en-US"),
    SupportedCultures = supportedCultureInfos,
    SupportedUICultures = supportedCultureInfos
});

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseMigrationsEndPoint();
}
else
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

app.MapControllers();
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

// Add additional endpoints required by the Identity /Account Razor components.
app.MapAdditionalIdentityEndpoints();

// Configure middleware
app.UseSerilogRequestLogging(); // Add Serilog request logging middleware

app.Run();
