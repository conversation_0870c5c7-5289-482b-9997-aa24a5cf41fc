﻿using Compass.Common.DTOs.Entity3;
using Compass.Common.DTOs.Generic;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Compass.Common.Models;
using Compass.Common.Resources;

namespace Compass.Common.Services
{
    public class Entity3Service : IEntity3Service
    {
        private readonly IEntity3Repository _entity3Repository;
        private readonly ISiteRepository _siteRepository;
        private readonly IUserEntity3AccessRepository _userEntity3AccessRepository;
        private readonly IUserEntity3LinkRepository _userEntity3LinkRepository;
        private readonly IUserRepository _userRepository;


        public Entity3Service(IEntity3Repository entity3Repository,
                                ISiteRepository siteRepository,
                                IUserEntity3AccessRepository userEntity3AccessRepository,
                                IUserEntity3LinkRepository userEntity3LinkRepository,
                                IUserRepository userRepository)
        {
            _entity3Repository = entity3Repository;
            _siteRepository = siteRepository;
            _userEntity3AccessRepository = userEntity3AccessRepository;
            _userEntity3LinkRepository = userEntity3LinkRepository;
            _userRepository = userRepository;
        }

        public async Task<Entity3> CreateEntity3Async(Entity3 entity3)
        {
            Entity3 createdEntity3 = await _entity3Repository.CreateEntity3Async(entity3);

            UserEntity3Access entity3UserAccess = new UserEntity3Access();
            entity3UserAccess.OrganizationId = entity3.OrganizationId;
            entity3UserAccess.Entity3Id = entity3.Id;
            entity3UserAccess.CanAdd = "Y";
            entity3UserAccess.CanUpdate = "Y";
            entity3UserAccess.CanDelete = "Y";
            entity3UserAccess.CanView = "Y";
            entity3UserAccess.CanAssign = "Y";

            await _userEntity3AccessRepository.AddUserEntity3AccessAsync(entity3UserAccess);

            return createdEntity3;
        }

        public async Task<KaplanPageable<Entity3ListDisplayDto>> GetEntity3Page(Entity3ListAction action)
        {
            List<Entity3ListDisplayDto> entity3List = await _entity3Repository.GetEntity3List(action);

            long? organizationId = action.organizationId;
            long? entity1Id = action.entity1Id;
            long? entity2Id = action.entity2Id;

            PageQuery pageQuery = action.pageQuery;
            int pageSize = pageQuery.PageSize;

            int entity3Count = await _entity3Repository.GetEntity3Count(organizationId, entity1Id, entity2Id, pageQuery.QueryText);

            int maxPages = (int)Math.Ceiling((double)entity3Count / pageSize);

            KaplanPageable<Entity3ListDisplayDto> pageable = new KaplanPageable<Entity3ListDisplayDto>();
            pageable.PageContent = entity3List;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<int> GetEntity3Count(long orgId)
        {
            int count = await _entity3Repository.GetEntity3Count(orgId, null, null, string.Empty);
            return count;
        }

        private async Task<bool> ValidateHasUsers(long? organizationId, long? entity3Id)
        {
            int userCount = await _userRepository.GetEntity3UserCount(organizationId, entity3Id, string.Empty);

            if (userCount > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public async Task<DeleteReturnDto> DeleteEntity3(long? organizationId, long? entity1Id, long? entity2Id, long? entity3Id)
        {
            DeleteReturnDto ret = new();
            ret.Success = true;

            // validate sites
            int siteCount = await _siteRepository.GetSiteCount(organizationId, entity1Id, entity2Id, entity3Id, string.Empty);

            if (siteCount > 0)
            {
                ret.Success = false;
                ret.Message = nameof(CommonResource.err_Entity3HasActiveSites);
            }

            // validate users
            bool hasUsers = await ValidateHasUsers(organizationId, entity3Id);
            if (hasUsers)
            {
                ret.Success = false;
                ret.Message = nameof(CommonResource.err_UsersAssigned);
            }

            //If valid then delete
            if (ret.Success)
            {
                bool success = await _entity3Repository.DeleteEntity3(entity3Id);
                ret.Success = success;
            }

            return ret;
        }

        public async Task<UserEntity3Link> AssignEntity3User(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            long? entity3Id = action.EntityId;

            UserEntity3Access? userEntity3Access = await _userEntity3AccessRepository.GetUserEntity3AccessAsync(organizationId, entity3Id);

            if (userEntity3Access is null)
            {
                throw new Exception("No Entity 3 Access Found");
            }

            UserEntity3Link createdLink = new UserEntity3Link();
            createdLink.OrganizationId = organizationId;
            createdLink.Entity3UserAccessId = userEntity3Access.Id;
            createdLink.UserId = action.UserId;
            createdLink.LinkStatus = CompassResource.LinkStatus_Active;
            createdLink.UserRole = "TEST"; // TODO need to figure out user roles

            UserEntity3Link newLink = await _userEntity3LinkRepository.AddUserEntity3LinkAsync(createdLink);

            return newLink;
        }

        public async Task<bool> UnAssignEntity3User(CreateUserLinkAction action)
        {
            long? organizationId = action.OrganizationId;
            long? entity1Id = action.EntityId;
            UserEntity3Access? userEntity3Access = await _userEntity3AccessRepository.GetUserEntity3AccessAsync(organizationId, entity1Id);

            if (userEntity3Access is null)
            {
                throw new Exception("No Entity 3 Access Found");
            }

            string? userId = action.UserId;
            long? accessId = userEntity3Access.Id;

            UserEntity3Link? removeLink = await _userEntity3LinkRepository.GetUserEntity3LinkAsync(organizationId, userId, accessId);

            if (removeLink is null)
            {
                throw new Exception("No Link Found");
            }

            long? linkId = removeLink.Id;
            bool result = await _userEntity3LinkRepository.RemoveUserEntity3LinkAsync(linkId);

            return result;
        }

        public async Task<List<Entity3>> GetEntities3Async(long? organizationId, long? entity2Id)
        {
            if (organizationId == null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (entity2Id == null)
            {
                throw new ArgumentNullException(nameof(entity2Id));
            }

            List<Entity3> ret = await _entity3Repository.GetEntities3Async(organizationId, entity2Id);
            return ret;
        }
    }
}
