﻿using Microsoft.AspNetCore.Components;

namespace Compass.Common.Controls.Generic
{
    public partial class ContactSummary
    {
        [Parameter]
        public string ContactName { get; set; } = string.Empty;

        [Parameter]
        public string Email { get; set; } = string.Empty;

        [Parameter]
        public string Phone { get; set; } = string.Empty;

        [Parameter]
        public string TeacherName { get; set; } = string.Empty;

        [Parameter]
        public string SchoolName { get; set; } = string.Empty;

        [Parameter]
        public string NumberOfChildren { get; set; } = string.Empty;

        public bool HasContactData =>
          !string.IsNullOrWhiteSpace(ContactName) ||
          !string.IsNullOrWhiteSpace(Email) ||
          !string.IsNullOrWhiteSpace(Phone) ||
          !string.IsNullOrWhiteSpace(TeacherName) ||
          !string.IsNullOrWhiteSpace(SchoolName) ||
          !string.IsNullOrWhiteSpace(NumberOfChildren);

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
