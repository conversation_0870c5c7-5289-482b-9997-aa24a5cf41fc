﻿@page "/Account/RegisterConfirmation"

@using System.Text
@using Compass.Common.Data
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities

@inject UserManager<ApplicationUser> UserManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager

<PageTitle>Register Confirmation | C4L</PageTitle>

<h1>Register confirmation</h1>

<StatusMessage Message="@statusMessage" AlertType="@alertType" />

@if (emailConfirmationLink is not null)
{
    <p>
        This app does not currently have a real email sender registered, see <a href="https://aka.ms/aspaccountconf">these docs</a> for how to configure a real email sender.
        Normally this would be emailed: <a href="@emailConfirmationLink">Click here to confirm your account</a>
    </p>
}
else
{
    <p>Please check your email to confirm your account.</p>
}

@code {
    private string? emailConfirmationLink;
    private string? statusMessage;
    private string? alertType = "danger";

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromQuery]
    private string? Email { get; set; }

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (Email is null)
        {
            RedirectManager.RedirectTo("");
        }

        var user = await UserManager.FindByEmailAsync(Email);
        if (user is null)
        {
            HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
            statusMessage = "Error finding user for unspecified email";
        }
        else if (await UserManager.IsEmailConfirmedAsync(user))
        {
            statusMessage = "Email already confirmed";
            alertType = "info";
        }
    }
}
