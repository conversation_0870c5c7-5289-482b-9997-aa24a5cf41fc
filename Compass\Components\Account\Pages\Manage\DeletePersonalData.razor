﻿@page "/account/delete-data"

@using System.ComponentModel.DataAnnotations
@using Compass.Common.Data
@using Compass.Common.DTOs.User
@using Microsoft.AspNetCore.Identity
@using Compass.Common.Interfaces.Services


@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager
@inject ILogger<DeletePersonalData> Logger
@inject IUserService UserService

<PageTitle>Delete Personal Data | C4L</PageTitle>

<StatusMessage Message="@message" AlertType="@alertType" />

<EditForm Model="Input" FormName="delete-user" OnValidSubmit="OnValidSubmitAsync" method="post" class="c4l-form">
    <h2 class="h3 text-center" data-color="c4l-danger">Delete Personal Data</h2>

    <div class="alert alert-warning my-4" role="alert">
        <p class="m-0 text-center font-weight-700">Warning, deleting your personal data will also delete your account. This change is permanent. We are not able to reverse this action once it is completed. Your data cannot be recovered once it has been deleted.</p>
        <p class="mt-2 mb-0 text-center font-weight-700">Enter your password below to finish deleting your account and personal data.</p>
    </div>

    <DataAnnotationsValidator />
    <ValidationSummary class="text-danger" role="alert" />
    @if (requirePassword)
    {
        <div class="form-floating mb-3">
            <InputText type="password" @bind-Value="Input.Password" class="form-control" id="password-input" autocomplete="current-password" placeholder="Please enter your password." />
            <label for="password-input" class="form-label">Password</label>
            <ValidationMessage For="() => Input.Password" class="text-danger" />
        </div>
    }
    <button class="c4l-button c4l-form-button c4l-danger-button" type="submit">Delete my personal data</button>
</EditForm>

@code {
    private string? message;
    private string? alertType = "danger";
    private ApplicationUser user = default!;
    private bool requirePassword;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        Input ??= new();
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);
        requirePassword = await UserManager.HasPasswordAsync(user);
    }

    private async Task<bool> CheckUserAssignments(string userId)
    {
        List<UserAssignmentDto> assignments = await UserService.GetUserAssignments(userId);
        return assignments.Count > 0;
    }

    private async Task OnValidSubmitAsync()
    {
        if (requirePassword && !await UserManager.CheckPasswordAsync(user, Input.Password))
        {
            message = "Error: Incorrect password.";
            return;
        }


        if (await CheckUserAssignments(user.Id))
        {
            message = "Error: Your account cannot be deleted because it is currently assigned to one or more entities. Please contact your administrator to remove these assignments first.";
            alertType = "danger";
            return;
        }

        var result = await UserManager.DeleteAsync(user);
        if (!result.Succeeded)
        {
            throw new InvalidOperationException("Unexpected error occurred deleting user.");
        }

        await SignInManager.SignOutAsync();

        var userId = await UserManager.GetUserIdAsync(user);
        Logger.LogInformation("User with ID '{UserId}' deleted themselves.", userId);

        RedirectManager.RedirectTo("login");
    }

    private sealed class InputModel
    {
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";
    }
}
