﻿using Compass.Common.Data;
using Compass.Common.DTOs.User;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Admin.General
{
    public partial class UserDetails
    {
        [Parameter]
        public required string selectedUserId { get; set; }
        private ApplicationUser? selectedUser;

        private List<UserAssignmentDto> userAssignmentDtoList = new();

        private string DialogMessage = "Are you sure you want to delete user?";
        private bool IsDeleteDialogVisible = false;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;

            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            await GetCommonSessionData();

            selectedUser = await UserAccessor.GetUserAsync(selectedUserId);
            userAssignmentDtoList = await UserService.GetUserAssignments(selectedUserId);
        }

        protected void OnDeleteClick()
        {
            IsDeleteDialogVisible = true;
        }

        protected async Task OnDeleteDialogResult(bool result)
        {
            if (result && selectedUser != null)
            {
                await UserManager.DeleteAsync(selectedUser);
                selectedUser = null;
                NavigationManager.NavigateTo("/userlist");
            }
            else
            {
                IsDeleteDialogVisible = false;
            }
        }
    }
}
