using Compass.C4L.Models;

namespace Compass.C4L.Interfaces.Services
{
    public interface IC4LLessonPreparationCompletedService
    {
        /// <summary>
        /// Gets a C4LLessonPreparationCompleted record by preparation ID and school year
        /// Note, if school year is null, the current school year is used
        /// </summary>
        Task<C4LLessonPreparationCompleted?> GetByPreparationIdAndSchoolYearAsync(long classroomId, long preparationId, int? schoolYear);

        /// <summary>
        /// Creates or updates a C4LLessonPreparationCompleted record
        /// </summary>
        Task<C4LLessonPreparationCompleted> SaveCompletionStatusAsync(long preparationId, long organizationId, long? classroomId, bool isCompleted);
        
        /// <summary>
        /// Gets the current school year for a site
        /// </summary>
        Task<int?> GetCurrentSchoolYearForSiteAsync(long? siteId, long? organizationId);
    }
}
