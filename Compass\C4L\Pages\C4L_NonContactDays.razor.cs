﻿using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Compass.Common.Data;
using Compass.Common.Pages.Admin.StudentGroup;
using Compass.Common.SessionHandlers;
using LanguageExt;
using Microsoft.AspNetCore.Components;

namespace Compass.C4L.Pages
{
    public partial class C4L_NonContactDays
    {
        [Inject]
        public required IC4LNonContactDayService NonContactDayService { get; set; }

        [Inject]
        public required IC4LClassroomService C4LClassroomService { get; set; }

        [Inject]
        private NavigationManager NavigationManager { get; set; } = default!;

        [Inject]
        private IC4LSessionStateService SessionStateService { get; set; } = default!;

        public long? c4l_classroomId { get; set; }

        private string? _currentUserId;
        private ApplicationUser? _currentUser;
        private string errorMessage = string.Empty;
        private string successMessage = string.Empty;

        private IEnumerable<C4LNonContactDay> nonContactDays = new List<C4LNonContactDay>();
        private bool showDeleteConfirmation = false;
        private long? nonContactDayIdToDelete;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            (ApplicationUser? user, string? userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                if (commonSessionData.CurrentStudentGroupId.HasValue)
                {
                    long studentGroupId = commonSessionData.CurrentStudentGroupId.Value;
                    C4LClassroom? c4lc = await C4LClassroomService.GetByStudentGroupIdAsync(studentGroupId);
                    if (c4lc is not null)
                    {
                        this.c4l_classroomId = c4lc.Id;
                    }
                }
            }
            if (this.c4l_classroomId.HasValue)
            {
                await LoadNonContactDays();
            }
        }

        private async Task LoadNonContactDays()
        {
            try
            {
                errorMessage = string.Empty;
                successMessage = string.Empty;

                if (this.c4l_classroomId.HasValue)
                {
                    nonContactDays = await NonContactDayService.GetNonContactDaysAsync(this.c4l_classroomId.Value);
                }
            }
            catch (Exception ex)
            {
                errorMessage = "Failed to load non-contact days";
                Console.WriteLine($"Error loading non-contact days: {ex.Message}");
            }
        }

        private async Task OnAddClick()
        {
            C4LNavigationContext navigationContext = await SessionStateService.GetNavigationContextAsync();
            navigationContext.NonContactDayId = null;
            await SessionStateService.SetNavigationContextAsync(navigationContext);

            NavigationManager.NavigateTo($"/c4l-noncontactday-addedit");
        }

        private async Task OnEditClick(long id)
        {
            C4LNavigationContext navigationContext = await SessionStateService.GetNavigationContextAsync();
            navigationContext.NonContactDayId = id;
            await SessionStateService.SetNavigationContextAsync(navigationContext);

            NavigationManager.NavigateTo($"/c4l-noncontactday-addedit");
        }

        private void OnDeleteClick(long id)
        {
            nonContactDayIdToDelete = id;
            showDeleteConfirmation = true;
        }

        private void CancelDelete()
        {
            showDeleteConfirmation = false;
            nonContactDayIdToDelete = null;
        }

        private async Task ConfirmDelete()
        {
            try
            {
                if (nonContactDayIdToDelete.HasValue)
                {
                    await NonContactDayService.DeleteNonContactDayAsync(nonContactDayIdToDelete.Value);
                    successMessage = "Non-contact day deleted successfully";
                    await LoadNonContactDays();
                }
            }
            catch (Exception ex)
            {
                errorMessage = "Failed to delete non-contact day";
                Console.WriteLine($"Error deleting non-contact day: {ex.Message}");
            }
            finally
            {
                showDeleteConfirmation = false;
                nonContactDayIdToDelete = null;
            }
        }

        private async Task OnDoneClick()
        {
            C4LNavigationContext navigationContext = await SessionStateService.GetNavigationContextAsync();
            navigationContext.NonContactDayId = null;
            await SessionStateService.SetNavigationContextAsync(navigationContext);

            NavigationManager.NavigateTo($"/c4l-student-group-summary");
        }
    }
}
