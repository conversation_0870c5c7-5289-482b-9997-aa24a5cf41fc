@page "/c4l-assessment"
@using Compass.C4L.Models
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

<div class="assessment-container">
    <table class="assessment-table">
        <thead>
            <tr>
                <th style="width: 50%">Objective</th>
                <th style="width: 12.5%">Independent</th>
                <th style="width: 12.5%">With help</th>
                <th style="width: 12.5%">With much help</th>
                <th style="width: 12.5%">Not Assessed</th>
            </tr>
        </thead>
        <tbody>
            @foreach (C4LLearningObjective objective in LearningObjectives)
            {
                <tr>
                    <td>@objective.Title</td>
                    <td>0</td>
                    <td>0</td>
                    <td>0</td>
                    <td>0</td>
                </tr>
            }
        </tbody>
    </table>
</div>

<style>
    .assessment-container {
        padding: 20px;
        width: 100%;
    }

    .assessment-table {
        width: 100%;
        border-collapse: collapse;
    }

    .assessment-table th,
    .assessment-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }

    .assessment-table th {
        background-color: #f4f4f4;
    }

    .assessment-table td:not(:first-child) {
        text-align: center;
    }
</style>