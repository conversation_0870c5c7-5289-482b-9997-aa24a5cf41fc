﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class UserEntity1LinkRepository : IUserEntity1LinkRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public UserEntity1LinkRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<UserEntity1Link> AddUserEntity1LinkAsync(UserEntity1Link userEntity1Link)
        {
            if (userEntity1Link is null)
            {
                throw new ArgumentNullException(nameof(userEntity1Link));
            }

            // Get the authentication state 
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            userEntity1Link.ModId = userId;
            userEntity1Link.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.UserEntity1Links.AddAsync(userEntity1Link);
                await _dbContext.SaveChangesAsync();
            }

            return userEntity1Link;
        }

        public async Task<UserEntity1Link?> GetUserEntity1LinkAsync(long? organizationId, string? userId, long? accessId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (userId is null)
            {
                throw new ArgumentNullException(nameof(userId));
            }

            if (accessId is null)
            {
                throw new ArgumentNullException(nameof(accessId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.UserEntity1Links.FirstOrDefaultAsync(o => o.OrganizationId == organizationId && o.UserId == userId && o.Entity1UserAccessId == accessId);
            }
        }

        public async Task<bool> RemoveUserEntity1LinkAsync(long? linkId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                UserEntity1Link? link = await _dbContext.UserEntity1Links.FirstOrDefaultAsync(o => o.Id == linkId);

                if (link == null)
                {
                    return false; // link not found
                }

                _dbContext.UserEntity1Links.Remove(link);
                await _dbContext.SaveChangesAsync();
                return true; // Successfully deleted
            }
        }
    }
}
