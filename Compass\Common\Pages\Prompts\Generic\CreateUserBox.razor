﻿@using Compass.Common.Data
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject ILogger<CreateUserBox> Logger
@inject IUserStore<ApplicationUser> UserStore

<div class="dialog-overlay" style="@(IsVisible ? "display: flex;" : "display: none;")">
    <div class="dialog-box">
        <h3 class="c4l-form-heading">Add New User</h3>

        <EditForm Model="Input" FormName="formUser" OnValidSubmit="CreateUserAsync">
            <DataAnnotationsValidator></DataAnnotationsValidator>
            <ValidationSummary></ValidationSummary>

            <div class="form-floating mb-3">
                <InputText class="form-control" @bind-Value="Input.FirstName" autocomplete="first-name" id="first-name" required placeholder="First Name" />
                <label class="form-label" for="first-name">First Name</label>
                <ValidationMessage For="() => Input.FirstName" class="text-danger" />
            </div>

            <div class="form-floating mb-3">
                <InputText class="form-control" @bind-Value="Input.LastName" autocomplete="last-name" id="last-name" required placeholder="Last Name" />
                <label for="last-name">Last Name</label>
                <ValidationMessage For="() => Input.LastName" class="text-danger" />
            </div>

            <div class="form-floating mb-3">
                <InputText class="form-control" type="email" @bind-Value="Input.Email" autocomplete="email" id="email" placeholder="<EMAIL>" />
                <label for="email">Email</label>
            </div>

            <div class="form-floating mb-3">
                <InputText class="form-control" @bind-Value="Input.UserName" autocomplete="username" id="user-name" required placeholder="Username" />
                <label for="user-name" class="form-label">Username</label>
                <ValidationMessage For="() => Input.UserName" class="text-danger" />
            </div>

            <div class="form-floating mb-3">
                <InputText class="form-control" type="password" @bind-Value="Input.Password" id="password" autocomplete="new-password" required aria-required="true" placeholder="Password" />
                <label for="password">Password</label>
                <ValidationMessage For="() => Input.Password" class="text-danger" />
            </div>

            <div class="form-floating mb-3">
                <InputText class="form-control" type="password" @bind-Value="Input.ConfirmPassword" id="confirm-password" required autocomplete="new-password" aria-required="true" placeholder="Confirm Password" />
                <label for="confirm-password">Confirm Password</label>
                <ValidationMessage For="() => Input.ConfirmPassword" class="text-danger" />
            </div>

            <div class="form-submit-buttons-wrapper">
                <button class="c4l-button c4l-form-button c4l-primary-button" type="submit">Save</button>
                <button class="c4l-button c4l-form-button c4l-secondary-button" type="button" @onclick="OnCancelClick">Cancel</button>

                @if (!string.IsNullOrEmpty(successMessage))
                {
                    <div class="alert alert-success" role="alert">
                        @successMessage
                    </div>
                }
            </div>
        </EditForm>
    </div>
</div>
