using Compass.C4L.DTOs;
using Compass.C4L.Models;

namespace Compass.C4L.Interfaces.Repositories
{
    public interface IC4LClassroomRepository
    {
        Task<C4LClassroom?> GetC4LClassroomByIdAsync(long id);
        Task<C4LClassroom?> GetByStudentGroupIdAsync(long studentGroupId);
        Task<IEnumerable<C4LClassroom>> GetC4LClassroomByOrganizationIdAsync(long organizationId);
        Task<C4LClassroom?> UpdateC4LClassroomAsync(long? id, C4LClassroom classroom);
        Task<C4LClassroom> CreateC4LClassroomAsync(C4LClassroom classroom);
        Task<C4LClassroom?> GetCurrentByStudentGroupIdAsync(long? organizationId, long? studentGroupId);
        Task<int> GetCurrentClassroomsByAssignedStudentIdCountAsync(long? organizationId, long? studentId);
        Task<List<C4LClassroomSelectionDisplayDto>> GetCurrentClassroomsByAssignedStudentIdAsync(long? organizationId, long? studentId);
    }
}