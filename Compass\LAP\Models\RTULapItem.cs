using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_rt_ulap_items", Schema = "dbo")]
    public class RTULapItem
    {
        [Key]
        [Column("InstID")]
        public long InstId { get; set; }

        [Required]
        [Column("ChildInstID")]
        public long ChildInstId { get; set; }

        [Required]
        [Column("SchoolyearInstID")]
        public long SchoolYearInstId { get; set; }

        [Required]
        [Column("CheckPt")]
        public int CheckPt { get; set; }

        [Required]
        [Column("CheckPtName")]
        [StringLength(30)]
        public string CheckPtName { get; set; } = string.Empty;

        [Column("ChronologicalAge")]
        public int? ChronologicalAge { get; set; }

        [Column("SchoolYear")]
        public int? SchoolYear { get; set; }

        [Required]
        [Column("DomainName")]
        [StringLength(30)]
        public string DomainName { get; set; } = string.Empty;

        [Required]
        [Column("DomainSequence")]
        public int DomainSequence { get; set; }

        [Required]
        [Column("SubscaleStaticID")]
        public long SubscaleStaticId { get; set; }

        [Required]
        [Column("SubscaleName")]
        [StringLength(30)]
        public string SubscaleName { get; set; } = string.Empty;

        [Required]
        [Column("SubscaleID")]
        [StringLength(5)]
        public string SubscaleId { get; set; } = string.Empty;

        [Required]
        [Column("SubscaleSequence")]
        public int SubscaleSequence { get; set; }

        [Required]
        [Column("Language")]
        public int Language { get; set; }

        [Column("RawScore")]
        public short? RawScore { get; set; }

        [Column("Basal")]
        public short? Basal { get; set; }

        [Column("Ceiling")]
        public short? Ceiling { get; set; }

        [Column("ItemCount")]
        public int? ItemCount { get; set; }

        [Required]
        [Column("AssessmentInstID")]
        public long AssessmentInstId { get; set; }

        [Required]
        [Column("AssessmentDate")]
        public DateTime AssessmentDate { get; set; }

        [Required]
        [Column("SubscaleInstID")]
        public long SubscaleInstId { get; set; }

        [Required]
        [Column("SubscaleDate")]
        public DateTime SubscaleDate { get; set; }

        // Items 1-165
        [Column("Item1")]
        public short? Item1 { get; set; }

        // ... (Items 2-164)
        // Note: For brevity, I'll add just a few items here. Let me know if you want the complete list

        [Column("Item165")]
        public short? Item165 { get; set; }

        [Column("ItemsMastered")]
        public int? ItemsMastered { get; set; }

        [Column("ExpressivePotential")]
        public int? ExpressivePotential { get; set; }

        [Column("ExpressiveMastered")]
        public int? ExpressiveMastered { get; set; }

        [Column("ReceptivePotential")]
        public int? ReceptivePotential { get; set; }

        [Column("ReceptiveMastered")]
        public int? ReceptiveMastered { get; set; }
    }
}