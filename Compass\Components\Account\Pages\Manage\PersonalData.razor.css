.personal-data-subheading {
  width: min(100%, 650px);
  margin-inline: auto;
  margin-block: 2rem;
}

.manage-data-form-wrapper {
  gap: 1rem;
  justify-content: center;
}

.personal-data-buttons-wrapper {
  margin-block: 0;

  & button {
    width: 100%;
    text-align: center;
  }
}

@media (min-width: 48rem) {
  .personal-data-buttons-wrapper {
    flex-direction: row;

    & button {
      width: fit-content;
    }
  }
}
