﻿@using Compass.Common.Data
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject NavigationManager NavigationManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject ILogger<CreateUserBox> Logger
@inject IUserStore<ApplicationUser> UserStore

<div class="dialog-overlay" style="@(IsVisible ? "display: flex;" : "display: none;")">
    <div class="dialog-box invite-modal-wrapper @CustomModalClass">
        <h3 class="mb-4">Invite User</h3>

        <EditForm Model="Input" FormName="formUser" OnValidSubmit="InviteUserAsync" class="c4l-form modal-form">
            <DataAnnotationsValidator></DataAnnotationsValidator>
            <ValidationSummary></ValidationSummary>

            <div class="form-floating mb-3">
                <InputText @bind-Value="Input.FirstName" Required="true" class="form-control" id="first-name" placeholder="First Name"></InputText>
                <label for="first-name" class="form-label">First Name</label>
                <ValidationMessage For="() => Input.FirstName" class="text-danger" />
            </div>

            <div class="form-floating mb-3">
                <InputText @bind-Value="Input.LastName" Required="true" class="form-control" id="last-name" placeholder="Last Name"></InputText>
                <label for="last-name" class="form-label">Last Name</label>
                <ValidationMessage For="() => Input.LastName" class="text-danger" />
            </div>

            <div class="form-floating mb-3">
                <InputText @bind-Value="Input.Email" Required="true" class="form-control" id="email" autocomplete="email" aria-required="true" placeholder="<EMAIL>" />
                <label for="email" class="form-label">Email</label>
                <ValidationMessage For="() => Input.Email" class="text-danger" />
            </div>

            <div class="form-submit-buttons-wrapper">
                <button class="c4l-button c4l-form-button c4l-primary-button" type="submit">Save</button>
                <button class="c4l-button c4l-form-button c4l-tertiary-button" @onclick="OnCancelClick" type="button">Cancel</button>
            </div>

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success" role="alert">
                    @successMessage
                </div>
            }
        </EditForm>
    </div>
</div>
