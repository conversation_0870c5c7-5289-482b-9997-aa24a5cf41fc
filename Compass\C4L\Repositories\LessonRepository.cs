using Compass.C4L.Interfaces.Repositories;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Data.Common;

namespace Compass.C4L.Repositories
{
    public class LessonRepository : ILessonRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly ApplicationDbContext _context;

        public LessonRepository(IDbContextFactory<ApplicationDbContext> contextFactory, ApplicationDbContext context)
        {
            _contextFactory = contextFactory;
            _context = context;
        }


        public async Task<List<C4LLesson>> GetAllLessonsAsync(string language)
        {
            return await _context.C4LLessons
                .Where(l => l.Language == language)
                .OrderBy(l => l.Unit)
                .ThenBy(l => l.Week)
                .ThenBy(l => l.Day)
                .ThenBy(l => l.LessonTypeSequence)
                .ThenBy(l => l.TitleSequence)
                .ToListAsync();
        }

        public async Task<List<C4LLesson>> GetLessonPage(string language, int availableDays, int totalDays)
        {
            List<C4LLesson> results = new List<C4LLesson>();
            SqlParameter[] parameters = new SqlParameter[]
            {
                new SqlParameter("@Language", language),
                new SqlParameter("@AvailableDays", availableDays),
                new SqlParameter("@TotalDays", totalDays)
            };

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                // Use ADO.NET directly for more control
                using DbCommand command = _dbContext.Database.GetDbConnection().CreateCommand();
                command.CommandText = "c4l_get_lesson_page";
                command.CommandType = CommandType.StoredProcedure;

                foreach (SqlParameter param in parameters)
                {
                    command.Parameters.Add(param);
                }

                // Ensure connection is open
                if (command.Connection.State != ConnectionState.Open)
                {
                    await command.Connection.OpenAsync();
                }

                try
                {
                    using DbDataReader reader = await command.ExecuteReaderAsync();

                    // Check if we have any rows
                    if (!reader.HasRows)
                    {
                        return results; // Return empty list
                    }

                    while (await reader.ReadAsync())
                    {
                        try
                        {
                            C4LLesson lesson = new C4LLesson
                            {
                                // Use GetOrdinal to find column positions and safely get values
                                Id = reader.GetInt64(reader.GetOrdinal("id")),
                                Language = reader.IsDBNull(reader.GetOrdinal("language"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("language")),
                                Title = reader.IsDBNull(reader.GetOrdinal("title"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("title")),
                                Unit = reader.GetInt32(reader.GetOrdinal("unit")),
                                Week = reader.GetInt32(reader.GetOrdinal("week")),
                                Day = reader.GetInt32(reader.GetOrdinal("day")),
                                LessonType = reader.IsDBNull(reader.GetOrdinal("lesson_type"))
                                    ? string.Empty
                                    : reader.GetString(reader.GetOrdinal("lesson_type")),
                                LessonTypeSequence = reader.GetInt32(reader.GetOrdinal("lesson_type_sequence")),
                                TitleSequence = reader.GetInt32(reader.GetOrdinal("title_sequence"))
                            };

                            results.Add(lesson);
                        }
                        catch (Exception ex)
                        {
                            //TODO use proper logger?
                            // Log the error but continue processing other rows
                            System.Diagnostics.Debug.WriteLine($"Error processing row: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error executing stored procedure: {ex.Message}");
                    throw; // Rethrow the exception after logging
                }
            }

            return results;
        }
    }
}
