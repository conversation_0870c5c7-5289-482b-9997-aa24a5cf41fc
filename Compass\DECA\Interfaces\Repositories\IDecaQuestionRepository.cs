﻿﻿using Compass.Deca.Models;

namespace Compass.DECA.Interfaces.Repositories
{
    public interface IDecaQuestionRepository
    {
        /// <summary>
        /// Gets a question by ID
        /// </summary>
        /// <param name="id">The ID of the question</param>
        /// <returns>The DecaQuestion object if found, null otherwise</returns>
        Task<DecaQuestion?> GetByIdAsync(long id);
        
        /// <summary>
        /// Gets all questions for a specific record form
        /// </summary>
        /// <param name="recordForm">The record form (single character)</param>
        /// <returns>A list of DecaQuestion objects ordered by question number</returns>
        Task<List<DecaQuestion>> GetByRecordFormAsync(string recordForm);
        
        /// <summary>
        /// Gets all questions
        /// </summary>
        /// <returns>A list of all DecaQuestion objects ordered by record form and question number</returns>
        Task<List<DecaQuestion>> GetAllAsync();
        
        /// <summary>
        /// Gets a specific question by record form and question number
        /// </summary>
        /// <param name="recordForm">The record form (single character)</param>
        /// <param name="questionNumber">The question number</param>
        /// <returns>The DecaQuestion object if found, null otherwise</returns>
        Task<DecaQuestion?> GetByRecordFormAndQuestionNumberAsync(string recordForm, int questionNumber);
        
        /// <summary>
        /// Gets all distinct record forms
        /// </summary>
        /// <returns>A list of distinct record form values</returns>
        Task<List<string>> GetDistinctRecordFormsAsync();
        
        /// <summary>
        /// Gets questions by protective factor scale
        /// </summary>
        /// <param name="protectiveFactorScale">The protective factor scale</param>
        /// <returns>A list of DecaQuestion objects with the specified protective factor scale</returns>
        Task<List<DecaQuestion>> GetByProtectiveFactorScaleAsync(string protectiveFactorScale);
    }
}
