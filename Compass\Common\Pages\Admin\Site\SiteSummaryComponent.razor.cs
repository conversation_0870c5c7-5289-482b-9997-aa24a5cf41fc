﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Site;
using Compass.Common.Helpers;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Site
{
    public partial class SiteSummaryComponent : IDisposable
    {
        private string contactName = string.Empty;
        private string email = string.Empty;
        private string phone = string.Empty;

        private int studentGroupCount = 0;
        private int activeStudentCount = 0;
        private int archiveStudentCount = 0;
        private int pendingStudentCount = 0;

        private string currentSiteName = string.Empty;

        private List<Compass.Common.Models.LicensePool> licensePoolPage = new();

        private int maxPages;
        private int currentPage;

        private long? organizationId;
        private bool isLoading = true;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                organizationId = commonSessionData.CurrentOrganizationId;
                long? siteId = commonSessionData.CurrentSiteId;
                SiteSummaryDto siteSummaryDto = await SiteService.GetSiteSummary(organizationId, siteId);

                contactName = siteSummaryDto.ContactName ?? string.Empty;
                currentSiteName = siteSummaryDto.SiteName ?? string.Empty;

                email = siteSummaryDto.ContactEmail ?? string.Empty;
                phone = siteSummaryDto.ContactPhone ?? string.Empty;

                studentGroupCount = siteSummaryDto.StudentGroupCount ?? 0;
                activeStudentCount = siteSummaryDto.ActiveStudentCount ?? 0;
                archiveStudentCount = siteSummaryDto.ArchiveStudentCount ?? 0;
                pendingStudentCount = siteSummaryDto.PendingStudentCount ?? 0;

                currentPage = 1;
                maxPages = 0;

                await GetLicensePoolPage();
            }
        }

        private async Task GetLicensePoolPage()
        {
            isLoading = true;
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;

            KaplanPageable<Compass.Common.Models.LicensePool> currentPage = await LicensePoolService.GetLicensePoolSummaryPages(pageQuery, organizationId);

            licensePoolPage = currentPage.PageContent;
            maxPages = currentPage.MaxPages;
            isLoading = false;

            StateHasChanged();
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetLicensePoolPage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetLicensePoolPage();
            }
        }

        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
