using Compass.C4L.Models;

namespace Compass.C4L.Interfaces.Repositories
{
    public interface IC4LLessonPreparationCompletedRepository
    {
        /// <summary>
        /// Gets a C4LLessonPreparationCompleted record by preparation ID and school year
        /// </summary>
        Task<C4LLessonPreparationCompleted?> GetByPreparationIdAndSchoolYearAsync(long preparationId, int schoolYear);
        
        /// <summary>
        /// Creates a new C4LLessonPreparationCompleted record
        /// </summary>
        Task<C4LLessonPreparationCompleted> CreateAsync(C4LLessonPreparationCompleted completedPreparation);
        
        /// <summary>
        /// Updates an existing C4LLessonPreparationCompleted record
        /// </summary>
        Task<C4LLessonPreparationCompleted> UpdateAsync(C4LLessonPreparationCompleted completedPreparation);
    }
}
