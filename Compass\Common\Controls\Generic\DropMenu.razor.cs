﻿using Microsoft.AspNetCore.Components;

namespace Compass.Common.Controls.Generic
{
    public partial class DropMenu
    {
        [Parameter]
        public string Title { get; set; } = string.Empty;

        [Parameter]
        public List<DropMenuItem> MenuItemList { get; set; } = new();

        [Parameter]
        public EventCallback<string> DropMenuSelectionResult { get; set; }

        private bool isOpen = false;

        private void ToggleDropdown()
        {
            isOpen = !isOpen;
        }

        private async Task OnItemSelected(DropMenuItem item)
        {
            ToggleDropdown();
            await DropMenuSelectionResult.InvokeAsync(item.Value);
        }

        public class DropMenuItem
        {
            public string Name { get; set; } = string.Empty;
            public string Value { get; set; } = string.Empty;
        }
    }
}
