@using Compass.Common.Interfaces.Repositories
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<h2 class="component-heading contact-summary-heading">@Localizer["lbl_ContactInformation"]</h2>

@if (HasContactData)
{
  <section class="border p-3 rounded mb-4 contact-summary-section d-grid">
    @if (!string.IsNullOrWhiteSpace(ContactName))
    {
      <div class="summary-wrapper d-flex">
        <p class="summary-info font-weight-700 mb-0">@Localizer["lbl_Name"]:</p>
        <p class="summary-info mb-0">@ContactName</p>
      </div>
    }

    @if (!string.IsNullOrWhiteSpace(Email))
    {
      <div class="summary-wrapper d-flex">
        <p class="summary-info font-weight-700 mb-0">@Localizer["lbl_Email"]:</p>
        <p class="summary-info mb-0">
          <a href="mailto:@Email">@Email</a>
        </p>
      </div>
    }

    @if (!string.IsNullOrWhiteSpace(Phone))
    {
      <div class="summary-wrapper d-flex">
        <p class="summary-info font-weight-700 mb-0">@Localizer["lbl_Phone"]:</p>
        <p class="summary-info mb-0">
          <a href="tel:@Phone">@Phone</a>
        </p>
      </div>
    }

    @if (!string.IsNullOrWhiteSpace(TeacherName))
    {
      <div class="summary-wrapper d-flex">
        <p class="summary-info font-weight-700 mb-0">@Localizer["lbl_teachers"]:</p>
        <p class="summary-info mb-0">@TeacherName</p>
      </div>
    }

    @if (!string.IsNullOrWhiteSpace(SchoolName))
    {
      <div class="summary-wrapper d-flex">
        <p class="summary-info font-weight-700 mb-0">@Localizer["lbl_School"]:</p>
        <p class="summary-info mb-0">@SchoolName</p>
      </div>
    }

    @if (!string.IsNullOrWhiteSpace(NumberOfChildren))
    {
      <div class="summary-wrapper d-flex">
        <p class="summary-info font-weight-700 mb-0">@Localizer["lbl_Children"]:</p>
        <p class="summary-info mb-0">@NumberOfChildren</p>
      </div>
    }
  </section>
}
else
{
<div class="info-message-wrapper text-center font-weight-500 mb-4">
  <p class="my-0">There is no contact information to show for this entity.</p>
</div>
}
