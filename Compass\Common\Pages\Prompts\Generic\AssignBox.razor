﻿@using Compass.Common.DTOs.User
@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject IUserService UserService

<div class="dialog-overlay" style="@(IsVisible ? "display: flex;" : "display: none;")">
    <div class="modal-scroll-wrapper">
        <div class="dialog-box assign-modal-wrapper @CustomModalClass">
            <div class="search-input-wrapper">
                <div class="form-input-wrapper">
                    <input type="text" class="form-control" placeholder="Search Name or Email" aria-label="Search" @bind="searchText" />
                </div>

                <div class="search-buttons-wrapper">
                    <button class="c4l-button c4l-primary-button" type="button" @onclick="OnSearch">Search</button>
                </div>
            </div>
            
            <div class="c4l-table-scroll-wrapper">
                <div class="c4l-table-wrapper">
                    <div class="c4l-table-headings-wrapper">
                        <h6 class="c4l-table-heading">Username</h6>
                        <h6 class="c4l-table-heading">Email</h6>
                        <h6 class="c4l-table-heading">First Name</h6>
                        <h6 class="c4l-table-heading">Last Name</h6>
                        <h6 class="c4l-table-heading">Assign User</h6>
                    </div>

                    @foreach (UserListDisplayDto user in userResults)
                    {
                        <div class="c4l-table-result-wrapper">
                            <p class="c4l-table-result-item">@user.UserName</p>
                            <p class="c4l-table-result-item">@user.Email</p>
                            <p class="c4l-table-result-item">@user.FirstName</p>
                            <p class="c4l-table-result-item">@user.LastName</p>
                            <button class="c4l-button c4l-secondary-button" @onclick="() => OnUserAssignClick(user.Id)">Assign</button>
                        </div>
                    }
                </div>
            </div>

            <div class="c4l-pagination-wrapper">
                <div class="c4l-pagination-buttons-wrapper">
                    <div class="buttons-wrapper assign-modal-buttons-wrapper">
                        <button
                            class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                            @onclick="() => OnPreviousClicked()" 
                            disabled="@(currentPage <= 1)"
                            type="button"
                        >
                            Previous
                        </button>

                        <button 
                            class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                            @onclick="() => OnNextClicked()"
                            disabled="@(currentPage >= maxPages)"
                            type="button"
                        >
                            Next
                        </button>

                        <button type="button" class="c4l-button c4l-tertiary-button c4l-pagination-button" @onclick="() => OnUserCancelClick()">Cancel</button>
                    </div>
                </div>

                <div class="page-count-wrapper font-weight-500">
                    <span class="current-page-number">@currentPage</span> of @maxPages
                </div>
            </div>
        </div>
    </div>
</div>
