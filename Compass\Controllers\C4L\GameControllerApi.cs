﻿using Compass.C4L.Helpers;
using Compass.C4L.Models;
using Compass.Common.Data;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Compass.Controllers.C4L
{
    [ApiController]
    [Route("api/[controller]")]
    public class GameControllerApi : Controller
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;
        public GameControllerApi(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public IActionResult Index()
        {
            return View();
        }

        [HttpGet("validate")]
        public ActionResult<GameParameters> ValidateToken(string token)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                C4LGameToken? gameToken = _dbContext.C4LGameTokens
                .FirstOrDefault(t => t.Token == token && t.Used == "N" && t.ExpireTs > DateTime.UtcNow);

                if (gameToken == null)
                {
                    return NotFound(new { error = "Invalid or expired token" });
                }

                // Mark token as used (one-time use)
                gameToken.Used = "Y";
                _dbContext.SaveChanges();

                return Ok(new GameParameters
                {
                    Unit = gameToken.Unit,
                    Week = gameToken.Week,
                    StudentName = gameToken.StudentName,
                    StudentId = (long)gameToken.StudentId,
                    C4LClassroomId = (long)gameToken.C4LClassroomId,
                    OrganizationId = (long)gameToken.OrganizationId
                });
            }
        }
    }
}
