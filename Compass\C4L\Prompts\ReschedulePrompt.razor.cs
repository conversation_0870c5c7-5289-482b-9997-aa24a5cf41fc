﻿using Compass.C4L.Interfaces.Services;
using Compass.C4L.Models;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Forms;

namespace Compass.C4L.Prompts
{
    public partial class ReschedulePrompt
    {
        [Inject]
        public required IC4LNonContactDayService NonContactDayService { get; set; }

        [Parameter]
        public EventCallback<bool> RescheduleResult { get; set; }

        [Parameter]
        public bool IsVisible { get; set; }
        [Parameter]
        public long? OrganizationId { get; set; }

        [Parameter]
        public long? C4L_ClassroomId { get; set; }
        [Parameter]
        public DateTime OriginalDate { get; set; }
        [SupplyParameterFromForm]
        private InputModel Input { get; set; } = new();

        protected override void OnParametersSet()
        {
            if (IsVisible)
            {
                Input = new(); // Clear input when the dialog is shown
                Input.NewStartDate ??= OriginalDate.AddDays(1); //Get Next days date
            }
        }

        private C4LNonContactDay CreateNonContactDay()
        {
            C4LNonContactDay nonContactDay = new C4LNonContactDay();

            if (this.C4L_ClassroomId != null)
            {
                nonContactDay.C4L_ClassroomId = (long)this.C4L_ClassroomId;
            }

            if (this.OrganizationId != null)
            {
                nonContactDay.OrganizationId = (long)this.OrganizationId;
            }

            nonContactDay.StartDate = OriginalDate;

            if (Input.NewStartDate != null)
            {
                DateTime rescheduleDate = Input.NewStartDate.Value.AddDays(-1);
                nonContactDay.EndDate = rescheduleDate;
            }

            nonContactDay.Description = "Rescheduled";

            return nonContactDay;
        }

        protected async Task OnRescheduleSubmit(EditContext editContext)
        {
            if (this.C4L_ClassroomId.HasValue)
            {
                C4LNonContactDay nonContactDay = CreateNonContactDay();

                bool isValid = await NonContactDayService.ValidateDateRange(
                            this.C4L_ClassroomId.Value,
                            nonContactDay.StartDate,
                            nonContactDay.EndDate,
                            null
                        );

                if (isValid)
                {
                    await NonContactDayService.CreateNonContactDayAsync(nonContactDay);
                }

                await RescheduleResult.InvokeAsync(true);
            }
        }

        protected async Task OnCancelClick()
        {
            await RescheduleResult.InvokeAsync(false);
        }

        public sealed class InputModel
        {
            public DateTime? NewStartDate { get; set; }
        }
    }
}
