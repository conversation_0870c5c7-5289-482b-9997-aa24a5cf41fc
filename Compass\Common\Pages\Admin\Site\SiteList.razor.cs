﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.Site;
using Compass.Common.Helpers;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.Site
{
    public partial class SiteList : IDisposable
    {
        private List<SiteListDisplayDto> siteResults = new();
        private string siteHierarchy = string.Empty;
        private string entity3Hierarchy = string.Empty;
        private string entity2Hierarchy = string.Empty;
        private string entity1Hierarchy = string.Empty;

        private int maxPages;
        private int currentPage;

        private long? currentOrganizationId;
        private long? currentEntity1Id;
        private long? currentEntity2Id;
        private long? currentEntity3Id;

        private string searchText = string.Empty;
        private string currentSearchText = string.Empty;

        private bool noSearchResults = false;
        private bool isLoading = true;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUser != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUser.Id);
                if (commonSessionData != null)
                {
                    currentOrganizationId = commonSessionData.CurrentOrganizationId;
                }
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            CurrentLanguageObserver.AddStateChangeListeners(UpdateLocalizedValues);

            searchText = string.Empty;
            currentSearchText = string.Empty;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                siteHierarchy = commonSessionData.SiteHierarchy;
                entity3Hierarchy = commonSessionData.Entity3Hierarchy;
                entity2Hierarchy = commonSessionData.Entity2Hierarchy;
                entity1Hierarchy = commonSessionData.Entity1Hierarchy;

                currentOrganizationId = commonSessionData.CurrentOrganizationId;
                currentEntity1Id = commonSessionData.CurrentEntity1Id;
                currentEntity2Id = commonSessionData.CurrentEntity2Id;
                currentEntity3Id = commonSessionData.CurrentEntity3Id;

                currentPage = 1;
                maxPages = 0;
                await GetSitePage();
            }
        }
        private void UpdateLocalizedValues()
        {
            var culture = CurrentLanguageObserver.GetCurrentCulture();
            CultureService.SetCulture(culture);

            InvokeAsync(StateHasChanged);
        }

        private async Task GetSitePage()
        {
            isLoading = true;
            SiteListAction action = new();
            PageQuery pageQuery = new PageQuery();
            pageQuery.PageNumber = this.currentPage;
            pageQuery.QueryText = currentSearchText;

            if (currentOrganizationId is not null)
            {
                action.pageQuery = pageQuery;
                action.organizationId = currentOrganizationId;
                action.userId = _currentUserId;

                action.entity1Id = currentEntity1Id;
                action.entity2Id = currentEntity2Id;
                action.entity3Id = currentEntity3Id;

                KaplanPageable<SiteListDisplayDto> currentPage = await SiteService.GetSitePage(action);

                siteResults = currentPage.PageContent;
                maxPages = currentPage.MaxPages;
                noSearchResults = !string.IsNullOrEmpty(currentSearchText) && siteResults.Count == 0;
                isLoading = false;

                StateHasChanged();
            }
        }

        protected async Task OnSiteSelected(SiteListDisplayDto site)
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                commonSessionData.CurrentSiteId = site.Id;
                commonSessionData.CurrentEntity3Id = site.Entity3Id;
                commonSessionData.CurrentEntity2Id = site.Entity2Id;
                commonSessionData.CurrentEntity1Id = site.Entity1Id;

                if (site.Name != null)
                {
                    commonSessionData.SelectedEntityName = site.Name;
                }
                else
                {
                    commonSessionData.SelectedEntityName = string.Empty;
                }

                if (_currentUser != null)
                {
                    await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                    await CommonSessionDataObserver.BroadcastStateChangeAsync();

                    NavigationManager.NavigateTo($"/site");
                }
            }
        }

        protected async Task OnPreviousClicked()
        {
            if (currentPage > 1)
            {
                currentPage--;
                await GetSitePage();
            }
        }

        protected async Task OnNextClicked()
        {
            if (currentPage < maxPages)
            {
                currentPage++;
                await GetSitePage();
            }
        }

        protected async Task OnSearch(string searchQuery)
        {
            this.currentPage = 1;
            searchText = searchQuery;
            currentSearchText = searchQuery;
            noSearchResults = false;
            await GetSitePage();
        }

        public void Dispose()
        {
            CurrentLanguageObserver.RemoveStateChangeListeners(UpdateLocalizedValues);
        }
    }
}
