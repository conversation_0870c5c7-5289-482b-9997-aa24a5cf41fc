﻿using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IUserEntity3LinkRepository
    {
        public Task<UserEntity3Link> AddUserEntity3LinkAsync(UserEntity3Link userEntity3Link);
        public Task<UserEntity3Link?> GetUserEntity3LinkAsync(long? organizationId, string? userId, long? accessId);
        public Task<bool> RemoveUserEntity3LinkAsync(long? linkId);
    }
}
