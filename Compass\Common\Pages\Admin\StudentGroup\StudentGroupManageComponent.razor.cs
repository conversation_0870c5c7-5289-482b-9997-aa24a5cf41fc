﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.SessionHandlers;

namespace Compass.Common.Pages.Admin.StudentGroup
{
    public partial class StudentGroupManageComponent
    {
        private bool IsDeleteDialogVisible = false;
        private bool IsDisplayMessageVisible = false;

        private string StudentGroupName = string.Empty;
        private string DialogMessage = string.Empty;
        private string DisplayMessage = string.Empty;
        private string StudentGroupHierarchy = string.Empty;

        private long? currentStudentGroupId;
        private long? currentOrgId;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            IsDeleteDialogVisible = false;
            IsDisplayMessageVisible = false;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                StudentGroupName = commonSessionData.SelectedEntityName;
                DialogMessage = "Are you sure you want to delete " + StudentGroupName + "?";
                StudentGroupHierarchy = commonSessionData.StudentGroupHierarchy;

                this.currentStudentGroupId = commonSessionData.CurrentStudentGroupId;
                this.currentOrgId = commonSessionData.CurrentOrganizationId;
            }
        }

        protected void OnDeleteSelected()
        {
            IsDeleteDialogVisible = true;
        }

        protected async Task OnDeleteDialogResult(bool result)
        {
            IsDeleteDialogVisible = false;
            if (result)
            {
                await DeleteStudentGroups();
            }
        }

        protected void OnDisplayMessageResult()
        {
            IsDisplayMessageVisible = false;
        }

        private async Task DeleteStudentGroups()
        {
            DeleteReturnDto deleteReturnDto = await StudentGroupService.DeleteStudentGroup(this.currentOrgId, this.currentStudentGroupId);

            if (deleteReturnDto.Success)
            {
                CommonSessionData? commonSessionData = await GetCommonSessionData();
                if (commonSessionData is not null)
                {
                    commonSessionData.ResetCurrentIdValues();

                    if (_currentUser != null)
                    {
                        await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                        await CommonSessionDataObserver.BroadcastStateChangeAsync();
                    }

                    NavigationManager.NavigateTo($"/grouplist");
                }
            }
            else
            {
                IsDisplayMessageVisible = true;
            }
        }
    }
}
