﻿using Compass.Common.Data;
using Compass.Common.DTOs.Generic;
using Compass.Common.Pages.Admin.Site.Manage;
using Compass.Common.SessionHandlers;
using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Admin.Site
{
    public partial class SiteManageComponent
    {
        private bool ShowMenu = true;
        private Type? CurrentTabComponent;

        private bool IsDeleteDialogVisible = false;
        private bool IsDisplayMessageVisible = false;

        private string SiteName = string.Empty;
        private string DialogMessage = string.Empty;
        private string SiteHierarchy = string.Empty;

        private string? _currentUserId;
        private ApplicationUser? _currentUser;

        private async Task<CommonSessionData?> GetCommonSessionData()
        {
            CommonSessionData? commonSessionData = null;
            var (user, userId) = await UserAccessor.GetUserAndIdAsync();
            _currentUser = user;
            _currentUserId = userId;
            if (_currentUserId != null)
            {
                commonSessionData = await UserSessionService.GetUserSessionAsync(_currentUserId);
            }

            return commonSessionData;
        }

        protected override async Task OnInitializedAsync()
        {
            IsDeleteDialogVisible = false;
            IsDisplayMessageVisible = false;

            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                SiteName = commonSessionData.SelectedEntityName;
                DialogMessage = "Are you sure you want to delete " + SiteName + "?";
                SiteHierarchy = commonSessionData.SiteHierarchy;
            }
        }

        protected void OnDeleteSelected()
        {
            IsDeleteDialogVisible = true;
        }

        protected async Task OnDeleteDialogResult(bool result)
        {
            if (result)
            {
                await DeleteSite();
            }
            else
            {
                IsDeleteDialogVisible = false;
            }
        }

        protected void OnDisplayMessageResult()
        {
            IsDisplayMessageVisible = false;
        }

        private async Task DeleteSite()
        {
            CommonSessionData? commonSessionData = await GetCommonSessionData();
            if (commonSessionData is not null)
            {
                long? currentOrgId = commonSessionData.CurrentOrganizationId;
                long? currentSiteId = commonSessionData.CurrentSiteId;

                DeleteReturnDto deleteReturnDto = await SiteService.DeleteSite(currentOrgId, currentSiteId);

                IsDeleteDialogVisible = false;

                if (deleteReturnDto.Success)
                {
                    commonSessionData.ResetCurrentIdValues();

                    if (_currentUser != null)
                    {
                        await UserSessionService.SetUserSessionAsync(_currentUser.Id, commonSessionData);
                        await CommonSessionDataObserver.BroadcastStateChangeAsync();
                    }
                    NavigationManager.NavigateTo($"/sitelist");
                }
                else
                {
                    IsDisplayMessageVisible = true;
                }
            }
        }

        protected Dictionary<string, object> GetDynamicParameters()
        {
            Dictionary<string, object> parameters = new Dictionary<string, object>();

            if (CurrentTabComponent != null)
            {
                // Add parameters conditionally
                if (CurrentTabComponent == typeof(SiteSchoolYearListComponent))
                {
                    parameters[nameof(SiteSchoolYearListComponent.OnReturn)] = EventCallback.Factory.Create(this, HandleReturn);
                }
            }

            return parameters;
        }

        private void HandleReturn()
        {
            ShowMenu = true;
        }

        protected void OnManageSchoolYears()
        {
            ShowMenu = false;
            CurrentTabComponent = typeof(SiteSchoolYearListComponent);
        }
    }
}
