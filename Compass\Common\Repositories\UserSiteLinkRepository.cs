﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class UserSiteLinkRepository : IUserSiteLinkRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public UserSiteLinkRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<UserSiteLink> AddUserSiteLinkAsync(UserSiteLink userSiteLink)
        {
            if (userSiteLink is null)
            {
                throw new ArgumentNullException(nameof(userSiteLink));
            }

            // Get the authentication state 
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            userSiteLink.ModId = userId;
            userSiteLink.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.UserSiteLinks.AddAsync(userSiteLink);
                await _dbContext.SaveChangesAsync();
            }

            return userSiteLink;
        }

        public async Task<UserSiteLink?> GetUserSiteLinkAsync(long? organizationId, string? userId, long? accessId)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (userId is null)
            {
                throw new ArgumentNullException(nameof(userId));
            }

            if (accessId is null)
            {
                throw new ArgumentNullException(nameof(accessId));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.UserSiteLinks.FirstOrDefaultAsync(o => o.OrganizationId == organizationId && o.UserId == userId && o.SiteUserAccessId == accessId);
            }
        }

        public async Task<bool> RemoveUserSiteLinkAsync(long? linkId)
        {
            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                UserSiteLink? link = await _dbContext.UserSiteLinks.FirstOrDefaultAsync(o => o.Id == linkId);

                if (link == null)
                {
                    return false; // link not found
                }

                _dbContext.UserSiteLinks.Remove(link);
                await _dbContext.SaveChangesAsync();
                return true; // Successfully deleted
            }
        }
    }
}
