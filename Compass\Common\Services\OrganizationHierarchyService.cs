﻿using Compass.Common.DTOs.OrganizationHierarchies;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;
using Compass.Common.Models;
using Compass.Common.Resources;

namespace Compass.Common.Services
{
    public class OrganizationHierarchyService : IOrganizationHierarchyService
    {
        private readonly IOrganizationHierarchyRepository _organizationHierarchyRepository;
        private readonly IEntity1Service _entity1Service;
        private readonly IEntity2Service _entity2Service;
        private readonly IEntity3Service _entity3Service;

        public OrganizationHierarchyService(IOrganizationHierarchyRepository organizationHierarchyRepository,
                                            IEntity1Service entity1Service,
                                            IEntity2Service entity2Service,
                                            IEntity3Service entity3Service)
        {
            _organizationHierarchyRepository = organizationHierarchyRepository;
            _entity1Service = entity1Service;
            _entity2Service = entity2Service;
            _entity3Service = entity3Service;
        }

        private async Task<OrganizationHierarchy> AddOrganizationHierarchyAsync(OrganizationHierarchy organizationHierarchy)
        {
            OrganizationHierarchy createdHierarchy = await _organizationHierarchyRepository.AddOrganizationHierarchyAsync(organizationHierarchy);

            Entity1 entity1 = new Entity1();
            entity1.OrganizationId = organizationHierarchy.OrganizationId;
            entity1.Name = "Default";
            entity1.IsDeleted = "D";
            Entity1 entity1Result = await _entity1Service.CreateEntity1Async(entity1);

            Entity2 entity2 = new Entity2();
            entity2.OrganizationId = organizationHierarchy.OrganizationId;
            entity2.Entity1Id = entity1Result.Id;
            entity2.Name = "Default";
            entity2.IsDeleted = "D";
            Entity2 entity2Result = await _entity2Service.CreateEntity2Async(entity2);

            Entity3 entity3 = new Entity3();
            entity3.OrganizationId = organizationHierarchy.OrganizationId;
            entity3.Entity1Id = entity1Result.Id;
            entity3.Entity2Id = entity2Result.Id;
            entity3.Name = "Default";
            entity3.IsDeleted = "D";
            await _entity3Service.CreateEntity3Async(entity3);

            return createdHierarchy;
        }

        public async Task<ValidateRemovedEntitiesDto> ValidateRemovedEntities(OrganizationHierarchy organizationHierarchy)
        {
            long orgId = organizationHierarchy.OrganizationId;
            OrganizationHierarchy? sourceHierarchy = await _organizationHierarchyRepository.GetOrganizationHierarchyAsync(orgId);
            ValidateRemovedEntitiesDto ret = new ValidateRemovedEntitiesDto();

            if (sourceHierarchy != null)
            {
                string? sourceEntity1Hierarchy = sourceHierarchy.HierarchyEntity1EntityName;
                string? sourceEntity2Hierarchy = sourceHierarchy.HierarchyEntity2EntityName;
                string? sourceEntity3Hierarchy = sourceHierarchy.HierarchyEntity3EntityName;

                string? entity1Hierarchy = organizationHierarchy.HierarchyEntity1EntityName;
                string? entity2Hierarchy = organizationHierarchy.HierarchyEntity2EntityName;
                string? entity3Hierarchy = organizationHierarchy.HierarchyEntity3EntityName;

                if (!string.IsNullOrEmpty(sourceEntity1Hierarchy) && string.IsNullOrEmpty(entity1Hierarchy))
                {
                    int entity1Count = await _entity1Service.GetEntity1Count(orgId);

                    if (entity1Count > 1)
                    {
                        ret.Entity1Removed = true;
                    }
                }
                if (!string.IsNullOrEmpty(sourceEntity2Hierarchy) && string.IsNullOrEmpty(entity2Hierarchy))
                {
                    int entity2Count = await _entity2Service.GetEntity2Count(orgId);

                    if (entity2Count > 1)
                    {
                        ret.Entity2Removed = true;
                    }
                }
                if (!string.IsNullOrEmpty(sourceEntity3Hierarchy) && string.IsNullOrEmpty(entity3Hierarchy))
                {
                    int entity3Count = await _entity3Service.GetEntity3Count(orgId);

                    if (entity3Count > 1)
                    {
                        ret.Entity3Removed = true;
                    }
                }
            }

            return ret;
        }

        private bool ValidateHierarchyOrder(OrganizationHierarchy organizationHierarchy)
        {
            bool isValid = true;

            string entity1Hierarchy = organizationHierarchy.HierarchyEntity1EntityName;
            string entity2Hierarchy = organizationHierarchy.HierarchyEntity2EntityName;
            string entity3Hierarchy = organizationHierarchy.HierarchyEntity3EntityName;

            if (entity3Hierarchy != string.Empty)
            {
                if (entity1Hierarchy == string.Empty || entity2Hierarchy == string.Empty)
                {
                    isValid = false;
                }
            }

            if (entity2Hierarchy != string.Empty)
            {
                if (entity1Hierarchy == string.Empty)
                {
                    isValid = false;
                }
            }

            return isValid;
        }

        private void DefaultHierarchyFields(OrganizationHierarchy organizationHierarchy)
        {
            string siteHierarchy = organizationHierarchy.HierarchySiteEntityName;
            string studentGroupHierarchy = organizationHierarchy.HierarchyStudentEntityName;

            if (string.IsNullOrEmpty(siteHierarchy))
            {
                organizationHierarchy.HierarchySiteEntityName = HierarchyResource.DefaultSiteName;
            }

            if (string.IsNullOrEmpty(studentGroupHierarchy))
            {
                organizationHierarchy.HierarchyStudentEntityName = HierarchyResource.DefaultStudentGroupName;
            }
        }

        public async Task<SaveHierarchyDto?> SaveHierarchy(OrganizationHierarchy organizationHierarchy)
        {
            SaveHierarchyDto ret = new SaveHierarchyDto();
            OrganizationHierarchy? savedOrganizationHierarchy = null;

            if (organizationHierarchy.Id == 0)
            {
                savedOrganizationHierarchy = await AddOrganizationHierarchyAsync(organizationHierarchy);
                ret.SavedOrganizationHierarchy = savedOrganizationHierarchy;
            }
            else
            {
                bool correctOrder = ValidateHierarchyOrder(organizationHierarchy);
                if (correctOrder)
                {
                    DefaultHierarchyFields(organizationHierarchy);

                    long organizationId = organizationHierarchy.OrganizationId;
                    savedOrganizationHierarchy = await _organizationHierarchyRepository.UpdateOrganizationHierarchyAsync(organizationId, organizationHierarchy);

                    ret.SavedOrganizationHierarchy = savedOrganizationHierarchy;
                }
                else
                {
                    //Handle return dto to tell client that order is incorrect
                    ret.Message = nameof(CommonResource.err_HierarchyOrder);
                }
            }

            return ret;
        }
    }
}
