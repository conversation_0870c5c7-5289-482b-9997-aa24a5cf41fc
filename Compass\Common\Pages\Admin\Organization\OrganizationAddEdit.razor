﻿@page "/organization/addedit/{organizationId:long}"
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject NavigationManager NavigationManager
@inject IOrganizationService OrganizationService
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService
@inject UserSessionService UserSessionService
@inject CommonSessionDataObserver CommonSessionDataObserver;
@inject UserAccessor UserAccessor

@if (organizationId > 0)
{
    <h1 class="page-title">@Localizer["lbl_EditOrganization"]</h1>
}
else
{
    <h1 class="page-title">@Localizer["lbl_AddOrganization"]</h1>
}

<div class="component-content-wrapper organization-addedit-wrapper">
    @if (organization is not null)
    {
        <EditForm Model="organization" FormName="formOrganization" OnValidSubmit="OnValidateSubmit" class="c4l-form organization-add-edit-form">
            <DataAnnotationsValidator></DataAnnotationsValidator>
            <ValidationSummary></ValidationSummary>

            @if (organizationId > 0)
            {
                <InputNumber @bind-Value="organization.Id" hidden></InputNumber>
            }

            <h3 class="c4l-form-heading">@Localizer["org-addedit-details"]</h3>

            <div>
                <label class="col-form-label" for="organizationName">
                    @Localizer["lbl_Name"]
                    <InputText 
                        @bind-Value="organization.Name" 
                        id="organizationName" 
                        class="form-control mt-2"
                    />
                </label>
            </div>

            <div>
                <label class="col-form-label" for="organization-contact-email">
                    @Localizer["lbl_ContactEmail"]
                    <InputText 
                        @bind-Value="organization.ContactEmail" 
                        id="organization-contact-email" 
                        class="form-control mt-2"
                    />
                </label>
            </div>

            <div>
                <label class="col-form-label" for="organization-contact-firstname">
                    @Localizer["lbl_ContactFirstName"]
                    <InputText 
                        @bind-Value="organization.ContactFirstName" 
                        id="organization-contact-firstname" 
                        class="form-control mt-2"
                    />
                </label>
            </div>

            <div>
                <label class="col-form-label" for="organization-contact-lastname">
                    @Localizer["lbl_ContactLastName"]
                    <InputText 
                        @bind-Value="organization.ContactLastName" 
                        id="organization-contact-lastname" 
                        class="form-control mt-2"
                    />
                </label>
            </div>

            <div>
                <label class="col-form-label" for="organization-contact-phone-number">
                    @Localizer["lbl_ContactPhone"]
                    <InputText 
                        @bind-Value="organization.ContactPhone" 
                        id="organization-contact-phone-number" 
                        class="form-control mt-2"
                    />
                </label>
            </div>

            <div>
                <label class="col-form-label" for="organization-address1">
                    @Localizer["lbl_Address1"]
                    <InputText 
                        @bind-Value="organization.Address1" 
                        id="organization-address1" 
                        class="form-control mt-2"
                    />
                </label>
            </div>

            <div>
                <label class="col-form-label" for="organization-address2">
                    @Localizer["lbl_Address2"]
                    <InputText 
                        @bind-Value="organization.Address2" 
                        id="organization-address2" 
                        class="form-control mt-2"
                    />
                </label>
            </div>

            <div>
                <label class="col-form-label" for="organization-city">
                    @Localizer["lbl_City"]
                    <InputText 
                        @bind-Value="organization.City" 
                        id="organization-city" 
                        class="form-control mt-2"
                    />
                </label>
            </div>

            <div>
                <label class="col-form-label" for="organization-state">
                    @Localizer["lbl_State"]
                    <InputSelect 
                        @bind-Value="organization.State"
                        id="organization-state" 
                    >
                        <option disabled value="">-- @Localizer["lbl_State"] --</option>

                        @foreach (var stateCode in CompassResource.UsStates)
                        {
                            <option value="@stateCode">@stateCode</option>
                        }
                    </InputSelect>
                    <ValidationMessage For="() => organization.State" />
                </label>
            </div>

            <div>
                <label class="col-form-label" for="organization-zipcode">
                    @Localizer["lbl_ZipCode"]
                    <InputText 
                        @bind-Value="organization.ZipCode" 
                        id="organization-zipcode" 
                        class="form-control mt-2"
                    />
                </label>
            </div>

            <div class="form-submit-buttons-wrapper">
                @if (organizationId > 0)
                {
                    <button class="c4l-button c4l-form-button c4l-primary-button" type="submit">@Localizer["btn-update"]</button>
                }
                else
                {
                    <button class="c4l-button c4l-form-button c4l-primary-button" type="submit">@Localizer["btn_save"]</button>
                }

                <a href="/organization-list" class="c4l-button c4l-form-button c4l-secondary-button">@Localizer["btn_close"]</a>
            </div>
        </EditForm>
    }
</div>
