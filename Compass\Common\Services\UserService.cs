﻿using Compass.Common.DTOs.Generic;
using Compass.Common.DTOs.User;
using Compass.Common.Helpers;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Interfaces.Services;

namespace Compass.Common.Services
{
    public class UserService : IUserService
    {
        private readonly IUserRepository _userRepository;

        public UserService(IUserRepository userRepository)
        {
            _userRepository = userRepository;
        }

        private KaplanPageable<UserListDisplayDto> CreateUserDisplayPages(long? organizationId, PageQuery pageQuery, List<UserListDisplayDto> userList, int userCount)
        {
            int pageSize = pageQuery.PageSize;

            int maxPages = (int)Math.Ceiling((double)userCount / pageSize);

            KaplanPageable<UserListDisplayDto> pageable = new KaplanPageable<UserListDisplayDto>();
            pageable.PageContent = userList;
            pageable.MaxPages = maxPages;

            return pageable;
        }

        public async Task<KaplanPageable<UserListDisplayDto>> GetUserDisplayPages(UserListAction action)
        {
            long? organizationId = action.OrganizationId;
            PageQuery pageQuery = action.PageQuery;
            List<UserListDisplayDto> userList = await _userRepository.GetUserList(action);
            int userCount = await _userRepository.GetUserCount(organizationId, pageQuery.QueryText);

            KaplanPageable<UserListDisplayDto> pageable = CreateUserDisplayPages(organizationId, pageQuery, userList, userCount);

            return pageable;
        }

        public async Task<KaplanPageable<UserListDisplayDto>> GetOrganizationUserDisplayPages(UserListAction action)
        {
            long? organizationId = action.OrganizationId;
            PageQuery pageQuery = action.PageQuery;
            List<UserListDisplayDto> userList = await _userRepository.GetOrganizationUserList(action);
            int userCount = await _userRepository.GetOrganizationUserCount(organizationId, pageQuery.QueryText);

            KaplanPageable<UserListDisplayDto> pageable = CreateUserDisplayPages(organizationId, pageQuery, userList, userCount);

            return pageable;
        }

        public async Task<KaplanPageable<UserListDisplayDto>> GetEntity1UserDisplayPages(UserListAction action)
        {
            long? organizationId = action.OrganizationId;
            long? entity1Id = action.EntityId;
            PageQuery pageQuery = action.PageQuery;

            List<UserListDisplayDto> userList = await _userRepository.GetEntity1UserList(action);
            int userCount = await _userRepository.GetEntity1UserCount(organizationId, entity1Id, pageQuery.QueryText);

            KaplanPageable<UserListDisplayDto> pageable = CreateUserDisplayPages(organizationId, pageQuery, userList, userCount);

            return pageable;
        }

        public async Task<KaplanPageable<UserListDisplayDto>> GetEntity2UserDisplayPages(UserListAction action)
        {
            long? organizationId = action.OrganizationId;
            long? entity2Id = action.EntityId;
            PageQuery pageQuery = action.PageQuery;

            List<UserListDisplayDto> userList = await _userRepository.GetEntity2UserList(action);
            int userCount = await _userRepository.GetEntity2UserCount(organizationId, entity2Id, pageQuery.QueryText);

            KaplanPageable<UserListDisplayDto> pageable = CreateUserDisplayPages(organizationId, pageQuery, userList, userCount);

            return pageable;
        }

        public async Task<KaplanPageable<UserListDisplayDto>> GetEntity3UserDisplayPages(UserListAction action)
        {
            long? organizationId = action.OrganizationId;
            long? entity3Id = action.EntityId;
            PageQuery pageQuery = action.PageQuery;

            List<UserListDisplayDto> userList = await _userRepository.GetEntity3UserList(action);
            int userCount = await _userRepository.GetEntity3UserCount(organizationId, entity3Id, pageQuery.QueryText);

            KaplanPageable<UserListDisplayDto> pageable = CreateUserDisplayPages(organizationId, pageQuery, userList, userCount);

            return pageable;
        }

        public async Task<KaplanPageable<UserListDisplayDto>> GetSiteUserDisplayPages(UserListAction action)
        {
            long? organizationId = action.OrganizationId;
            long? siteId = action.EntityId;
            PageQuery pageQuery = action.PageQuery;

            List<UserListDisplayDto> userList = await _userRepository.GetSiteUserList(action);
            int userCount = await _userRepository.GetSiteUserCount(organizationId, siteId, pageQuery.QueryText);

            KaplanPageable<UserListDisplayDto> pageable = CreateUserDisplayPages(organizationId, pageQuery, userList, userCount);

            return pageable;
        }

        public async Task<KaplanPageable<UserListDisplayDto>> GetStudentGroupUserDisplayPages(UserListAction action)
        {
            long? organizationId = action.OrganizationId;
            long? studentGroupId = action.EntityId;
            PageQuery pageQuery = action.PageQuery;

            List<UserListDisplayDto> userList = await _userRepository.GetStudentGroupUserList(action);
            int userCount = await _userRepository.GetStudentGroupUserCount(organizationId, studentGroupId, pageQuery.QueryText);

            KaplanPageable<UserListDisplayDto> pageable = CreateUserDisplayPages(organizationId, pageQuery, userList, userCount);

            return pageable;
        }

        public async Task<KaplanPageable<UserListDisplayDto>> GetUserAssignList(UserAssignListAction action)
        {
            long? organizationId = action.OrganizationId;
            long? entityId = action.EntityId;
            int assignLevel = action.AssignLevel;
            PageQuery pageQuery = action.PageQuery;
            List<UserListDisplayDto> userList = await _userRepository.GetUserInviteList(action);
            int userCount = await _userRepository.GetUserInviteCount(organizationId, entityId, assignLevel, pageQuery.QueryText);

            KaplanPageable<UserListDisplayDto> pageable = CreateUserDisplayPages(organizationId, pageQuery, userList, userCount);

            return pageable;
        }

        public async Task<List<UserAssignmentDto>> GetUserAssignments(string userId)
        {
            List<UserAssignmentDto> assignmentList = await _userRepository.GetUserAssignments(userId);
            return assignmentList;
        }
    }
}
