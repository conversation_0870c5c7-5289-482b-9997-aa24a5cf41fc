﻿@using Compass.Common.DTOs.User
@using Compass.Common.Data
@using Compass.Common.Interfaces.Services
@using Compass.Common.Resources
@using Compass.Common.Services
@using Compass.Common.SessionHandlers
@using Compass.Common.Controls.Generic
@using Compass.Common.Pages.Prompts.Generic
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.Extensions.Localization

@attribute [Authorize]
@rendermode @(new InteractiveServerRenderMode(prerender: false))

@inject UserManager<ApplicationUser> UserManager
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject UserSessionService UserSessionService
@inject NavigationManager NavigationManager
@inject CommonSessionDataObserver CommonSessionDataObserver
@inject UserAccessor UserAccessor
@inject IOrganizationService OrganizationService;
@inject IUserService UserService
@inject IStringLocalizer<CommonResource> Localizer
@inject CurrentCultureObserver CurrentLanguageObserver
@inject CultureService CultureService

<h2 class="h3 text-center mb-4">Organization @Localizer["lbl_UserList"]</h2>

<div class="c4l-search-table-wrapper mt-0">
    <SearchComponent SearchText="@searchText" OnSearch="OnSearch" NoSearchResults="@noSearchResults" />

    <LoaderComponent IsLoading="isLoading">
        @if (userResults != null && userResults.Count > 0) 
        {
            <div class="c4l-table-scroll-wrapper">
                <div class="c4l-table-wrapper organization-users-wrapper">
                    <div class="c4l-table-headings-wrapper organization-users-heading-wrapper">
                        <h6 class="c4l-table-heading">@Localizer["lbl_Username"]</h6>
                        <h6 class="c4l-table-heading">@Localizer["lbl_Email"]</h6>
                        <h6 class="c4l-table-heading">@Localizer["lbl_FirstName"]</h6>
                        <h6 class="c4l-table-heading">@Localizer["lbl_LastName"]</h6>
                        <h6 class="c4l-table-heading">@Localizer["lbl_RemoveUser"]</h6>
                    </div>

                    @foreach (UserListDisplayDto user in userResults)
                    {
                        <div class="c4l-table-result-wrapper org-users-result-wrapper">
                            <p class="c4l-table-result-item">@user.UserName</p>
                            <p class="c4l-table-result-item">@user.Email</p>
                            <p class="c4l-table-result-item">@user.FirstName</p>
                            <p class="c4l-table-result-item">@user.LastName</p>
                            <button class="c4l-button c4l-danger-button" @onclick="() => UnAssignUser(user.Id, user.UserName)">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="var(--white)" viewBox="0 0 640 512">
                                    <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L353.3 251.6C407.9 237 448 187.2 448 128C448 57.3 390.7 0 320 0C250.2 0 193.5 55.8 192 125.2L38.8 5.1zM264.3 304.3C170.5 309.4 96 387.2 96 482.3c0 16.4 13.3 29.7 29.7 29.7l388.6 0c3.9 0 7.6-.7 11-2.1l-261-205.6z"/>
                                </svg>
                                @Localizer["lbl_RemoveUser"]
                            </button>
                        </div>
                    }
                </div>
            </div>
        }

        @if(!isLoading && userResults.Count == 0)
        {
            <NoTableDataMessage />
        }
    </LoaderComponent>
</div>

<div class="c4l-pagination-wrapper">
    <div class="c4l-pagination-buttons-wrapper">
        <div class="buttons-wrapper">
            <button
                class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                @onclick="() => OnPreviousClicked()" 
                disabled="@(currentPage <= 1)"
                type="button"
            >
                @Localizer["lbl_Previous"]
            </button>

            <button 
                class="c4l-button c4l-ghost-primary c4l-pagination-button" 
                @onclick="() => OnNextClicked()"
                disabled="@(currentPage >= maxPages)"
                type="button"
            >
                @Localizer["lbl_Next"]
            </button>
        </div>

        <div class="d-flex" style="gap:0.5rem;">
            <button type="button" class="c4l-button c4l-primary-button c4l-pagination-button" @onclick="OnAssignClicked">@Localizer["lbl_AssignUser"]</button>
            <button type="button" class="c4l-button c4l-secondary-button c4l-pagination-button" @onclick="OnInviteClicked">@Localizer["lbl_InviteUser"]</button>
        </div>
    </div>

    <div class="page-count-wrapper font-weight-500">
        <span class="current-page-number">@currentPage</span> @Localizer["lbl_of"] @maxPages
    </div>
</div>

<AssignBox 
    IsVisible="@IsAssignBoxVisible"
    AssignResult="OnAssignResult"
    OrganizationId="currentOrganizationId"
    EntityId="currentOrganizationId"
    AssignLevel="assignLevel" 
    CustomModalClass="organization-assign-modal"
/>

<InviteBox 
    IsVisible="@IsInviteBoxVisible"
    InviteUserResult="OnInviteUserResult"
    OrganizationId="currentOrganizationId"
    CustomModalClass="organization-invite-modal"
/>

<DialogBox 
    Title="Attention"
    Message=@unAssignMessage
    IsVisible="@IsUnAssignDialogVisible"
    DialogResult="OnUnAssignDialogResult" 
    EntityName="@currentOrgName"
/>
