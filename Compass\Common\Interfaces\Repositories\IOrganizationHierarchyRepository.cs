﻿using Compass.Common.Models;
using LanguageExt.Common;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IOrganizationHierarchyRepository
    {
        public Task<OrganizationHierarchy?> GetOrganizationHierarchyAsync(long? organizationId);
        public Task<OrganizationHierarchy> AddOrganizationHierarchyAsync(OrganizationHierarchy organizationHierarchy);
        public Task<OrganizationHierarchy?> UpdateOrganizationHierarchyAsync(long? id, OrganizationHierarchy organizationHierarchy);
        public Task<List<VisibleEntity>> GetVisibleEntities(string? userId, long? organizationId);
    }
}
