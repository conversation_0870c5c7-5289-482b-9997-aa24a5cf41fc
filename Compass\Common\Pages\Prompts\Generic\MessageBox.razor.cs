﻿using Microsoft.AspNetCore.Components;

namespace Compass.Common.Pages.Prompts.Generic
{
    public partial class MessageBox
    {
        [Parameter]
        public string Title { get; set; } = "Message";

        [Parameter]
        public string Message { get; set; } = string.Empty;

        [Parameter]
        public bool IsVisible { get; set; }

        [Parameter]
        public bool IsLocalized { get; set; } = false;

        [Parameter]
        public EventCallback OnClose { get; set; }

        protected async Task Close()
        {
            await OnClose.InvokeAsync();
        }
    }
}
