﻿using Compass.Common.Data;
using Compass.Common.Interfaces.Repositories;
using Compass.Common.Models;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace Compass.Common.Repositories
{
    public class UserEntity1AccessRepository : IUserEntity1AccessRepository
    {
        private readonly IDbContextFactory<ApplicationDbContext> _contextFactory;
        private readonly AuthenticationStateProvider _authenticationStateProvider;

        public UserEntity1AccessRepository(IDbContextFactory<ApplicationDbContext> contextFactory, AuthenticationStateProvider authenticationStateProvider)
        {
            _contextFactory = contextFactory;
            _authenticationStateProvider = authenticationStateProvider;
        }

        public async Task<UserEntity1Access?> GetUserEntity1AccessAsync(long? organizationId, long? entity1Id)
        {
            if (organizationId is null)
            {
                throw new ArgumentNullException(nameof(organizationId));
            }

            if (entity1Id is null)
            {
                throw new ArgumentNullException(nameof(entity1Id));
            }

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                return await _dbContext.UserEntity1Accesses.FirstOrDefaultAsync(o => o.OrganizationId == organizationId && o.Entity1Id == entity1Id);
            }
        }

        public async Task<UserEntity1Access?> AddUserEntity1AccessAsync(UserEntity1Access? userEntity1Access)
        {
            if (userEntity1Access is null)
            {
                throw new ArgumentNullException(nameof(userEntity1Access));
            }

            // Get the authentication state 
            AuthenticationState authState = await _authenticationStateProvider.GetAuthenticationStateAsync();
            ClaimsPrincipal user = authState.User;
            string? userId = user.FindFirstValue(ClaimTypes.NameIdentifier);
            userEntity1Access.ModId = userId;
            userEntity1Access.ModTs = DateTime.Now;

            using (ApplicationDbContext _dbContext = _contextFactory.CreateDbContext())
            {
                await _dbContext.UserEntity1Accesses.AddAsync(userEntity1Access);
                await _dbContext.SaveChangesAsync();
            }

            return userEntity1Access;
        }
    }
}
