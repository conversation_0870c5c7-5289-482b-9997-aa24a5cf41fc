.loader-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  width: clamp(2.5rem, 1.055rem + 6.163vw, 5rem);
  height: clamp(2.5rem, 1.055rem + 6.163vw, 5rem);
  border-radius: 50%;
  border: clamp(0.25rem, 0.069rem + 0.77vw, 0.563rem) solid;
  border-color: hsl(200 40% 90%);
  border-right-color: var(--c4l-secondary-teal);
  animation: loading-spinner 2.5s infinite linear;
}

@keyframes loading-spinner {
  to {
    transform: rotate(1turn);
  }
}
