﻿using Compass.Common.DTOs.Entity3;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IEntity3Repository
    {
        Task<List<Entity3>> GetEntities3Async(long? organizationId, long? entity2Id);
        Task<Entity3> CreateEntity3Async(Entity3 entity);
        Task<Entity3?> GetEntity3Async(long? id);
        Task<Entity3?> UpdateEntity3Async(long? id, Entity3 entity);
        Task<List<Entity3ListDisplayDto>> GetEntity3List(Entity3ListAction action);
        Task<int> GetEntity3Count(long? organizationId, long? entity1Id, long? entity2Id, string queryText);
        Task<bool> DeleteEntity3(long? id);
    }
}
