using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Compass.LAP.Models
{
    [Table("lap_state_map")]
    public class LAPStateMap
    {
        [Key]
        [Column("InstID")]
        public long InstId { get; set; }

        [Column("ItemNo")]
        [StringLength(25)]
        public string? ItemNo { get; set; }

        [Column("DomainSequence")]
        public int? DomainSequence { get; set; }

        [Column("SubscaleSequence")]
        public int? SubscaleSequence { get; set; }

        [Column("ItemSequence")]
        public int? ItemSequence { get; set; }

        [Column("StateNo")]
        [StringLength(25)]
        public string? StateNo { get; set; }

        [Column("StateID")]
        [StringLength(25)]
        public string? StateId { get; set; }

        [Column("StField")]
        [StringLength(100)]
        public string? StField { get; set; }

        [Column("Instrument")]
        public int? Instrument { get; set; }

        [Column("ReportType")]
        [StringLength(25)]
        public string? ReportType { get; set; }

        [Column("Sequence")]
        public int? Sequence { get; set; }

        [Column("Position")]
        public int? Position { get; set; }

        [Column("Indicator")]
        public int? Indicator { get; set; }

        [Column("Description")]
        [StringLength(25)]
        public string? Description { get; set; }

        [Column("ReportName")]
        [StringLength(200)]
        public string? ReportName { get; set; }

        [Column("StartAge")]
        public int? StartAge { get; set; }

        [Column("EndAge")]
        public int? EndAge { get; set; }

        [Column("AgeLevel")]
        [StringLength(25)]
        public string? AgeLevel { get; set; }

        [Column("StateRevisionYr")]
        public int? StateRevisionYr { get; set; }
    }
}