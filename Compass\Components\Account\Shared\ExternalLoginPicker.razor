﻿@using Compass.Common.Data
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity

@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityRedirectManager RedirectManager

<section class="external-logins-section">
    @if (externalLogins.Length == 0)
    {
        <p class="info-message-wrapper text-center mb-0">There are no external authentication services configured. See this <a href="https://go.microsoft.com/fwlink/?LinkID=532715">article about setting up this ASP.NET application to support logging in via external services</a>.
        </p>
    }
    else
    {
        <form class="c4l-form c4l-external-login-form" action="Account/PerformExternalLogin" method="post">
            <AntiforgeryToken />

            <h3 class="c4l-form-heading">Use another service to log in.</h3>
            <input type="hidden" name="ReturnUrl" value="@ReturnUrl" />

            <div class="buttons-wrapper external-logins-wrapper">
                @foreach (var provider in externalLogins)
                {
                    <button type="submit" class="c4l-button c4l-ghost-primary external-login-button" name="provider" value="@provider.Name" title="Log in using your @provider.DisplayName account">
                    @if (provider.Name == "Microsoft")
                    {
                        <svg fill="none" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" height="24" width="24">
                            <path d="m17 17h10v10h-10z" fill="#feba08"/>
                            <path d="m5 17h10v10h-10z" fill="#05a6f0"/>
                            <path d="m17 5h10v10h-10z" fill="#80bc06"/><path d="m5 5h10v10h-10z" fill="#f25325"/>
                        </svg>
                    }
                    else
                    {
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 18 18" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M17.64 9.20455C17.64 8.56636 17.5827 7.95273 17.4764 7.36364H9V10.845H13.8436C13.635 11.97 13.0009 12.9232 12.0477 13.5614V15.8195H14.9564C16.6582 14.2527 17.64 11.9455 17.64 9.20455Z" fill="#4285F4"></path>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.99976 18C11.4298 18 13.467 17.1941 14.9561 15.8195L12.0475 13.5614C11.2416 14.1014 10.2107 14.4205 8.99976 14.4205C6.65567 14.4205 4.67158 12.8373 3.96385 10.71H0.957031V13.0418C2.43794 15.9832 5.48158 18 8.99976 18Z" fill="#34A853"></path>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.96409 10.71C3.78409 10.17 3.68182 9.59318 3.68182 9C3.68182 8.40682 3.78409 7.83 3.96409 7.29V4.95818H0.957273C0.347727 6.17318 0 7.54773 0 9C0 10.4523 0.347727 11.8268 0.957273 13.0418L3.96409 10.71Z" fill="#FBBC05"></path>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.99976 3.57955C10.3211 3.57955 11.5075 4.03364 12.4402 4.92545L15.0216 2.34409C13.4629 0.891818 11.4257 0 8.99976 0C5.48158 0 2.43794 2.01682 0.957031 4.95818L3.96385 7.29C4.67158 5.16273 6.65567 3.57955 8.99976 3.57955Z" fill="#EA4335"></path>
                        </svg>
                    }
                        @provider.DisplayName
                    </button>
                }
            </div>
        </form>
    }
</section>

@code {
    private AuthenticationScheme[] externalLogins = [];

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        externalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).ToArray();
    }
}
