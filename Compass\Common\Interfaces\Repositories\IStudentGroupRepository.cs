﻿using Compass.Common.DTOs.StudentGroup;
using Compass.Common.Helpers;
using Compass.Common.Models;

namespace Compass.Common.Interfaces.Repositories
{
    public interface IStudentGroupRepository
    {
        public Task<List<StudentGroup>?> GetStudentGroupsAsync(long? organizationId, long? siteId);
        public Task<StudentGroup> CreateStudentGroupAsync(StudentGroup studentGroup);
        public Task<StudentGroup?> GetStudentGroupAsync(long? id);
        public Task<StudentGroup?> UpdateStudentGroupAsync(long? id, StudentGroup? studentGroup);
        public Task<List<StudentGroupListDisplayDto>> GetStudentGroupList(StudentGroupListAction action);
        public Task<int> GetStudentGroupCount(long? organizationId, long? siteId, string queryText);
        public Task<bool> DeleteStudentGroup(long? id);
        public Task<List<StudentGroupListDisplayDto>> GetAssignedStudentGroupList(AssignStudentGroupListAction action);
        public Task<int> GetAssignedStudentGroupCount(long? organizationId, long? siteId, string queryText);
        public Task<List<StudentGroupListDisplayDto>> GetUnAssignedStudentGroupList(AssignStudentGroupListAction action);
        public Task<int> GetUnAssignedStudentGroupCount(long? organizationId, long? siteId, string queryText);
    }
}
